
import React, { useState } from 'react';

interface PreferenceFormProps {
  initialPreferences?: {
    dietaryRestrictions: string[];
    spiceLevel: string;
    budgetRange: [number, number];
    favoriteCuisines: string[];
    locationPreferences: string[];
  };
  onSave?: (preferences: any) => void;
}

const PreferenceForm: React.FC<PreferenceFormProps> = ({ initialPreferences, onSave }) => {
  const [dietaryRestrictions, setDietaryRestrictions] = useState(initialPreferences?.dietaryRestrictions || []);
  const [spiceLevel, setSpiceLevel] = useState(initialPreferences?.spiceLevel || 'medium');
  const [budgetMin, setBudgetMin] = useState(initialPreferences?.budgetRange[0] || 500);
  const [budgetMax, setBudgetMax] = useState(initialPreferences?.budgetRange[1] || 2000);
  const [favoriteCuisines, setFavoriteCuisines] = useState(initialPreferences?.favoriteCuisines || []);
  const [locationPreferences, setLocationPreferences] = useState(initialPreferences?.locationPreferences || []);

  const handleDietaryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;
    setDietaryRestrictions((prev) =>
      checked ? [...prev, value] : prev.filter((item) => item !== value)
    );
  };

  const handleCuisineChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;
    setFavoriteCuisines((prev) =>
      checked ? [...prev, value] : prev.filter((item) => item !== value)
    );
  };

  const handleLocationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;
    setLocationPreferences((prev) =>
      checked ? [...prev, value] : prev.filter((item) => item !== value)
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSave) {
      onSave({
        dietaryRestrictions,
        spiceLevel,
        budgetRange: [budgetMin, budgetMax],
        favoriteCuisines,
        locationPreferences,
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow-md">
      <div>
        <h3 className="text-xl font-semibold text-gray-800 mb-3">Dietary Restrictions</h3>
        <div className="flex flex-wrap gap-3">
          {[ 'Halal', 'Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free' ].map((restriction) => (
            <label key={restriction} className="flex items-center space-x-2">
              <input
                type="checkbox"
                value={restriction}
                checked={dietaryRestrictions.includes(restriction)}
                onChange={handleDietaryChange}
                className="form-checkbox text-orange-500 rounded"
              />
              <span>{restriction}</span>
            </label>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-xl font-semibold text-gray-800 mb-3">Spice Level</h3>
        <div className="flex space-x-4">
          {[ 'mild', 'medium', 'hot', 'extra hot' ].map((level) => (
            <label key={level} className="flex items-center space-x-2 capitalize">
              <input
                type="radio"
                name="spiceLevel"
                value={level}
                checked={spiceLevel === level}
                onChange={(e) => setSpiceLevel(e.target.value)}
                className="form-radio text-orange-500"
              />
              <span>{level}</span>
            </label>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-xl font-semibold text-gray-800 mb-3">Budget Range (PKR)</h3>
        <div className="flex items-center space-x-4">
          <input
            type="number"
            value={budgetMin}
            onChange={(e) => setBudgetMin(Number(e.target.value))}
            className="w-24 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            min="0"
          />
          <span>-</span>
          <input
            type="number"
            value={budgetMax}
            onChange={(e) => setBudgetMax(Number(e.target.value))}
            className="w-24 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            min={budgetMin}
          />
        </div>
      </div>

      <div>
        <h3 className="text-xl font-semibold text-gray-800 mb-3">Favorite Cuisines</h3>
        <div className="flex flex-wrap gap-3">
          {[ 'Pakistani', 'Chinese', 'Continental', 'Italian', 'Fast Food', 'BBQ', 'Desserts', 'Thai', 'Japanese' ].map((cuisine) => (
            <label key={cuisine} className="flex items-center space-x-2">
              <input
                type="checkbox"
                value={cuisine}
                checked={favoriteCuisines.includes(cuisine)}
                onChange={handleCuisineChange}
                className="form-checkbox text-orange-500 rounded"
              />
              <span>{cuisine}</span>
            </label>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-xl font-semibold text-gray-800 mb-3">Location Preferences</h3>
        <div className="flex flex-wrap gap-3">
          {[ 'Lahore', 'Karachi', 'Islamabad', 'Rawalpindi', 'Faisalabad', 'Multan' ].map((location) => (
            <label key={location} className="flex items-center space-x-2">
              <input
                type="checkbox"
                value={location}
                checked={locationPreferences.includes(location)}
                onChange={handleLocationChange}
                className="form-checkbox text-orange-500 rounded"
              />
              <span>{location}</span>
            </label>
          ))}
        </div>
      </div>

      <button
        type="submit"
        className="bg-orange-500 text-white px-6 py-3 rounded-md shadow-md hover:bg-orange-600 transition-colors"
      >
        Save Preferences
      </button>
    </form>
  );
};

export default PreferenceForm;
