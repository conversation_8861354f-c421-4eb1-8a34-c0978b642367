@import "tailwindcss";

/* RotiShoti Custom Theme - "Spices & Cream" Palette */
:root {
  /* "Spices & Cream" Color Palette */
  --cream: #FFF8F0;
  --chocolate: #3D2C21;
  --saffron: #FF6B00;
  --paprika: #E67E22;
  --coriander: #27AE60;
  --chili: #C0392B;
  --turmeric: #F39C12;
  --cardamom: #8E44AD;
  --cinnamon: #D35400;

  /* Legacy Colors (for compatibility) */
  --primary-orange: #FF6B00;
  --primary-green: #4A7C59;
  --saffron-yellow: #F39C12;
  --warm-white: #FFF8F0;
  --deep-charcoal: #3D2C21;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--saffron) 0%, var(--paprika) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--paprika) 0%, var(--cinnamon) 100%);
  --gradient-spice: linear-gradient(135deg, var(--saffron) 0%, var(--paprika) 50%, var(--cinnamon) 100%);
  --gradient-cream: linear-gradient(135deg, var(--cream) 0%, #FDF2E9 100%);

  /* Shadows */
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--cream);
  color: var(--chocolate);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* Typography */
.heading-1 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
}

.heading-2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
}

.heading-3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
}

@media (min-width: 768px) {
  .heading-1 { font-size: 3rem; }
  .heading-2 { font-size: 2.25rem; }
  .heading-3 { font-size: 1.5rem; }
}

/* Custom Components */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-soft);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-soft);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.card {
  background: white;
  border-radius: 1rem;
  box-shadow: var(--shadow-soft);
  transition: all 0.3s ease;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-large);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-orange);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #e55a2b;
}

/* Enhanced Animations for Chat-Centric Design */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce-gentle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes chat-bubble-pop {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  80% {
    transform: scale(1.02) translateY(-2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Enhanced Animation Classes */
.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s infinite;
}

.animate-chat-bubble {
  animation: chat-bubble-pop 0.4s ease-out;
}

/* Pakistani Typography */
.font-urdu {
  font-family: 'Noto Nastaliq Urdu', 'Jameel Noori Nastaleeq', serif;
}

/* Glass Morphism Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Chat Scrollbar */
.chat-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #FF6B35, #F7931E);
  border-radius: 10px;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #F7931E, #FF6B35);
}

/* Pakistani Pattern Background */
.pakistani-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(247, 147, 30, 0.1) 0%, transparent 50%);
}

/* Enhanced Card Hover Effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Food Emoji Animations */
.food-emoji {
  display: inline-block;
  transition: transform 0.3s ease;
}

.food-emoji:hover {
  transform: scale(1.2) rotate(5deg);
}

/* Pakistani Button Style */
.btn-pakistani {
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.btn-pakistani:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
}

.btn-pakistani:active {
  transform: translateY(0);
}

/* Loading Spinner with Pakistani Colors */
.spinner-pakistani {
  border: 3px solid rgba(255, 107, 53, 0.3);
  border-top: 3px solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .mobile-padding {
    padding: 1rem;
  }

  .mobile-text-center {
    text-align: center;
  }

  .mobile-chat-fullscreen {
    height: calc(100vh - 80px);
  }

  .mobile-padding-bottom {
    padding-bottom: 100px;
  }
}

/* Modal Styles - Prevent Flickering */
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9999 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 1rem !important;
}

.modal-content {
  position: relative !important;
  background: white !important;
  border-radius: 1rem !important;
  max-width: 56rem !important;
  width: 100% !important;
  max-height: 90vh !important;
  min-height: 500px !important;
  overflow: hidden !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  transform: none !important;
  transition: none !important;
}

.modal-close-btn {
  position: absolute !important;
  top: 16px !important;
  right: 16px !important;
  z-index: 10 !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 50% !important;
  padding: 8px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  transition: background-color 0.2s !important;
}

.modal-close-btn:hover {
  background: rgba(255, 255, 255, 1) !important;
}

/* Leaflet Map Styles */
.leaflet-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.custom-restaurant-marker {
  background: transparent !important;
  border: none !important;
}

.user-location-marker {
  background: transparent !important;
  border: none !important;
}

.custom-popup .leaflet-popup-content-wrapper {
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

.custom-popup .leaflet-popup-content {
  margin: 0 !important;
  font-family: 'Inter', sans-serif !important;
}

.custom-popup .leaflet-popup-tip {
  background: white !important;
  box-shadow: 0 3px 14px rgba(0, 0, 0, 0.15) !important;
}

/* Map Controls */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.leaflet-control-zoom a {
  background: white !important;
  color: #374151 !important;
  border: none !important;
  font-size: 18px !important;
  font-weight: bold !important;
}

.leaflet-control-zoom a:hover {
  background: #f3f4f6 !important;
  color: #111827 !important;
}
