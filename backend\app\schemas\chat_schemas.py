"""
RotiShoti - AI-Powered Food Discovery Platform
Author: <PERSON><PERSON><PERSON>
Chat Schemas - Data models for chat functionality
"""

from pydantic import BaseModel
from typing import Optional, List


class ChatMessage(BaseModel):
    role: str  # 'user' or 'assistant'
    content: str


class UserProfile(BaseModel):
    name: str
    food_personality: str
    favorite_cuisines: List[str]
    spice_level: str
    dining_style: List[str]
    taste_profile: dict
    budget_range: Optional[List[int]] = None  # [min_budget, max_budget]


class ChatRequest(BaseModel):
    user_query: str
    location: Optional[str] = None
    user_location: Optional[dict] = None  # {latitude: float, longitude: float}
    budget: Optional[str] = None
    user_profile: Optional[UserProfile] = None
    conversation_history: Optional[List[ChatMessage]] = []


class RestaurantCard(BaseModel):
    name: str
    area: str
    address: str
    rating: str
    item_name: str
    price: str
    map_embed_url: str


class ChatResponse(BaseModel):
    response: str
    restaurants: Optional[List[RestaurantCard]] = []
    confidence: Optional[float] = 1.0
    fact_checked: Optional[bool] = False
    accuracy_score: Optional[float] = None
    primary_model: Optional[str] = None
    fact_checker_used: Optional[bool] = False
