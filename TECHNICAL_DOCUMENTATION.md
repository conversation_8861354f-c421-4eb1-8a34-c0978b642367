# RotiShoti: AI-Powered Restaurant Recommendation System

## Project Overview

RotiShoti is a sophisticated web application that leverages artificial intelligence to provide personalized restaurant recommendations in Islamabad, Pakistan. The system combines natural language processing, vector similarity search, and real-time chat functionality to deliver an intuitive food discovery experience.

**Authors: <AUTHORS>

## Architecture Overview

### Backend (FastAPI + Python)
- **Framework**: FastAPI with async/await support
- **AI Integration**: Groq LLM for natural language processing
- **Database**: Supabase (PostgreSQL) with vector embeddings
- **Authentication**: Supabase Auth with JWT tokens
- **Data Processing**: Vector similarity search for restaurant matching

### Frontend (Next.js + React)
- **Framework**: Next.js 15 with TypeScript
- **State Management**: Zustand for global state
- **UI Components**: Custom components with Framer Motion animations
- **Styling**: Tailwind CSS with food-themed design

## Development Progress Summary

### Phase 1: Foundation Setup
1. **Project Structure**: Established enterprise-grade folder structure
2. **Backend API**: Created FastAPI application with proper routing
3. **Database Setup**: Configured Supabase with restaurant data
4. **Frontend Framework**: Set up Next.js with TypeScript

### Phase 2: Core Features Implementation
1. **AI Integration**: Implemented Groq LLM for chat responses
2. **Restaurant Database**: Created comprehensive Islamabad restaurant dataset
3. **Vector Search**: Implemented similarity search for recommendations
4. **User Authentication**: Added Supabase auth integration

### Phase 3: Chat System Development
1. **Real-time Chat**: Built responsive chat interface
2. **Message Management**: Implemented conversation history
3. **Restaurant Cards**: Created interactive restaurant display cards
4. **Maps Integration**: Added Google Maps embedding

### Phase 4: Business Logic Separation
1. **Service Layer**: Extracted business logic into dedicated services
2. **Validation Service**: Centralized input validation
3. **Restaurant Service**: Separated restaurant-related operations
4. **Chat Service**: Isolated chat functionality

### Phase 5: Performance Optimization
1. **State Management**: Fixed real-time UI updates
2. **API Optimization**: Reduced response times to ~2 seconds
3. **Error Handling**: Implemented comprehensive error management
4. **Type Safety**: Enhanced TypeScript definitions

## Key Features

### Intelligent Recommendation Engine
- Natural language query processing
- Budget range filtering (₨1000-₨6000+)
- Category-specific search (burgers, steaks, pizza, etc.)
- Location-based recommendations
- User preference learning

### Advanced Chat Interface
- Real-time messaging without page refresh
- Context-aware responses
- Conversation history management
- Interactive restaurant cards with maps
- Mobile-responsive design

### Restaurant Data Management
- Comprehensive Islamabad restaurant database
- Real-time pricing and menu information
- Google Maps integration for locations
- Rating and review aggregation

## Technical Implementation

### Backend Services
```
backend/app/services/
├── ai_service.py          # Groq LLM integration
├── chat_service.py        # Chat processing logic
├── recommendation_service.py # Restaurant matching
├── restaurant_service.py  # Restaurant data operations
├── user_service.py        # User management
└── auth_service.py        # Authentication logic
```

### Frontend Services
```
frontend/src/services/
├── chatService.ts         # Chat API communication
├── restaurantService.ts   # Restaurant operations
├── userService.ts         # User profile management
└── validationService.ts   # Input validation
```

### API Endpoints
- `POST /api/v1/chat/chat` - Main chat endpoint
- `GET /api/v1/restaurants/search` - Restaurant search
- `POST /api/v1/auth/login` - User authentication
- `GET /api/v1/users/profile` - User profile

## Performance Metrics

- **API Response Time**: ~2 seconds average
- **UI Update Speed**: Real-time (no refresh required)
- **Recommendation Accuracy**: High precision with category filtering
- **Mobile Responsiveness**: Fully optimized for all devices

## Security Implementation

- Environment variable management for API keys
- Input validation and sanitization
- Secure authentication with Supabase
- CORS configuration for cross-origin requests
- Error handling without sensitive data exposure

## Testing & Quality Assurance

- Comprehensive API testing suite
- Real-time debugging and logging
- Performance monitoring
- Error tracking and handling

## Deployment Architecture

### Production Environment
- **Frontend**: Vercel deployment with CDN
- **Backend**: Railway/Heroku with auto-scaling
- **Database**: Supabase managed PostgreSQL
- **Monitoring**: Real-time error tracking

### Development Environment
- **Frontend**: Next.js dev server (localhost:3000)
- **Backend**: FastAPI dev server (localhost:8000)
- **Database**: Supabase development instance

## Code Quality Standards

### Enterprise-Grade Structure
- Separation of concerns with service layers
- Comprehensive type definitions
- Consistent naming conventions
- Proper error handling
- Clean, maintainable code

### Best Practices Implemented
- Single Responsibility Principle
- Dependency Injection
- Input validation at all levels
- Comprehensive logging
- Performance optimization

## Future Enhancements

1. **Advanced Filtering**: Dietary restrictions, cuisine preferences
2. **Social Features**: User reviews, ratings, sharing
3. **Analytics**: User behavior tracking, recommendation improvement
4. **Mobile App**: React Native implementation
5. **Multi-city Support**: Expansion beyond Islamabad

## Maintenance & Support

- Regular dependency updates
- Performance monitoring
- User feedback integration
- Continuous improvement based on usage patterns
- Security audits and updates
