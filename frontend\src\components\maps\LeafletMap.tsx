'use client';

import { useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';

// Proper SSR-safe Leaflet implementation
const LeafletMapComponent = dynamic(
  () => import('./LeafletMapCore'),
  { 
    ssr: false,
    loading: () => (
      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-green-50 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading interactive map...</p>
          <p className="text-sm text-gray-500">Preparing restaurant locations</p>
        </div>
      </div>
    )
  }
);

interface Restaurant {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  rating: number;
  cuisine_types: string[];
  price_range_min: number;
  price_range_max: number;
  phone?: string;
  image_url?: string;
  distance?: number;
}

interface LeafletMapProps {
  restaurants: Restaurant[];
  userLocation?: { lat: number; lng: number };
  selectedRestaurant?: Restaurant;
  onRestaurantSelect?: (restaurant: Restaurant) => void;
  height?: string;
  showUserLocation?: boolean;
  maxDistance?: number; // in km
  className?: string;
}

export const LeafletMap: React.FC<LeafletMapProps> = (props) => {
  return (
    <div className={`relative ${props.className || ''}`} style={{ height: props.height || '400px' }}>
      <LeafletMapComponent {...props} />
    </div>
  );
};
