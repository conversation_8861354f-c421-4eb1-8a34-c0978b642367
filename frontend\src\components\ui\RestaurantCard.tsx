
'use client';

import React, { useState } from 'react';

interface Restaurant {
  id: string;
  name: string;
  cuisine_types: string[];
  area: string;
  price_range_average: number;
  rating_overall: number;
  features: string[];
  image?: string;
}

interface RestaurantCardProps {
  restaurant: Restaurant;
  onSave?: (restaurantId: string) => void;
  onVisit?: (restaurantId: string) => void;
}

const RestaurantCard: React.FC<RestaurantCardProps> = ({ restaurant, onSave, onVisit }) => {
  const [isSaved, setIsSaved] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Generate random restaurant images from Unsplash
  const getRestaurantImage = (name: string) => {
    const imageIds = [
      'UxRhrU8fPHQ', // Pakistani food
      'ZuIDLSz3XLg', // Restaurant interior
      'N_Y88TWmGwA', // Food plating
      'jpkfc5_d-DI', // Restaurant ambiance
      'lP5MCM6nZ5A', // Food photography
      'MQUqbmszGGM', // Pakistani cuisine
      'dphM2U1xq0U', // Restaurant dining
      'IGfIGP5ONV0', // Food close-up
    ];
    const randomId = imageIds[Math.abs(name.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % imageIds.length];
    return `https://images.unsplash.com/${randomId}?w=400&h=250&fit=crop`;
  };

  const handleSave = () => {
    setIsSaved(!isSaved);
    onSave?.(restaurant.id);
  };

  const handleVisit = () => {
    onVisit?.(restaurant.id);
  };

  const getPriceLevel = (price: number) => {
    if (price < 500) return { level: '$', color: 'text-green-600' };
    if (price < 1500) return { level: '$$', color: 'text-yellow-600' };
    if (price < 3000) return { level: '$$$', color: 'text-orange-600' };
    return { level: '$$$$', color: 'text-red-600' };
  };

  const priceInfo = getPriceLevel(restaurant.price_range_average);

  return (
    <div className="card group cursor-pointer" onClick={handleVisit}>
      {/* Image */}
      <div className="relative h-48 overflow-hidden">
        {!imageError ? (
          <img
            src={restaurant.image || getRestaurantImage(restaurant.name)}
            alt={restaurant.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-orange-400 to-yellow-500 flex items-center justify-center">
            <span className="text-white text-2xl font-bold">
              {restaurant.name.charAt(0)}
            </span>
          </div>
        )}

        {/* Save Button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleSave();
          }}
          className={`absolute top-3 right-3 w-8 h-8 rounded-full flex items-center justify-center transition-all ${
            isSaved
              ? 'bg-red-500 text-white'
              : 'bg-white/80 text-gray-600 hover:bg-white hover:text-red-500'
          }`}
        >
          <svg className="w-4 h-4" fill={isSaved ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>

        {/* Rating Badge */}
        <div className="absolute top-3 left-3 bg-white/90 px-2 py-1 rounded-full flex items-center space-x-1">
          <svg className="w-3 h-3 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
          <span className="text-xs font-medium text-gray-700">{restaurant.rating_overall}</span>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Header */}
        <div className="flex justify-between items-start mb-2">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 text-lg group-hover:text-orange-600 transition-colors">
              {restaurant.name}
            </h3>
            <p className="text-sm text-gray-500">{restaurant.area}</p>
          </div>
          <div className="flex items-center space-x-1">
            <span className={`text-sm font-bold ${priceInfo.color}`}>
              {priceInfo.level}
            </span>
          </div>
        </div>

        {/* Cuisine Types */}
        <div className="flex flex-wrap gap-1 mb-3">
          {restaurant.cuisine_types.slice(0, 3).map((cuisine, index) => (
            <span
              key={index}
              className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
            >
              {cuisine}
            </span>
          ))}
          {restaurant.cuisine_types.length > 3 && (
            <span className="text-xs text-gray-400">
              +{restaurant.cuisine_types.length - 3} more
            </span>
          )}
        </div>

        {/* Features */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 text-xs text-gray-500">
            {restaurant.features.includes('Delivery') && (
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                  <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z" />
                </svg>
                <span>Delivery</span>
              </div>
            )}
            {restaurant.features.includes('Parking') && (
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm3 2h2a2 2 0 110 4H7V6z" clipRule="evenodd" />
                </svg>
                <span>Parking</span>
              </div>
            )}
          </div>

          {/* Price */}
          <div className="text-right">
            <p className="text-xs text-gray-500">Avg. Price</p>
            <p className="text-sm font-semibold text-gray-900">₨{restaurant.price_range_average}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RestaurantCard;
