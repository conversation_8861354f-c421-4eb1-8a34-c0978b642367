@echo off
REM RotiShoti Repository Initialization Script
REM Author: <PERSON><PERSON><PERSON>
REM This script initializes the git repository and sets up the project

echo 🍽️ Initializing RotiShoti Repository
echo Author: Ma<PERSON><PERSON> Farooque
echo =======================================

REM Initialize git repository if not already initialized
if not exist ".git" (
    echo 📦 Initializing Git repository...
    git init
    echo ✅ Git repository initialized
) else (
    echo 📦 Git repository already exists
)

REM Add all files to git
echo 📁 Adding files to git...
git add .

REM Create initial commit
echo 💾 Creating initial commit...
git commit -m "Initial commit: RotiShoti AI-Powered Food Discovery Platform - Complete FastAPI backend with AI integration - Next.js frontend with mobile-responsive design - User profile system with taste visualizations - Real Islamabad restaurant database - Bilingual chatbot with conversation memory - Professional Pakistani-inspired UI/UX Author: Masab Farooque Repository: https://github.com/Masab12/RotiShoti"

REM Setup instructions
echo.
echo 🌐 Repository Setup Complete!
echo.
echo Next steps:
echo 1. Create a new repository on GitHub: https://github.com/new
echo 2. Name it 'RotiShoti'
echo 3. Run the following commands:
echo.
echo    git remote add origin https://github.com/Masab12/RotiShoti.git
echo    git branch -M main
echo    git push -u origin main
echo.
echo 🚀 Your RotiShoti repository is ready!
echo Built with ❤️ by Masab Farooque

pause
