#!/usr/bin/env python3
"""
Test script to verify vector search functionality
"""

import asyncio
import sys
import os
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from app.services.vector_search_service import VectorSearchService

async def test_vector_search():
    """Test vector search with various queries"""
    print("🔍 Testing Vector Search Functionality...")

    vector_service = VectorSearchService()

    # Test queries with and without budget constraints
    test_cases = [
        {"query": "burger", "budget": None, "description": "General burger search"},
        {"query": "pizza", "budget": None, "description": "General pizza search"},
        {"query": "biryani", "budget": None, "description": "General biryani search"},
        {"query": "chicken karahi", "budget": None, "description": "General karahi search"},
        {"query": "steak", "budget": None, "description": "General steak search"},
        {"query": "dessert", "budget": None, "description": "General dessert search"},
        {"query": "burger", "budget": (200, 500), "description": "Cheap burgers under Rs500"},
        {"query": "pizza", "budget": (300, 800), "description": "Affordable pizza under Rs800"},
        {"query": "food", "budget": (100, 300), "description": "Very cheap food under Rs300"},
        {"query": "steak", "budget": (2000, 10000), "description": "Expensive steaks Rs2000+"},
        {"query": "biryani", "budget": (500, 1000), "description": "Mid-range biryani Rs500-1000"},
    ]

    for test_case in test_cases:
        query = test_case["query"]
        budget = test_case["budget"]
        description = test_case["description"]

        print(f"\n🔎 Testing: {description}")
        print(f"   Query: '{query}' | Budget: {budget}")

        try:
            results = await vector_service.semantic_search_menu_items(
                query=query,
                limit=5,
                similarity_threshold=0.3,
                budget_range=budget,
                city="Islamabad"
            )

            if results:
                print(f"✅ Found {len(results)} results:")
                for i, result in enumerate(results[:3], 1):  # Show top 3
                    price = result.get('price', 0)
                    similarity = result.get('similarity_score', 0)
                    print(f"   {i}. {result.get('restaurant_name')} - {result.get('item_name')}")
                    print(f"      Price: Rs{price} | Similarity: {similarity:.3f}")

                    # Validate budget constraint
                    if budget:
                        min_budget, max_budget = budget
                        if price < min_budget or price > max_budget:
                            print(f"      ⚠️  WARNING: Price Rs{price} is outside budget range Rs{min_budget}-{max_budget}")
            else:
                print("❌ No results found")

        except Exception as e:
            print(f"❌ Error: {e}")

    print("\n🎉 Vector search test completed!")

if __name__ == "__main__":
    asyncio.run(test_vector_search())
