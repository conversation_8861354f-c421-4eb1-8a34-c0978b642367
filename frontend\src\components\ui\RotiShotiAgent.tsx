'use client';

import React, { useState, useEffect } from 'react';

interface RotiShotiAgentProps {
  state?: 'idle' | 'listening' | 'thinking' | 'responding' | 'greeting' | 'excited' | 'searching' | 'recommending';
  size?: 'small' | 'medium' | 'large';
  showName?: boolean;
  personality?: 'friendly' | 'professional' | 'enthusiastic';
}

export const RotiShotiAgent: React.FC<RotiShotiAgentProps> = ({
  state = 'idle',
  size = 'medium',
  showName = true,
  personality = 'friendly'
}) => {
  const [currentState, setCurrentState] = useState(state);
  const [animationPhase, setAnimationPhase] = useState(0);

  useEffect(() => {
    setCurrentState(state);
  }, [state]);

  // Animation cycle for thinking states
  useEffect(() => {
    if (currentState === 'thinking' || currentState === 'searching') {
      const interval = setInterval(() => {
        setAnimationPhase(prev => (prev + 1) % 3);
      }, 800);
      return () => clearInterval(interval);
    }
  }, [currentState]);

  const sizeClasses = {
    small: 'w-12 h-12',
    medium: 'w-16 h-16',
    large: 'w-24 h-24'
  };

  const getAgentEmoji = () => {
    switch (currentState) {
      case 'listening':
        return '👂';
      case 'thinking':
        return ['🤔', '💭', '🧠'][animationPhase];
      case 'searching':
        return ['🔍', '🕵️', '📍'][animationPhase];
      case 'responding':
        return '😊';
      case 'greeting':
        return '👋';
      case 'excited':
        return '🤩';
      case 'recommending':
        return '🎯';
      default:
        return '🍽️';
    }
  };

  const getStateMessage = () => {
    const messages = {
      friendly: {
        listening: 'I\'m all ears! 👂',
        thinking: ['Hmm, let me think...', 'Searching my foodie brain...', 'Almost got it!'][animationPhase],
        searching: ['Looking for tasty spots...', 'Checking the best places...', 'Found some gems!'][animationPhase],
        responding: 'Here are my top picks! 😊',
        greeting: 'Hey there, foodie! 👋',
        excited: 'Ooh, I love this question! 🤩',
        recommending: 'Perfect matches coming up! 🎯',
        idle: 'Ready to find your next craving! 🍽️'
      },
      professional: {
        listening: 'Processing your request...',
        thinking: ['Analyzing options...', 'Evaluating restaurants...', 'Finalizing recommendations...'][animationPhase],
        searching: ['Searching database...', 'Filtering results...', 'Optimizing matches...'][animationPhase],
        responding: 'Here are your personalized recommendations.',
        greeting: 'Welcome to RotiShoti AI Assistant.',
        excited: 'Excellent query! Let me help.',
        recommending: 'Curated recommendations ready.',
        idle: 'How may I assist with your dining needs?'
      },
      enthusiastic: {
        listening: 'Ooh, tell me more! 🎉',
        thinking: ['This is exciting!', 'So many great options!', 'You\'ll love these!'][animationPhase],
        searching: ['Hunting for treasures!', 'Finding food gold!', 'Discovering gems!'][animationPhase],
        responding: 'OMG, you\'re gonna LOVE these! 🔥',
        greeting: 'HELLO AMAZING FOODIE! 🎊',
        excited: 'YES! This is my favorite topic! 🚀',
        recommending: 'BOOM! Perfect matches! 💥',
        idle: 'Ready to blow your mind with food! 🤯'
      }
    };

    return messages[personality][currentState] || messages[personality].idle;
  };

  const getAnimationClass = () => {
    switch (currentState) {
      case 'listening':
        return 'animate-pulse';
      case 'thinking':
        return 'animate-bounce-gentle';
      case 'responding':
        return 'animate-wiggle';
      default:
        return 'animate-pulse-slow';
    }
  };

  return (
    <div className="flex flex-col items-center gap-3">
      {/* Agent Avatar */}
      <div className="relative">
        <div 
          className={`${sizeClasses[size]} bg-spice-gradient rounded-full flex items-center justify-center shadow-lg ${getAnimationClass()}`}
        >
          <span className="text-2xl filter drop-shadow-sm">
            {getAgentEmoji()}
          </span>
        </div>
        
        {/* Status Indicator */}
        {currentState !== 'idle' && (
          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-coriander rounded-full border-2 border-white animate-pulse"></div>
        )}
      </div>

      {/* Agent Name & Status */}
      {showName && (
        <div className="text-center">
          <h3 className="font-heading font-bold text-chocolate text-lg">
            RotiShoti Food AI Agent
          </h3>
          <p className="text-sm text-chocolate/70 font-body">
            {getStateMessage()}
          </p>
        </div>
      )}
    </div>
  );
};

// Enhanced Thinking Animation Component for Chat
interface ThinkingAnimationProps {
  personality?: 'friendly' | 'professional' | 'enthusiastic';
  message?: string;
}

export const ThinkingAnimation: React.FC<ThinkingAnimationProps> = ({
  personality = 'friendly',
  message
}) => {
  const [messageIndex, setMessageIndex] = useState(0);
  const [dots, setDots] = useState('');

  const thinkingMessages = {
    friendly: [
      'Shoti is thinking',
      'Hmm, let me see',
      'Checking my foodie notes',
      'Almost there'
    ],
    professional: [
      'Processing your request',
      'Analyzing options',
      'Evaluating matches',
      'Finalizing results'
    ],
    enthusiastic: [
      'OMG, so many options',
      'This is exciting',
      'Finding the BEST spots',
      'You\'re gonna love this'
    ]
  };

  useEffect(() => {
    const messageInterval = setInterval(() => {
      setMessageIndex(prev => (prev + 1) % thinkingMessages[personality].length);
    }, 2000);

    const dotsInterval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => {
      clearInterval(messageInterval);
      clearInterval(dotsInterval);
    };
  }, [personality]);

  return (
    <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl border border-orange-200">
      <RotiShotiAgent
        state="thinking"
        size="small"
        showName={false}
        personality={personality}
      />
      <div className="flex flex-col">
        <span className="text-gray-800 font-medium">
          {message || thinkingMessages[personality][messageIndex]}{dots}
        </span>
        <span className="text-gray-600 text-sm">
          {personality === 'enthusiastic' ? 'Hunting for food treasures...' :
           personality === 'professional' ? 'Optimizing recommendations...' :
           'Checking menus, prices, and ratings...'}
        </span>
      </div>
    </div>
  );
};

export default RotiShotiAgent;
