import { UserPreferences, UserProfile } from '@/store/userStore';

export class UserService {
  static validateUserProfile(profile: Partial<UserProfile>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!profile.name || profile.name.trim().length === 0) {
      errors.push('Name is required');
    }

    if (!profile.email || !this.isValidEmail(profile.email)) {
      errors.push('Valid email is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static getDefaultPreferences(): UserPreferences {
    return {
      budgetRange: [1000, 3000],
      favoriteCuisines: ['Pakistani'],
      spiceLevel: 'medium',
      diningStyle: ['casual'],
      dietaryRestrictions: []
    };
  }

  static mergePreferences(
    existing: UserPreferences,
    updates: Partial<UserPreferences>
  ): UserPreferences {
    return {
      ...existing,
      ...updates
    };
  }

  static determineFoodPersonality(preferences: UserPreferences): string {
    const { favoriteCuisines, spiceLevel, diningStyle } = preferences;

    if (favoriteCuisines && favoriteCuisines.length > 3) {
      return 'adventurous';
    }

    if (spiceLevel === 'hot' || spiceLevel === 'extra-hot') {
      return 'bold';
    }

    if (diningStyle && diningStyle.includes('fine-dining')) {
      return 'sophisticated';
    }

    return 'casual';
  }

  static getBudgetCategory(budgetRange?: [number, number]): 'low' | 'medium' | 'high' {
    if (!budgetRange) return 'medium';
    
    const [min, max] = budgetRange;
    const average = (min + max) / 2;

    if (average < 1500) return 'low';
    if (average > 3000) return 'high';
    return 'medium';
  }

  static formatBudgetRange(budgetRange: [number, number]): string {
    const [min, max] = budgetRange;
    return `₨${min.toLocaleString('en-PK')} - ₨${max.toLocaleString('en-PK')}`;
  }

  static updateLastActivity(userId: string): void {
    const timestamp = new Date().toISOString();
    localStorage.setItem(`lastActivity_${userId}`, timestamp);
  }

  static getLastActivity(userId: string): Date | null {
    const timestamp = localStorage.getItem(`lastActivity_${userId}`);
    return timestamp ? new Date(timestamp) : null;
  }

  static isActiveUser(userId: string, hoursThreshold: number = 24): boolean {
    const lastActivity = this.getLastActivity(userId);
    if (!lastActivity) return false;

    const now = new Date();
    const hoursDiff = (now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60);
    return hoursDiff <= hoursThreshold;
  }
}
