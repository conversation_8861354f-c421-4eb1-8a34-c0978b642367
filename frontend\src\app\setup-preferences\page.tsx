"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { motion } from 'framer-motion';
import { ChefHat, MapPin, DollarSign, Flame, Utensils, ArrowRight, Star } from 'lucide-react';

const SPICE_LEVELS = [
  { value: 'mild', label: 'Mild', icon: '🌶️', description: 'Just a hint of spice' },
  { value: 'medium', label: 'Medium', icon: '🌶️🌶️', description: 'Perfect balance' },
  { value: 'hot', label: 'Hot', icon: '🌶️🌶️🌶️', description: 'Bring the heat!' },
  { value: 'extra-hot', label: 'Extra Hot', icon: '🌶️🌶️🌶️🌶️', description: 'Fire in your mouth!' }
];

const CUISINES = [
  'Pakistani', 'Indian', 'Chinese', 'Italian', 'Fast Food', 'BBQ', 
  'Continental', 'Mediterranean', 'Thai', 'Japanese', 'Mexican', 'Lebanese'
];

const DINING_STYLES = [
  'Fine Dining', 'Casual Dining', 'Fast Casual', 'Street Food', 
  'Food Trucks', 'Buffet', 'Takeaway', 'Delivery Only'
];

const CITIES = [
  'Islamabad', 'Rawalpindi', 'Lahore', 'Karachi', 'Faisalabad', 
  'Multan', 'Peshawar', 'Quetta', 'Sialkot', 'Gujranwala'
];

export default function SetupPreferences() {
  const router = useRouter();
  const { currentUser, updatePreferences } = useUserStore();
  const [currentStep, setCurrentStep] = useState(1);
  const [preferences, setPreferences] = useState({
    spiceLevel: 'medium' as 'mild' | 'medium' | 'hot' | 'extra-hot',
    budgetRange: [1000, 3000] as [number, number],
    favoriteCuisines: ['Pakistani'],
    diningStyle: ['Casual Dining'],
    locationPreferences: ['Islamabad'],
    tasteProfile: {
      spicy: 50,
      sweet: 50,
      savory: 50,
      adventurous: 50,
      traditional: 50
    }
  });

  useEffect(() => {
    if (!currentUser) {
      router.push('/login');
      return;
    }
    
    // Initialize with current user preferences if they exist
    if (currentUser.preferences) {
      setPreferences({
        spiceLevel: currentUser.preferences.spiceLevel || 'medium',
        budgetRange: currentUser.preferences.budgetRange || [1000, 3000],
        favoriteCuisines: currentUser.preferences.favoriteCuisines || ['Pakistani'],
        diningStyle: currentUser.preferences.diningStyle || ['Casual Dining'],
        locationPreferences: currentUser.preferences.locationPreferences || [currentUser.city || 'Islamabad'],
        tasteProfile: currentUser.tasteProfile || {
          spicy: 50, sweet: 50, savory: 50, adventurous: 50, traditional: 50
        }
      });
    }
  }, [currentUser, router]);

  const handleCuisineToggle = (cuisine: string) => {
    setPreferences(prev => ({
      ...prev,
      favoriteCuisines: prev.favoriteCuisines.includes(cuisine)
        ? prev.favoriteCuisines.filter(c => c !== cuisine)
        : [...prev.favoriteCuisines, cuisine]
    }));
  };

  const handleDiningStyleToggle = (style: string) => {
    setPreferences(prev => ({
      ...prev,
      diningStyle: prev.diningStyle.includes(style)
        ? prev.diningStyle.filter(s => s !== style)
        : [...prev.diningStyle, style]
    }));
  };

  const handleLocationToggle = (location: string) => {
    setPreferences(prev => ({
      ...prev,
      locationPreferences: prev.locationPreferences.includes(location)
        ? prev.locationPreferences.filter(l => l !== location)
        : [...prev.locationPreferences, location]
    }));
  };

  const handleTasteProfileChange = (taste: string, value: number) => {
    setPreferences(prev => ({
      ...prev,
      tasteProfile: {
        ...prev.tasteProfile,
        [taste]: value
      }
    }));
  };

  const handleFinish = async () => {
    try {
      // Get token from cookies
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('token='))
        ?.split('=')[1];

      if (!token) {
        alert('Please login again');
        router.push('/login');
        return;
      }

      // Always update frontend store first (for immediate use)
      updatePreferences(preferences);

      // Set a flag to indicate preferences have been completed
      localStorage.setItem('preferences_completed', 'true');

      // Try to save preferences to backend (optional - won't fail if backend doesn't support it)
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/v1/auth/preferences`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify(preferences),
          }
        );

        if (response.ok) {
          console.log('Preferences saved to backend successfully');
        } else {
          console.log('Backend preferences save failed, but continuing with local storage');
        }
      } catch (error) {
        console.log('Backend preferences save error (continuing with local storage):', error);
      }

      // Always proceed to homepage
      router.push('/');
    } catch (error) {
      console.error('Error saving preferences:', error);
      alert('Failed to save preferences');
    }
  };

  const nextStep = () => setCurrentStep(prev => Math.min(prev + 1, 5));
  const prevStep = () => setCurrentStep(prev => Math.max(prev - 1, 1));

  if (!currentUser) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome, {currentUser.name}! 🎉
          </h1>
          <p className="text-gray-600">
            Let's personalize your food discovery experience
          </p>
          
          {/* Progress Bar */}
          <div className="mt-6 bg-gray-200 rounded-full h-2">
            <div 
              className="bg-orange-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / 5) * 100}%` }}
            />
          </div>
          <p className="text-sm text-gray-500 mt-2">Step {currentStep} of 5</p>
        </div>

        {/* Step Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          className="bg-white rounded-2xl shadow-lg p-8"
        >
          {currentStep === 1 && (
            <div className="text-center">
              <Flame className="w-16 h-16 text-orange-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">How spicy do you like it?</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {SPICE_LEVELS.map((level) => (
                  <button
                    key={level.value}
                    onClick={() => setPreferences(prev => ({ ...prev, spiceLevel: level.value as any }))}
                    className={`p-4 rounded-xl border-2 transition-all ${
                      preferences.spiceLevel === level.value
                        ? 'border-orange-500 bg-orange-50'
                        : 'border-gray-200 hover:border-orange-300'
                    }`}
                  >
                    <div className="text-2xl mb-2">{level.icon}</div>
                    <div className="font-semibold">{level.label}</div>
                    <div className="text-sm text-gray-600">{level.description}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="text-center">
              <DollarSign className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">What's your budget range?</h2>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Budget Range: ₨{preferences.budgetRange[0]} - ₨{preferences.budgetRange[1]}
                  </label>
                  <div className="flex space-x-4">
                    <div className="flex-1">
                      <label className="block text-xs text-gray-500 mb-1">Minimum</label>
                      <input
                        type="range"
                        min="200"
                        max="5000"
                        step="100"
                        value={preferences.budgetRange[0]}
                        onChange={(e) => setPreferences(prev => ({
                          ...prev,
                          budgetRange: [parseInt(e.target.value), prev.budgetRange[1]]
                        }))}
                        className="w-full"
                      />
                    </div>
                    <div className="flex-1">
                      <label className="block text-xs text-gray-500 mb-1">Maximum</label>
                      <input
                        type="range"
                        min="500"
                        max="10000"
                        step="100"
                        value={preferences.budgetRange[1]}
                        onChange={(e) => setPreferences(prev => ({
                          ...prev,
                          budgetRange: [prev.budgetRange[0], parseInt(e.target.value)]
                        }))}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="text-center">
              <ChefHat className="w-16 h-16 text-purple-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">What cuisines do you love?</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {CUISINES.map((cuisine) => (
                  <button
                    key={cuisine}
                    onClick={() => handleCuisineToggle(cuisine)}
                    className={`p-3 rounded-lg border-2 transition-all ${
                      preferences.favoriteCuisines.includes(cuisine)
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-gray-200 hover:border-purple-300'
                    }`}
                  >
                    {cuisine}
                  </button>
                ))}
              </div>
            </div>
          )}

          {currentStep === 4 && (
            <div className="text-center">
              <Utensils className="w-16 h-16 text-blue-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">Preferred dining styles?</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {DINING_STYLES.map((style) => (
                  <button
                    key={style}
                    onClick={() => handleDiningStyleToggle(style)}
                    className={`p-3 rounded-lg border-2 transition-all ${
                      preferences.diningStyle.includes(style)
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-blue-300'
                    }`}
                  >
                    {style}
                  </button>
                ))}
              </div>
            </div>
          )}

          {currentStep === 5 && (
            <div className="text-center">
              <MapPin className="w-16 h-16 text-red-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">Where do you dine?</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {CITIES.map((city) => (
                  <button
                    key={city}
                    onClick={() => handleLocationToggle(city)}
                    className={`p-3 rounded-lg border-2 transition-all ${
                      preferences.locationPreferences.includes(city)
                        ? 'border-red-500 bg-red-50 text-red-700'
                        : 'border-gray-200 hover:border-red-300'
                    }`}
                  >
                    {city}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-between mt-8">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className={`px-6 py-2 rounded-lg ${
                currentStep === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>
            
            {currentStep < 5 ? (
              <button
                onClick={nextStep}
                className="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 flex items-center space-x-2"
              >
                <span>Next</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            ) : (
              <button
                onClick={handleFinish}
                className="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center space-x-2"
              >
                <Star className="w-4 h-4" />
                <span>Start Exploring!</span>
              </button>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
