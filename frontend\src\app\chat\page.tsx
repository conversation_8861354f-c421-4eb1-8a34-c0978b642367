'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import ChatPanel from '@/components/ui/ChatPanel.v3';
import { useUserStore } from '@/store/userStore';
import { useChatStore } from '@/store/chatStore';
import AuthGuard from '@/components/auth/AuthGuard';

export default function ChatPage() {
  return (
    <AuthGuard>
      <ChatPageContent />
    </AuthGuard>
  );
}

function ChatPageContent() {
  const { currentUser } = useUserStore();
  const { setCurrentUser } = useChatStore();
  const searchParams = useSearchParams();
  const [initialQuery, setInitialQuery] = useState('');

  // Set current user in chat store when user changes
  useEffect(() => {
    if (currentUser?.id) {
      setCurrentUser(currentUser.id);
    }
  }, [currentUser?.id, setCurrentUser]);

  // Get initial query from URL params
  useEffect(() => {
    const query = searchParams.get('q');
    if (query) {
      setInitialQuery(decodeURIComponent(query));
    }
  }, [searchParams]);

  const getGreeting = () => {
    const hour = new Date().getHours();
    const name = currentUser?.name || 'Foodie';

    if (hour < 12) return `Good Morning, ${name}!`;
    if (hour < 17) return `Good Afternoon, ${name}!`;
    return `Good Evening, ${name}!`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Chat Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {getGreeting()} 🍽️
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            I'm your AI-powered food guide. Ask me anything about restaurants, cuisines, or food recommendations in Islamabad!
          </p>
          
          {initialQuery && (
            <div className="mt-4 bg-orange-100 border border-orange-200 rounded-xl p-4 max-w-2xl mx-auto">
              <p className="text-orange-800">
                <span className="font-semibold">Starting with your query:</span> "{initialQuery}"
              </p>
            </div>
          )}
        </div>

        {/* Full-Screen Chat Interface */}
        <div className="bg-white rounded-3xl shadow-2xl overflow-hidden" style={{ height: 'calc(100vh - 300px)' }}>
          <ChatPanel 
            isFullscreen={true}
            initialQuery={initialQuery}
            className="h-full"
          />
        </div>

        {/* Quick Tips */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="text-3xl mb-3">🎯</div>
            <h3 className="font-bold text-gray-900 mb-2">Smart Recommendations</h3>
            <p className="text-gray-600 text-sm">
              Get personalized food suggestions based on your preferences, budget, and location.
            </p>
          </div>
          
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="text-3xl mb-3">📍</div>
            <h3 className="font-bold text-gray-900 mb-2">Location-Aware</h3>
            <p className="text-gray-600 text-sm">
              Find restaurants near you with real distances and interactive maps.
            </p>
          </div>
          
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="text-3xl mb-3">💰</div>
            <h3 className="font-bold text-gray-900 mb-2">Budget-Friendly</h3>
            <p className="text-gray-600 text-sm">
              Discover great food options that fit your budget perfectly.
            </p>
          </div>
        </div>

        {/* Sample Queries */}
        <div className="mt-8 bg-white rounded-2xl p-6 shadow-lg">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Try asking me:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              "Find me the best biryani in Islamabad under Rs 1000",
              "What are some good restaurants near F-7 for dinner?",
              "Recommend spicy Pakistani food for lunch",
              "Show me vegetarian options with good ratings",
              "Find restaurants with outdoor seating",
              "What's the closest pizza place to my location?"
            ].map((query, index) => (
              <div
                key={index}
                className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-4 border border-orange-200 hover:border-orange-300 transition-colors cursor-pointer"
                onClick={() => {
                  // You can implement auto-filling the chat with this query
                  const chatInput = document.querySelector('input[placeholder*="food"]') as HTMLInputElement;
                  if (chatInput) {
                    chatInput.value = query;
                    chatInput.focus();
                  }
                }}
              >
                <p className="text-gray-700 text-sm">"{query}"</p>
              </div>
            ))}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
