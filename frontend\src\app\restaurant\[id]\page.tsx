
import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import Header from '@/components/layout/Header';
import MenuItemCard from '@/components/ui/MenuItemCard';

// Dummy data for a single restaurant
const dummyRestaurant = {
  id: '1',
  name: 'The Gourmet Grill',
  cuisine: 'Steakhouse, Continental',
  rating: 4.5,
  priceRange: '$$',
  address: '123 Main St, Gulberg, Lahore',
  phone: '+92 300 1234567',
  website: 'https://www.gourmetgrill.com',
  images: [
    '/images/restaurant1.jpg',
    '/images/restaurant_interior.jpg',
    '/images/restaurant_food.jpg',
  ],
  quickInfo: {
    price: '$$',
    distance: '2.5 km',
    status: 'Open Now',
    features: ['Parking', 'AC', 'WiFi', 'Family-friendly'],
  },
  menu: [
    {
      name: 'Classic Beef Steak',
      description: 'Grilled to perfection with a choice of sauces.',
      price: 1800,
      category: 'Mains',
      spiceLevel: 'Mild',
      imageUrl: '/images/steak.jpg',
    },
    {
      name: 'Chicken Lasagna',
      description: 'Layers of pasta, chicken, and creamy cheese sauce.',
      price: 1200,
      category: 'Mains',
      spiceLevel: 'Mild',
      imageUrl: '/images/lasagna.jpg',
    },
    {
      name: 'Molten Lava Cake',
      description: 'Warm chocolate cake with a gooey center.',
      price: 650,
      category: 'Desserts',
      spiceLevel: 'N/A',
      imageUrl: '/images/lava_cake.jpg',
    },
  ],
  reviews: [
    { user: 'Ali Khan', rating: 5, comment: 'Amazing steak and great ambiance!' },
    { user: 'Sara Ahmed', rating: 4, comment: 'Good food, but a bit pricey.' },
  ],
};

export default function RestaurantDetailPage() {
  // In a real app, you'd fetch the restaurant ID from the URL and then fetch data
  // const router = useRouter();
  // const { id } = router.query;
  // const restaurant = fetchRestaurantData(id);

  const restaurant = dummyRestaurant; // Using dummy data for now

  if (!restaurant) {
    return <div className="text-center py-10">Restaurant not found.</div>;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Header />

      <main className="container mx-auto p-4">
        {/* Hero Section */}
        <section className="relative w-full h-64 md:h-96 rounded-lg overflow-hidden shadow-md mb-6">
          <Image
            src={restaurant.images[0]}
            alt={restaurant.name}
            layout="fill"
            objectFit="cover"
            className="rounded-lg"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-end p-6">
            <div className="text-white">
              <h1 className="text-4xl font-bold mb-2">{restaurant.name}</h1>
              <p className="text-xl">{restaurant.cuisine}</p>
              <div className="flex items-center mt-2">
                <span className="text-yellow-400 text-2xl font-bold mr-2">★</span>
                <span className="text-xl font-semibold">{restaurant.rating.toFixed(1)}</span>
                <span className="text-lg ml-3">{restaurant.priceRange}</span>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Info Bar */}
        <section className="bg-white p-4 rounded-lg shadow-md mb-6 flex flex-wrap justify-around text-center">
          <div className="flex flex-col items-center p-2">
            <span className="font-semibold">Price</span>
            <span className="text-gray-600">{restaurant.quickInfo.price}</span>
          </div>
          <div className="flex flex-col items-center p-2">
            <span className="font-semibold">Distance</span>
            <span className="text-gray-600">{restaurant.quickInfo.distance}</span>
          </div>
          <div className="flex flex-col items-center p-2">
            <span className="font-semibold">Status</span>
            <span className="text-green-600 font-semibold">{restaurant.quickInfo.status}</span>
          </div>
          {restaurant.quickInfo.features.map((feature, index) => (
            <div key={index} className="flex flex-col items-center p-2">
              <span className="font-semibold">{feature}</span>
            </div>
          ))}
        </section>

        {/* Menu Section */}
        <section className="mb-6">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">Menu</h2>
          {/* Category Tabs Placeholder */}
          <div className="flex space-x-4 mb-4 overflow-x-auto pb-2">
            <button className="px-4 py-2 rounded-full bg-orange-500 text-white text-sm">All</button>
            <button className="px-4 py-2 rounded-full bg-gray-200 text-gray-700 text-sm">Mains</button>
            <button className="px-4 py-2 rounded-full bg-gray-200 text-gray-700 text-sm">Appetizers</button>
            <button className="px-4 py-2 rounded-full bg-gray-200 text-gray-700 text-sm">Desserts</button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {restaurant.menu.map((item, index) => (
              <MenuItemCard key={index} {...item} />
            ))}
          </div>
        </section>

        {/* AI Recommendations */}
        <section className="mb-6 bg-orange-100 p-6 rounded-lg shadow-md">
          <h2 className="text-2xl font-bold text-orange-800 mb-4">RotiShoti AI Recommends</h2>
          <p className="text-orange-700 mb-4">
            Based on your taste, try the <strong>Classic Beef Steak</strong> – it's a local favorite!
            Other customers also ordered the <strong>Chicken Lasagna</strong>.
          </p>
          {/* Placeholder for recommended menu items */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* <MenuItemCard ... /> */}
          </div>
        </section>

        {/* Action Buttons */}
        <section className="mb-6 flex flex-wrap justify-center gap-4">
          <button className="bg-blue-500 text-white px-6 py-3 rounded-full shadow-md hover:bg-blue-600 transition-colors">
            Get Directions
          </button>
          <button className="bg-green-500 text-white px-6 py-3 rounded-full shadow-md hover:bg-green-600 transition-colors">
            Call Restaurant
          </button>
          <button className="bg-purple-500 text-white px-6 py-3 rounded-full shadow-md hover:bg-purple-600 transition-colors">
            Visit Website
          </button>
          <button className="bg-gray-500 text-white px-6 py-3 rounded-full shadow-md hover:bg-gray-600 transition-colors">
            Share with Friends
          </button>
        </section>

        {/* Reviews & Ratings */}
        <section>
          <h2 className="text-3xl font-bold text-gray-800 mb-4">Reviews & Ratings</h2>
          <div className="bg-white p-6 rounded-lg shadow-md">
            {restaurant.reviews.length > 0 ? (
              restaurant.reviews.map((review, index) => (
                <div key={index} className="mb-4 pb-4 border-b last:border-b-0">
                  <div className="flex items-center mb-2">
                    <span className="font-semibold mr-2">{review.user}</span>
                    <span className="text-yellow-500">{'★'.repeat(review.rating)}</span>
                  </div>
                  <p className="text-gray-700">{review.comment}</p>
                </div>
              ))
            ) : (
              <p className="text-gray-600">No reviews yet. Be the first to review!</p>
            )}
            {/* Review Form Placeholder */}
            <div className="mt-6">
              <h3 className="text-xl font-semibold mb-3">Write a Review</h3>
              <textarea
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                rows={4}
                placeholder="Share your experience..."
              ></textarea>
              <button className="mt-3 bg-orange-500 text-white px-5 py-2 rounded-md hover:bg-orange-600 transition-colors">
                Submit Review
              </button>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
