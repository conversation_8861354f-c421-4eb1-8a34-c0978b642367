"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { useUserStore } from "@/store/userStore";
import { useChatStore } from "@/store/chatStore";
import ImageUpload from "@/components/ui/ImageUpload";

export default function CompleteProfile() {
  const { data: session } = useSession();
  const router = useRouter();
  const setUser = useUserStore((state) => state.setUser);
  const setCurrentUser = useChatStore((state) => state.setCurrentUser);

  const [form, setForm] = useState({
    name: "",
    email: "",
    phone: "",
    city: "",
    age_group: "",
    dietary_restrictions: [],
    profile_picture: "",
  });

  useEffect(() => {
    if (session) {
      setForm((prev) => ({
        ...prev,
        name: session.user?.name || "",
        email: session.user?.email || "",
        profile_picture: session.user?.image || "",
      }));

      // Store Google profile picture in localStorage for later use
      if (session.user?.image) {
        localStorage.setItem('google_profile_picture', session.user.image);
      }
    }
  }, [session]);

  const handleCheckboxChange = (value: string) => {
    setForm((prev) => {
      const exists = prev.dietary_restrictions.includes(value);
      return {
        ...prev,
        dietary_restrictions: exists
          ? prev.dietary_restrictions.filter((v) => v !== value)
          : [...prev.dietary_restrictions, value],
      };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Store profile picture locally (not sent to backend)
    if (form.profile_picture) {
      localStorage.setItem('user_profile_picture', form.profile_picture);
    }

    // Send form data without profile_picture (database doesn't have this field)
    const { profile_picture, ...profileData } = form;

    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/v1/auth/complete-profile`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(profileData),
      }
    );

    if (res.ok) {
      const data = await res.json();
      document.cookie = `token=${data.access_token}; path=/; Secure`;

      // Fetch user profile to set in store
      try {
        const profileRes = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/v1/auth/profile`,
          {
            headers: {
              Authorization: `Bearer ${data.access_token}`,
            },
          }
        );

        if (profileRes.ok) {
          const user = await profileRes.json();

          // Add profile picture from localStorage
          const storedProfilePicture = localStorage.getItem('user_profile_picture') ||
                                      localStorage.getItem('google_profile_picture');
          if (storedProfilePicture) {
            user.profile_picture = storedProfilePicture;
          }

          // Set user in store
          setUser({ currentUser: user, isAuthenticated: true });

          // Set chat store current user to ensure proper isolation
          setCurrentUser(user.id);
        }
      } catch (error) {
        console.error("Failed to fetch user profile:", error);
      }

      router.push("/setup-preferences");
    } else {
      // Get the actual error message
      try {
        const errorData = await res.json();
        alert(`Failed to complete profile: ${errorData.detail || 'Unknown error'}`);
      } catch (e) {
        alert("Failed to complete profile. Please try again.");
      }
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white mt-10 rounded-xl shadow-md">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">
        Complete Your Profile
      </h2>
      <form onSubmit={handleSubmit} className="space-y-5">
        {/* Profile Picture Section */}
        <div className="text-center">
          <ImageUpload
            currentImage={form.profile_picture}
            onImageChange={(imageUrl) => setForm({ ...form, profile_picture: imageUrl })}
            className="mx-auto"
          />
          <p className="text-sm text-gray-600 mt-2">
            {form.profile_picture ? "Profile Picture" : "Add Profile Picture"}
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email (read-only)
          </label>
          <input
            type="email"
            value={form.email}
            readOnly
            className="w-full p-3 border border-gray-300 rounded bg-gray-100 cursor-not-allowed"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Full Name
          </label>
          <input
            type="text"
            value={form.name}
            onChange={(e) => setForm({ ...form, name: e.target.value })}
            placeholder="Your Name"
            className="w-full p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number
          </label>
          <input
            type="tel"
            value={form.phone}
            onChange={(e) => setForm({ ...form, phone: e.target.value })}
            placeholder="+92xxxxxxxxxx"
            className="w-full p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            City
          </label>
          <input
            type="text"
            value={form.city}
            onChange={(e) => setForm({ ...form, city: e.target.value })}
            placeholder="City (e.g. Islamabad)"
            className="w-full p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Age Group
          </label>
          <select
            value={form.age_group}
            onChange={(e) => setForm({ ...form, age_group: e.target.value })}
            className="w-full p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="">Select Age Group</option>
            <option value="under-18">Under 18</option>
            <option value="18-26">18–26</option>
            <option value="27-35">27–35</option>
            <option value="36-50">36–50</option>
            <option value="50+">50+</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Dietary Restrictions
          </label>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
            {["vegetarian", "vegan", "halal", "gluten-free", "dairy-free"].map(
              (option) => (
                <label key={option} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={form.dietary_restrictions.includes(option)}
                    onChange={() => handleCheckboxChange(option)}
                    className="rounded border-gray-300 text-orange-500 focus:ring-orange-500"
                  />
                  <span className="text-sm capitalize">{option}</span>
                </label>
              )
            )}
          </div>
        </div>

        <button
          type="submit"
          className="w-full bg-orange-500 text-white py-3 px-4 rounded-md hover:bg-orange-600 transition duration-200"
        >
          Complete Profile
        </button>
      </form>
    </div>
  );
}
