from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

class UserBase(BaseModel):
    email: str
    name: str
    phone: Optional[str] = None
    city: Optional[str] = None
    age_group: Optional[str] = None
    dietary_restrictions: Optional[List[str]] = None
    profile_picture: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None

class UserCreate(UserBase):
    password: str

class UserInDB(UserBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    role: str
    _isActive: bool
    password: str

    class Config:
        from_attributes = True

class Location(BaseModel):
    city: str
    area: Optional[str] = None
    address: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None

class MenuItem(BaseModel):
    id: Optional[uuid.UUID] = Field(default_factory=uuid.uuid4)
    restaurant_id: Optional[uuid.UUID] = None
    name: str
    description: Optional[str] = None
    price: int
    category: Optional[str] = None
    spice_level: Optional[str] = None
    is_vegetarian: Optional[bool] = False
    is_available: Optional[bool] = True
    embedding: Optional[List[float]] = None # For semantic search

    class Config:
        from_attributes = True

class Branch(BaseModel):
    branch_id: str
    name: str
    area: str
    address: str
    phone: Optional[str] = None
    coordinates: List[float]  # [latitude, longitude]
    is_main_branch: bool = False
    features: Optional[List[str]] = []

class PriceRange(BaseModel):
    min: int
    max: int
    average: int

class Ratings(BaseModel):
    overall: float
    food_quality: Optional[float] = None
    service: Optional[float] = None
    value: Optional[float] = None

class Ambiance(BaseModel):
    type: str
    noise_level: Optional[str] = None

class RestaurantBase(BaseModel):
    name: str
    city: str
    image_url: Optional[str] = None
    cuisine_types: List[str] = []
    price_range: PriceRange
    branches: List[Branch] = []
    ratings: Ratings
    ambiance: Ambiance

class RestaurantCreate(RestaurantBase):
    pass

class RestaurantInDB(RestaurantBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    menu_items: List[MenuItem] = []

    class Config:
        from_attributes = True

class UserInteraction(BaseModel):
    id: Optional[uuid.UUID] = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID
    restaurant_id: Optional[uuid.UUID] = None
    interaction_type: str # 'view', 'like', 'visit', 'rate'
    query: Optional[str] = None
    rating: Optional[int] = None
    feedback: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        from_attributes = True