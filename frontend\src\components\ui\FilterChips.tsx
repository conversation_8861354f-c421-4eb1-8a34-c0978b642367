
import React from 'react';

interface FilterChipProps {
  label: string;
  isActive?: boolean;
  onClick?: (label: string) => void;
}

const FilterChip: React.FC<FilterChipProps> = ({ label, isActive = false, onClick }) => {
  const activeClasses = isActive ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300';
  return (
    <button
      type="button"
      className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${activeClasses}`}
      onClick={() => onClick?.(label)}
    >
      {label}
    </button>
  );
};

interface FilterChipsProps {
  chips: string[];
  activeChips?: string[];
  onChipClick?: (label: string) => void;
}

const FilterChips: React.FC<FilterChipsProps> = ({ chips, activeChips = [], onChipClick }) => {
  return (
    <div className="flex flex-wrap gap-2">
      {chips.map((chip) => (
        <FilterChip
          key={chip}
          label={chip}
          isActive={activeChips.includes(chip)}
          onClick={onChipClick}
        />
      ))}
    </div>
  );
};

export default FilterChips;
