"""
RotiShoti - AI-Powered Food Discovery Platform
Author: <PERSON><PERSON><PERSON> to regenerate vector embeddings for all menu items
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.db.supabase import create_supabase_client
from app.services.ai_service import ai_service

async def regenerate_all_embeddings():
    """
    Regenerate embeddings for all menu items in the database
    """
    print("🚀 Starting embedding regeneration process...")
    
    # Initialize Supabase client
    supabase = create_supabase_client()
    
    try:
        # Step 1: Get all menu items
        print("📊 Fetching all menu items...")
        response = supabase.from_('menu_items').select("""
            id,
            name,
            description,
            category,
            restaurants!inner(name, cuisine_types)
        """).execute()
        
        menu_items = response.data or []
        print(f"📋 Found {len(menu_items)} menu items to process")
        
        if not menu_items:
            print("❌ No menu items found!")
            return
        
        # Step 2: Process each menu item
        processed = 0
        failed = 0
        
        for item in menu_items:
            try:
                # Create searchable text for embedding (shorter for consistency)
                restaurant_name = item['restaurants']['name']
                cuisine_types = item['restaurants']['cuisine_types'] or []

                # Create concise searchable text to match current AI service embedding dimensions
                searchable_text = f"{item['name']} {item['description'] or ''} {item['category'] or ''} {restaurant_name} {', '.join(cuisine_types) if cuisine_types else ''}".strip()
                
                print(f"🔄 Processing: {item['name']} from {restaurant_name}")
                
                # Generate embedding
                embedding = await ai_service.generate_embedding(searchable_text)
                
                if embedding and len(embedding) > 0:
                    # Update the menu item with new embedding
                    update_response = supabase.from_('menu_items').update({
                        'embedding': embedding,
                        'searchable_text': searchable_text
                    }).eq('id', item['id']).execute()
                    
                    if update_response.data:
                        processed += 1
                        print(f"✅ Updated embedding for: {item['name']}")
                    else:
                        failed += 1
                        print(f"❌ Failed to update: {item['name']}")
                else:
                    failed += 1
                    print(f"❌ Failed to generate embedding for: {item['name']}")
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                failed += 1
                print(f"❌ Error processing {item.get('name', 'Unknown')}: {str(e)}")
                continue
        
        # Step 3: Summary
        print(f"\n🎉 Embedding regeneration complete!")
        print(f"✅ Successfully processed: {processed} items")
        print(f"❌ Failed: {failed} items")
        print(f"📊 Total: {len(menu_items)} items")
        
        if processed > 0:
            print(f"\n🧠 Vector search is now ready with fresh embeddings!")
            print(f"🔍 The system can now perform semantic search on {processed} menu items")
        
    except Exception as e:
        print(f"❌ Critical error during embedding regeneration: {str(e)}")
        return False
    
    return True

async def test_vector_search():
    """
    Test the vector search with a sample query
    """
    print("\n🧪 Testing vector search...")
    
    try:
        from app.services.vector_search_service import vector_search_service
        
        # Test search
        results = await vector_search_service.semantic_search_menu_items(
            query="burgers",
            limit=5,
            similarity_threshold=0.6,
            city="Islamabad"
        )
        
        print(f"🔍 Test search for 'burgers' found {len(results)} results:")
        for result in results[:3]:  # Show top 3
            print(f"  - {result['item_name']} at {result['restaurant_name']} (score: {result['similarity_score']:.3f})")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ Vector search test failed: {str(e)}")
        return False

if __name__ == "__main__":
    async def main():
        print("🍔 RotiShoti Vector Embedding Regeneration")
        print("=" * 50)
        
        # Regenerate embeddings
        success = await regenerate_all_embeddings()
        
        if success:
            # Test the vector search
            await test_vector_search()
            print("\n🚀 Vector search system is now ready!")
        else:
            print("\n❌ Embedding regeneration failed!")
    
    # Run the script
    asyncio.run(main())
