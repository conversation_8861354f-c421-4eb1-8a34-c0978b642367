'use client';

import React, { useState, useEffect } from 'react';

interface Restaurant {
  name: string;
  address: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
}

interface SimpleMapProps {
  restaurant: Restaurant;
  onClose: () => void;
}

export const SimpleMap: React.FC<SimpleMapProps> = ({ restaurant, onClose }) => {
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null);
  const [distance, setDistance] = useState<string>('');

  useEffect(() => {
    // Get user location
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLat = position.coords.latitude;
          const userLng = position.coords.longitude;
          setUserLocation({ lat: userLat, lng: userLng });

          // Calculate distance if restaurant has coordinates
          if (restaurant.latitude && restaurant.longitude) {
            const dist = calculateDistance(
              userLat, userLng, 
              restaurant.latitude, restaurant.longitude
            );
            setDistance(`${dist.toFixed(1)} km away`);
          }
        },
        (error) => {
          console.log('Location access denied:', error);
        }
      );
    }
  }, [restaurant]);

  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Create Google Maps embed URL
  const getMapUrl = () => {
    const query = encodeURIComponent(`${restaurant.name} ${restaurant.address} Islamabad Pakistan`);
    return `https://www.google.com/maps/embed/v1/place?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dw901SwHHqfeE0&q=${query}&zoom=15`;
  };

  const getDirectionsUrl = () => {
    if (restaurant.latitude && restaurant.longitude) {
      return `https://www.google.com/maps/dir/?api=1&destination=${restaurant.latitude},${restaurant.longitude}`;
    }
    const query = encodeURIComponent(`${restaurant.name} ${restaurant.address} Islamabad Pakistan`);
    return `https://www.google.com/maps/dir/?api=1&destination=${query}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[9999] flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-500 to-orange-500 text-white p-6 relative">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-all"
          >
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          <div className="pr-12">
            <h2 className="text-2xl font-bold mb-2">{restaurant.name}</h2>
            <div className="flex items-center gap-4 text-sm opacity-90">
              <span>📍 {restaurant.address}</span>
              {distance && <span>🚗 {distance}</span>}
              {restaurant.phone && <span>📞 {restaurant.phone}</span>}
            </div>
          </div>
        </div>

        {/* Map */}
        <div className="relative h-96">
          <iframe
            src={getMapUrl()}
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            className="w-full h-full"
          />
        </div>

        {/* Actions */}
        <div className="p-6 bg-gray-50 border-t">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900">{restaurant.name}</h3>
              <p className="text-sm text-gray-600">{restaurant.address}</p>
              {distance && (
                <p className="text-sm text-orange-600 font-medium mt-1">
                  {userLocation ? `📍 ${distance} from your location` : 'Location services needed for distance'}
                </p>
              )}
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => window.open(getDirectionsUrl(), '_blank')}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
              >
                <span>🧭</span> Get Directions
              </button>
              
              {restaurant.phone && (
                <button
                  onClick={() => window.open(`tel:${restaurant.phone}`, '_self')}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
                >
                  <span>📞</span> Call Now
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
