
'use client';

import React, { useState, useEffect } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { useUserStore, UserPreferences } from '@/store/userStore';
import AuthGuard from '@/components/auth/AuthGuard';

export default function ProfilePage() {
  const { currentUser, isAuthenticated } = useUserStore();

  // Show loading if not authenticated or no user
  if (!isAuthenticated || !currentUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
            <h2 className="text-3xl font-bold text-gray-900 mb-3">Loading Your Profile...</h2>
            <p className="text-gray-600 text-lg">Preparing your personalized food experience</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return <ProfileContent />;
}

function ProfileContent() {
  const { currentUser, updatePreferences } = useUserStore();
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [editForm, setEditForm] = useState({
    budgetRange: [500, 2000] as [number, number],
    spiceLevel: 'medium' as 'mild' | 'medium' | 'hot' | 'extra-hot',
    favoriteCuisines: ['Pakistani'],
    diningStyle: ['Casual Dining'],
    locationPreferences: ['Islamabad'],
    tasteProfile: {
      spicy: 50,
      sweet: 50,
      savory: 50,
      adventurous: 50,
      traditional: 50
    }
  });

  // Initialize form with current user data
  useEffect(() => {
    if (currentUser) {
      setEditForm({
        budgetRange: currentUser.preferences?.budgetRange || [500, 2000],
        spiceLevel: currentUser.preferences?.spiceLevel || 'medium',
        favoriteCuisines: currentUser.preferences?.favoriteCuisines || ['Pakistani'],
        diningStyle: currentUser.preferences?.diningStyle || ['Casual Dining'],
        locationPreferences: currentUser.preferences?.locationPreferences || ['Islamabad'],
        tasteProfile: currentUser.tasteProfile || {
          spicy: 50,
          sweet: 50,
          savory: 50,
          adventurous: 50,
          traditional: 50
        }
      });
    }
  }, [currentUser]);

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">No Profile Found</h2>
            <p className="text-gray-600">Please select a profile from the header.</p>
          </div>
        </div>
      </div>
    );
  }

  const handleSavePreferences = () => {
    updatePreferences(editForm);
    setIsEditing(false);
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
  };

  const handleCancelEdit = () => {
    // Reset form to current user data
    if (currentUser) {
      setEditForm({
        budgetRange: currentUser.preferences?.budgetRange || [500, 2000],
        spiceLevel: currentUser.preferences?.spiceLevel || 'medium',
        favoriteCuisines: currentUser.preferences?.favoriteCuisines || ['Pakistani'],
        diningStyle: currentUser.preferences?.diningStyle || ['Casual Dining'],
        locationPreferences: currentUser.preferences?.locationPreferences || ['Islamabad'],
        tasteProfile: currentUser.tasteProfile || {
          spicy: 50,
          sweet: 50,
          savory: 50,
          adventurous: 50,
          traditional: 50
        }
      });
    }
    setIsEditing(false);
  };

  // Calculate taste profile percentages for visualization
  const tasteProfileData = [
    { name: 'Spicy', key: 'spicy' as const, value: currentUser?.tasteProfile?.spicy || 50, color: 'bg-red-500' },
    { name: 'Sweet', key: 'sweet' as const, value: currentUser?.tasteProfile?.sweet || 50, color: 'bg-pink-500' },
    { name: 'Savory', key: 'savory' as const, value: currentUser?.tasteProfile?.savory || 50, color: 'bg-yellow-500' },
    { name: 'Adventurous', key: 'adventurous' as const, value: currentUser?.tasteProfile?.adventurous || 50, color: 'bg-purple-500' },
    { name: 'Traditional', key: 'traditional' as const, value: currentUser?.tasteProfile?.traditional || 50, color: 'bg-green-500' },
  ];

  // currentUser is guaranteed to exist here since we check in parent component

  return (
    <div className="min-h-screen bg-cream font-body">
      <Header />

      {/* Success Message */}
      {showSuccessMessage && (
        <div className="fixed top-20 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-xl shadow-lg">
          <div className="flex items-center gap-2">
            <span>✅</span>
            <span>Preferences updated successfully!</span>
          </div>
        </div>
      )}

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Modern Profile Header */}
        <div className="bg-white rounded-3xl shadow-2xl overflow-hidden mb-8">
          <div className="bg-gradient-to-r from-saffron via-paprika to-cinnamon px-8 py-16 relative">
            <div className="absolute inset-0 bg-chocolate bg-opacity-20"></div>
            <div className="relative flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
              <div className="relative">
                {currentUser?.avatar ? (
                  <img
                    src={currentUser.avatar}
                    alt={currentUser?.name || 'User'}
                    className="w-32 h-32 rounded-full border-4 border-white shadow-2xl object-cover"
                  />
                ) : (
                  <div className="w-32 h-32 rounded-full border-4 border-white shadow-2xl bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center">
                    <span className="text-white text-4xl font-bold">
                      {(currentUser?.name || 'U').charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <div className="absolute -bottom-2 -right-2 bg-green-500 w-8 h-8 rounded-full border-4 border-white flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
              </div>
              <div className="text-center md:text-left text-white drop-shadow-lg">
                <h1 className="text-4xl font-heading font-bold mb-3 text-white drop-shadow-md">{currentUser?.name || 'Food Explorer'}</h1>
                <p className="text-xl font-body opacity-90 mb-4 text-white drop-shadow-sm">{currentUser?.foodPersonality || 'Passionate Food Lover'}</p>
                <div className="flex items-center justify-center md:justify-start space-x-6">
                  <div className="bg-white bg-opacity-20 backdrop-blur-sm px-4 py-2 rounded-2xl">
                    <div className="text-2xl font-bold">{currentUser?.streak || 0}</div>
                    <div className="text-sm opacity-90">Day Streak</div>
                  </div>
                  <div className="bg-white bg-opacity-20 backdrop-blur-sm px-4 py-2 rounded-2xl">
                    <div className="text-2xl font-bold">{currentUser?.activityHistory?.length || 0}</div>
                    <div className="text-sm opacity-90">Activities</div>
                  </div>
                  <div className="bg-white bg-opacity-20 backdrop-blur-sm px-4 py-2 rounded-2xl">
                    <div className="text-2xl font-bold">{(currentUser?.achievements || []).length}</div>
                    <div className="text-sm opacity-90">Achievements</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="px-8 py-8 bg-gradient-to-r from-orange-50 to-red-50">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl mb-2">🍽️</div>
                <div className="text-lg font-semibold text-gray-900">Favorite Cuisine</div>
                <div className="text-orange-600">{currentUser?.preferences?.favoriteCuisines?.[0] || 'Pakistani'}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-2">💰</div>
                <div className="text-lg font-semibold text-gray-900">Budget Range</div>
                <div className="text-orange-600">Rs {currentUser?.preferences?.budgetRange?.[0] || 500} - Rs {currentUser?.preferences?.budgetRange?.[1] || 2000}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-2">🌶️</div>
                <div className="text-lg font-semibold text-gray-900">Spice Level</div>
                <div className="text-orange-600 capitalize">{currentUser?.preferences?.spiceLevel || 'Medium'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Tab Navigation */}
        <div className="mb-8">
          <div className="bg-white rounded-2xl shadow-lg p-2">
            <nav className="flex space-x-2">
              {[
                { id: 'overview', name: 'Overview', icon: '📊', desc: 'Your profile summary' },
                { id: 'preferences', name: 'Preferences', icon: '⚙️', desc: 'Food preferences' },
                { id: 'activity', name: 'Activity', icon: '📈', desc: 'Recent activity' },
                { id: 'taste', name: 'Taste Profile', icon: '🎯', desc: 'Flavor preferences' },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 py-4 px-4 rounded-xl font-medium text-sm transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-red-500 to-orange-500 text-white shadow-lg'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-1">{tab.icon}</div>
                    <div className="font-semibold">{tab.name}</div>
                    <div className={`text-xs mt-1 ${activeTab === tab.id ? 'text-white opacity-90' : 'text-gray-500'}`}>
                      {tab.desc}
                    </div>
                  </div>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Quick Stats */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Restaurants Visited</span>
                  <span className="font-semibold">{currentUser.activityHistory?.filter(a => a.type === 'visited').length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Searches Made</span>
                  <span className="font-semibold">{currentUser.activityHistory?.filter(a => a.type === 'search').length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Favorites Saved</span>
                  <span className="font-semibold">{currentUser.activityHistory?.filter(a => a.type === 'saved').length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Reviews Written</span>
                  <span className="font-semibold">{currentUser.activityHistory?.filter(a => a.type === 'review').length || 0}</span>
                </div>
              </div>
            </div>

            {/* Favorite Cuisines */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Favorite Cuisines</h3>
              <div className="space-y-2">
                {(currentUser.preferences?.favoriteCuisines || []).map((cuisine, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-gray-700">{cuisine}</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-orange-500 h-2 rounded-full"
                        style={{ width: `${Math.random() * 60 + 40}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Budget Range */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget Preferences</h3>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-500 mb-2">
                  ₨{currentUser.preferences?.budgetRange?.[0] || 500} - ₨{currentUser.preferences?.budgetRange?.[1] || 2000}
                </div>
                <p className="text-gray-600">Preferred spending range</p>
                <div className="mt-4">
                  <span className="inline-block bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">
                    {currentUser.preferences?.spiceLevel || 'medium'} spice level
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'taste' && (
          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900">Your Taste Profile</h3>
              {!isEditing ? (
                <button
                  onClick={() => setIsEditing(true)}
                  className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
                >
                  Edit Taste Profile
                </button>
              ) : (
                <div className="space-x-2">
                  <button
                    onClick={handleSavePreferences}
                    className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                  >
                    Save Changes
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Taste Sliders */}
              <div className="space-y-6">
                {tasteProfileData.map((item, index) => (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium text-gray-700">{item.name}</span>
                      <span className="text-sm text-gray-500">{isEditing ? editForm.tasteProfile[item.key] : item.value}%</span>
                    </div>
                    {!isEditing ? (
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full ${item.color} transition-all duration-1000 ease-out`}
                          style={{ width: `${item.value}%` }}
                        ></div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={editForm.tasteProfile[item.key]}
                          onChange={(e) => setEditForm({
                            ...editForm,
                            tasteProfile: {
                              ...editForm.tasteProfile,
                              [item.key]: parseInt(e.target.value)
                            }
                          })}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <div
                            className={`h-3 rounded-full ${item.color} transition-all duration-300 ease-out`}
                            style={{ width: `${editForm.tasteProfile[item.key]}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Taste Radar */}
              <div className="flex items-center justify-center">
                <div className="relative w-64 h-64">
                  <svg className="w-full h-full" viewBox="0 0 200 200">
                    {/* Pentagon background */}
                    <polygon
                      points="100,20 180,70 150,150 50,150 20,70"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="2"
                    />
                    <polygon
                      points="100,40 160,80 130,130 70,130 40,80"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="1"
                    />
                    <polygon
                      points="100,60 140,90 110,110 90,110 60,90"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="1"
                    />

                    {/* Data polygon */}
                    <polygon
                      points={`100,${20 + (100 - (isEditing ? editForm.tasteProfile.spicy : currentUser.tasteProfile?.spicy || 50)) * 0.8} ${20 + (isEditing ? editForm.tasteProfile.adventurous : currentUser.tasteProfile?.adventurous || 50) * 1.6},${70 + (100 - (isEditing ? editForm.tasteProfile.adventurous : currentUser.tasteProfile?.adventurous || 50)) * 0.8} ${50 + (isEditing ? editForm.tasteProfile.traditional : currentUser.tasteProfile?.traditional || 50) * 1.0},${150 - (100 - (isEditing ? editForm.tasteProfile.traditional : currentUser.tasteProfile?.traditional || 50)) * 1.0} ${50 + (isEditing ? editForm.tasteProfile.savory : currentUser.tasteProfile?.savory || 50) * 1.0},${150 - (100 - (isEditing ? editForm.tasteProfile.savory : currentUser.tasteProfile?.savory || 50)) * 1.0} ${20 + (isEditing ? editForm.tasteProfile.sweet : currentUser.tasteProfile?.sweet || 50) * 1.6},${70 + (100 - (isEditing ? editForm.tasteProfile.sweet : currentUser.tasteProfile?.sweet || 50)) * 0.8}`}
                      fill="rgba(249, 115, 22, 0.3)"
                      stroke="#f97316"
                      strokeWidth="2"
                    />

                    {/* Labels */}
                    <text x="100" y="15" textAnchor="middle" className="text-xs fill-gray-600">Spicy</text>
                    <text x="185" y="75" textAnchor="start" className="text-xs fill-gray-600">Adventurous</text>
                    <text x="155" y="165" textAnchor="middle" className="text-xs fill-gray-600">Traditional</text>
                    <text x="45" y="165" textAnchor="middle" className="text-xs fill-gray-600">Savory</text>
                    <text x="15" y="75" textAnchor="end" className="text-xs fill-gray-600">Sweet</text>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Recent Activity</h3>
            <div className="space-y-4">
              {(currentUser.activityHistory || []).map((activity, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    activity.type === 'search' ? 'bg-blue-100 text-blue-600' :
                    activity.type === 'visited' ? 'bg-green-100 text-green-600' :
                    activity.type === 'saved' ? 'bg-yellow-100 text-yellow-600' :
                    'bg-purple-100 text-purple-600'
                  }`}>
                    {activity.type === 'search' && '🔍'}
                    {activity.type === 'visited' && '🏪'}
                    {activity.type === 'saved' && '❤️'}
                    {activity.type === 'review' && '⭐'}
                  </div>
                  <div className="flex-1">
                    <p className="text-gray-900 font-medium">
                      {activity.type === 'search' && `Searched for "${activity.query}"`}
                      {activity.type === 'visited' && `Visited ${activity.restaurant}`}
                      {activity.type === 'saved' && `Saved ${activity.restaurant}`}
                      {activity.type === 'review' && `Reviewed ${activity.restaurant}`}
                    </p>
                    <p className="text-sm text-gray-500">{activity.date}</p>
                    {activity.rating && (
                      <div className="flex items-center mt-1">
                        {[...Array(5)].map((_, i) => (
                          <svg key={i} className={`w-4 h-4 ${i < activity.rating! ? 'text-yellow-400' : 'text-gray-300'}`} fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'preferences' && (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Food Preferences</h3>
              {!isEditing ? (
                <button
                  onClick={() => setIsEditing(true)}
                  className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
                >
                  Edit Preferences
                </button>
              ) : (
                <div className="space-x-2">
                  <button
                    onClick={handleSavePreferences}
                    className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                  >
                    Save Changes
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              )}
            </div>

            <div className="space-y-8">
              {/* Budget Range */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Budget Range</h4>
                {!isEditing ? (
                  <div className="text-lg font-semibold text-orange-500">
                    ₨{currentUser.preferences?.budgetRange?.[0] || 500} - ₨{currentUser.preferences?.budgetRange?.[1] || 2000}
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600">₨{editForm.budgetRange[0]}</span>
                      <input
                        type="range"
                        min="100"
                        max="10000"
                        step="100"
                        value={editForm.budgetRange[0]}
                        onChange={(e) => setEditForm({
                          ...editForm,
                          budgetRange: [parseInt(e.target.value), editForm.budgetRange[1]]
                        })}
                        className="flex-1"
                      />
                      <span className="text-sm text-gray-600">₨{editForm.budgetRange[1]}</span>
                      <input
                        type="range"
                        min="500"
                        max="15000"
                        step="100"
                        value={editForm.budgetRange[1]}
                        onChange={(e) => setEditForm({
                          ...editForm,
                          budgetRange: [editForm.budgetRange[0], parseInt(e.target.value)]
                        })}
                        className="flex-1"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Spice Level */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Spice Level</h4>
                {!isEditing ? (
                  <span className="inline-block bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm capitalize">
                    {currentUser.preferences?.spiceLevel || 'medium'}
                  </span>
                ) : (
                  <select
                    value={editForm.spiceLevel}
                    onChange={(e) => setEditForm({ ...editForm, spiceLevel: e.target.value as 'mild' | 'medium' | 'hot' | 'extra-hot' })}
                    className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option value="mild">Mild</option>
                    <option value="medium">Medium</option>
                    <option value="hot">Hot</option>
                    <option value="extra-hot">Extra Hot</option>
                  </select>
                )}
              </div>

              {/* Favorite Cuisines */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Favorite Cuisines</h4>
                {!isEditing ? (
                  <div className="space-y-2">
                    {(currentUser.preferences?.favoriteCuisines || []).map((cuisine, index) => (
                      <span key={index} className="inline-block bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm mr-2 mb-2">
                        {cuisine}
                      </span>
                    ))}
                  </div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {['Pakistani', 'Indian', 'Chinese', 'Italian', 'Turkish', 'Arabic', 'Continental', 'Fast Food', 'BBQ', 'Seafood'].map((cuisine) => (
                      <label key={cuisine} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={editForm.favoriteCuisines.includes(cuisine)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEditForm({
                                ...editForm,
                                favoriteCuisines: [...editForm.favoriteCuisines, cuisine]
                              });
                            } else {
                              setEditForm({
                                ...editForm,
                                favoriteCuisines: editForm.favoriteCuisines.filter(c => c !== cuisine)
                              });
                            }
                          }}
                          className="rounded border-gray-300 text-orange-500 focus:ring-orange-500"
                        />
                        <span className="text-sm">{cuisine}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>

              {/* Dining Style */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Dining Style</h4>
                {!isEditing ? (
                  <div className="space-y-2">
                    {(currentUser.preferences?.diningStyle || []).map((style, index) => (
                      <span key={index} className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm mr-2 mb-2">
                        {style}
                      </span>
                    ))}
                  </div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {['Fine Dining', 'Casual Dining', 'Fast Food', 'Street Food', 'Buffet', 'Takeaway', 'Delivery', 'Food Court'].map((style) => (
                      <label key={style} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={editForm.diningStyle.includes(style)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEditForm({
                                ...editForm,
                                diningStyle: [...editForm.diningStyle, style]
                              });
                            } else {
                              setEditForm({
                                ...editForm,
                                diningStyle: editForm.diningStyle.filter(s => s !== style)
                              });
                            }
                          }}
                          className="rounded border-gray-300 text-blue-500 focus:ring-blue-500"
                        />
                        <span className="text-sm">{style}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>

              {/* Location Preferences */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Preferred Areas</h4>
                {!isEditing ? (
                  <div className="space-y-2">
                    {(currentUser.preferences?.locationPreferences || []).map((location, index) => (
                      <span key={index} className="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm mr-2 mb-2">
                        {location}
                      </span>
                    ))}
                  </div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {['F-6', 'F-7', 'F-8', 'F-10', 'F-11', 'Blue Area', 'Centaurus', 'Gulberg', 'DHA', 'Bahria Town', 'G-9', 'G-10', 'Saddar', 'Rawalpindi'].map((location) => (
                      <label key={location} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={editForm.locationPreferences.includes(location)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEditForm({
                                ...editForm,
                                locationPreferences: [...editForm.locationPreferences, location]
                              });
                            } else {
                              setEditForm({
                                ...editForm,
                                locationPreferences: editForm.locationPreferences.filter(l => l !== location)
                              });
                            }
                          }}
                          className="rounded border-gray-300 text-green-500 focus:ring-green-500"
                        />
                        <span className="text-sm">{location}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
