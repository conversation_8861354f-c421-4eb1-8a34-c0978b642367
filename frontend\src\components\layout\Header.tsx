'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { RealTimeSearch } from '@/components/ui/RealTimeSearch';
import '../../styles/design-system.css';

const Header: React.FC = () => {
  const { currentUser, logout } = useUserStore();
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const router = useRouter();



  const handleLogout = () => {
    logout();
    localStorage.removeItem('access_token');
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    localStorage.removeItem('rotishoti-chat-storage');
    setShowProfileDropdown(false);
    router.push('/login');
  };

  const handleProfileClick = () => {
    setShowProfileDropdown(false);
    router.push('/profile');
  };

  return (
    <header className="bg-white sticky top-0 z-50 shadow-lg border-b border-orange-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo - Clickable */}
          <Link href="/" className="flex items-center gap-3 hover:scale-105 transition-transform duration-200">
            <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-red-500 to-orange-500 flex items-center justify-center shadow-xl">
              <span className="text-white font-bold text-2xl">🍽️</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
                RotiShoti
              </h1>
              <p className="text-sm text-orange-600 font-medium">Your AI Food Companion</p>
            </div>
          </Link>

          {/* Real-Time Search Bar */}
          <div className="flex-1 max-w-2xl mx-8">
            <RealTimeSearch />
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center gap-6">
            <Link href="/restaurants" className="text-gray-700 hover:text-orange-600 font-medium transition-colors duration-200">
              Restaurants
            </Link>
          </div>

          {/* Profile Dropdown */}
          {currentUser ? (
            <div className="relative">
              <button
                onClick={() => setShowProfileDropdown(!showProfileDropdown)}
                className="flex items-center gap-3 p-3 rounded-2xl hover:bg-orange-50 transition-all duration-200 border border-orange-200"
              >
                <img
                  src={currentUser.avatar || '/default-avatar.png'}
                  alt={currentUser.name || 'User'}
                  className="w-10 h-10 rounded-full object-cover ring-2 ring-orange-200"
                />
                <div className="hidden sm:block text-left">
                  <p className="text-sm font-semibold text-gray-900">{currentUser.name}</p>
                  <p className="text-xs text-orange-600">{currentUser.foodPersonality || 'Food Lover'}</p>
                </div>
                <svg
                  className={`w-4 h-4 transition-transform text-orange-400 ${showProfileDropdown ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Dropdown Menu */}
              {showProfileDropdown && (
                <div className="absolute right-0 mt-3 w-64 bg-white rounded-2xl shadow-2xl border border-orange-100 py-2 z-50">
                  <div className="px-4 py-3 border-b border-orange-100">
                    <p className="text-sm font-semibold text-gray-900">Account</p>
                    <p className="text-xs text-orange-600">{currentUser.email}</p>
                  </div>

                  {/* Profile Option */}
                  <button
                    onClick={handleProfileClick}
                    className="w-full flex items-center gap-3 px-4 py-3 hover:bg-orange-50 transition-all text-left"
                  >
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">View Profile</p>
                      <p className="text-xs text-gray-500">Manage preferences & settings</p>
                    </div>
                  </button>

                  {/* Logout Option */}
                  <button
                    onClick={handleLogout}
                    className="w-full flex items-center gap-3 px-4 py-3 hover:bg-red-50 transition-all text-left border-t border-orange-100 mt-2"
                  >
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-red-400 to-red-500 flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">Sign Out</p>
                      <p className="text-xs text-gray-500">Log out of your account</p>
                    </div>
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center gap-4">
              <Link
                href="/login"
                className="text-gray-700 hover:text-orange-600 font-medium transition-colors duration-200"
              >
                Sign In
              </Link>
              <Link
                href="/register"
                className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-6 py-2 rounded-xl hover:from-red-600 hover:to-orange-600 transition-all duration-200 font-medium"
              >
                Sign Up
              </Link>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
