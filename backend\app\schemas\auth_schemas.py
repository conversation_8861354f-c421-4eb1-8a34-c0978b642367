from pydantic import BaseModel, EmailStr
from typing import Optional

class UserRegister(BaseModel):
    email: EmailStr
    password: str
    name: str
    phone: Optional[str] = None
    city: Optional[str] = None
    age_group: Optional[str] = None
    dietary_restrictions: Optional[list[str]] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    email: Optional[str] = None

class UserProfile(BaseModel):
    id: Optional[str] = None
    email: EmailStr
    name: str
    role: str
    phone: Optional[str] = None
    city: Optional[str] = None
    age_group: Optional[str] = None
    dietary_restrictions: Optional[list[str]] = None

    class Config:
        from_attributes = True