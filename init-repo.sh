#!/bin/bash

# RotiShoti Repository Initialization Script
# Author: <PERSON><PERSON><PERSON>
# This script initializes the git repository and sets up the project

echo "🍽️ Initializing RotiShoti Repository"
echo "Author: <PERSON><PERSON><PERSON>"
echo "======================================="

# Initialize git repository if not already initialized
if [ ! -d ".git" ]; then
    echo "📦 Initializing Git repository..."
    git init
    echo "✅ Git repository initialized"
else
    echo "📦 Git repository already exists"
fi

# Add all files to git
echo "📁 Adding files to git..."
git add .

# Create initial commit
echo "💾 Creating initial commit..."
git commit -m "Initial commit: RotiShoti AI-Powered Food Discovery Platform

- Complete FastAPI backend with AI integration
- Next.js frontend with mobile-responsive design
- User profile system with taste visualizations
- Real Islamabad restaurant database
- Bilingual chatbot with conversation memory
- Professional Pakistani-inspired UI/UX

Author: <PERSON><PERSON>b Farooque
Repository: https://github.com/Masab12/RotiShoti"

# Set up remote origin (user needs to create GitHub repo first)
echo ""
echo "🌐 Repository Setup Complete!"
echo ""
echo "Next steps:"
echo "1. Create a new repository on GitHub: https://github.com/new"
echo "2. Name it 'RotiShoti'"
echo "3. Run the following commands:"
echo ""
echo "   git remote add origin https://github.com/Masab12/RotiShoti.git"
echo "   git branch -M main"
echo "   git push -u origin main"
echo ""
echo "🚀 Your RotiShoti repository is ready!"
echo "Built with ❤️ by Masab Farooque"
