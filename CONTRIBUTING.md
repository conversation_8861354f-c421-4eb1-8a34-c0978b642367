# Contributing to RotiShoti

**Author:** <PERSON><PERSON><PERSON>, <PERSON><PERSON>
**Repository:** https://github.com/Masab12/RotiShoti

## Overview

RotiShoti is an AI-powered food discovery platform authored by <PERSON><PERSON><PERSON>. This document outlines the contribution guidelines for the project.

## Project Ownership

This project is authored and maintained by **<PERSON><PERSON><PERSON>**. All contributions should respect the original vision and architecture of the platform.

## Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Python 3.11+
- Supabase account
- Groq API key

### Development Setup

1. **Clone the repository**
```bash
git clone https://github.com/Masab12/RotiShoti.git
cd RotiShoti
```

2. **Install dependencies**
```bash
npm run setup
```

3. **Environment setup**
```bash
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env.local
# Fill in your actual API keys and configuration
```

4. **Run the development servers**
```bash
npm run dev
```

## Code Style Guidelines

### General Principles
- Maintain clean, readable code
- Follow existing patterns and conventions
- Include proper error handling
- Write meaningful commit messages

### Backend (Python/FastAPI)
- Follow PEP 8 style guidelines
- Use type hints for all function parameters and return values
- Include docstrings for all modules, classes, and functions
- Maintain proper separation of concerns

### Frontend (TypeScript/Next.js)
- Use TypeScript for all new components
- Follow React best practices and hooks patterns
- Maintain responsive design principles
- Use Tailwind CSS for styling

### File Headers
All new files should include the following header:
```
/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Author: Masab Farooque
 * [Brief description of the file's purpose]
 */
```

## Contribution Process

### For Bug Reports
1. Check existing issues to avoid duplicates
2. Provide detailed reproduction steps
3. Include environment information
4. Add screenshots if applicable

### For Feature Requests
1. Discuss the feature with the project author first
2. Ensure it aligns with the project's vision
3. Provide detailed use cases and requirements

### For Code Contributions
1. Fork the repository
2. Create a feature branch from `main`
3. Make your changes following the style guidelines
4. Test your changes thoroughly
5. Submit a pull request with detailed description

## Testing

### Backend Testing
```bash
cd backend
python -m pytest
```

### Frontend Testing
```bash
cd frontend
npm run test
```

## Security

- Never commit sensitive information (API keys, passwords, etc.)
- Use environment variables for all configuration
- Follow security best practices for authentication and data handling

## Documentation

- Update README.md for significant changes
- Document new API endpoints
- Include inline comments for complex logic
- Update environment variable examples

## Questions and Support

For questions or support, please contact the project author:
- **GitHub:** [@Masab12](https://github.com/Masab12)
- **Email:** <EMAIL>

## License

By contributing to this project, you agree that your contributions will be licensed under the MIT License.

---

**Thank you for your interest in contributing to RotiShoti!**  
*Built with ❤️ by Masab Farooque*
