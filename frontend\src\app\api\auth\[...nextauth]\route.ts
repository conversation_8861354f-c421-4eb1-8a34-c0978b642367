// src/app/(auth)/[...nextauth]/route.ts
import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import jwt from "jsonwebtoken";
import { NextAuthOptions } from "next-auth";

const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
  },
  jwt: {
    encode: async ({ token, secret }) => {
      // Ensure token is defined before signing
      if (!token) return "";

      return jwt.sign(
        {
          name: token.name,
          email: token.email,
          picture: token.picture,
          isNewUser: token.isNewUser,
        },
        secret,
        { algorithm: "HS256", expiresIn: "1h" }
      );
    },
    decode: async ({ token, secret }) => {
      if (!token) return null;

      try {
        return jwt.verify(token, secret);
      } catch (err) {
        return null;
      }
    },
  },
  callbacks: {
    async jwt({ token, account, profile }) {
      if (account && profile) {
        token.email = profile.email;
        token.name = profile.name;
        token.picture = profile.picture;
        token.isNewUser = true;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user = {
          ...session.user,
          name: token.name,
          email: token.email,
          picture: token.picture,
          isNewUser: token.isNewUser,
        };
      }
      return session;
    },
  },
  pages: {
    signIn: "/auth/signin", // optional
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
