from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional, Dict
from app.schemas.restaurant_schemas import RestaurantResponse, RestaurantCreate, RestaurantSearch, RestaurantNearMe
from app.db.supabase import create_supabase_client
from app.db.models import RestaurantInDB, MenuItem
from app.services.map_service import map_service
from app.services.data_loader_service import data_loader_service

router = APIRouter()

@router.post("/", response_model=RestaurantResponse, status_code=status.HTTP_201_CREATED)
async def create_restaurant(restaurant: RestaurantCreate):
    supabase = create_supabase_client()
    # Insert restaurant data
    restaurant_data = restaurant.model_dump(exclude={'menu_items'})
    response = supabase.from_('restaurants').insert(restaurant_data).execute()
    if not response.data:
        raise HTTPException(status_code=500, detail="Failed to create restaurant")
    
    created_restaurant_id = response.data[0]['id']

    # Insert menu items
    menu_items_data = []
    for item in restaurant.menu_items:
        item_data = item.model_dump()
        item_data['restaurant_id'] = created_restaurant_id
        menu_items_data.append(item_data)
    
    if menu_items_data:
        menu_response = supabase.from_('menu_items').insert(menu_items_data).execute()
        if not menu_response.data:
            # Consider rolling back restaurant creation or logging error
            print(f"Warning: Failed to insert menu items for restaurant {created_restaurant_id}")

    # Fetch the complete created restaurant with menu items
    full_restaurant_response = supabase.from_('restaurants').select('*, menu_items(*)').eq('id', created_restaurant_id).single().execute()
    if not full_restaurant_response.data:
        raise HTTPException(status_code=500, detail="Failed to retrieve created restaurant")
    
    return RestaurantResponse(**full_restaurant_response.data)

@router.get("/")
async def get_all_restaurants(
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0)
):
    supabase = create_supabase_client()
    response = supabase.from_('restaurants').select('*').range(offset, offset + limit - 1).execute()
    if response.data:
        return response.data
    return []

@router.get("/{restaurant_id}")
async def get_restaurant_by_id(restaurant_id: str):
    supabase = create_supabase_client()
    response = supabase.from_('restaurants').select('*, menu_items(*)').eq('id', restaurant_id).single().execute()
    if response.data:
        return response.data
    raise HTTPException(status_code=404, detail="Restaurant not found")

@router.get("/search")
async def search_restaurants(
    query: str,
    city: Optional[str] = None,
    cuisine: Optional[str] = None,
    price_max: Optional[int] = None,
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0)
):
    supabase = create_supabase_client()
    # Basic search by name/cuisine for now. Semantic search will be handled by recommendations.
    search_query = supabase.from_('restaurants').select('*, menu_items(*)')

    if query:
        search_query = search_query.ilike('name', f'%{query}%').ilike('cuisine_types', f'%{query}%') # This ilike on array might not work as expected, consider text search
    if city:
        search_query = search_query.eq('city', city)
    if cuisine:
        search_query = search_query.contains('cuisine_types', [cuisine])
    if price_max:
        search_query = search_query.lte('price_range_average', price_max)
    
    response = search_query.range(offset, offset + limit - 1).execute()
    if response.data:
        return response.data
    return []

@router.get("/near_me", response_model=List[RestaurantResponse])
async def get_restaurants_near_me(
    latitude: float,
    longitude: float,
    radius_km: float = Query(5.0, ge=0.1, le=100.0),
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0)
):
    supabase = create_supabase_client()
    # This requires PostGIS extension and a geography column in Supabase
    # Example using a raw SQL function if available:
    # response = supabase.rpc('get_restaurants_within_radius', {
    #     'lat': latitude, 'lon': longitude, 'radius': radius_km * 1000 # meters
    # }).execute()

    # For now, a simplified approach or placeholder
    # In a real scenario, you'd use a spatial query in Supabase.
    # This is a dummy response.
    print(f"Searching near {latitude}, {longitude} within {radius_km} km")
    response = supabase.from_('restaurants').select('*, menu_items(*)').limit(limit).offset(offset).execute()
    if response.data:
        return [RestaurantResponse(**r) for r in response.data]
    return []

@router.get("/json")
async def get_restaurants_from_json():
    """Get all restaurants from JSON file"""
    try:
        import json
        import os

        json_path = os.path.join(os.path.dirname(__file__), '../../data/islamabad_restaurants.json')
        with open(json_path, 'r', encoding='utf-8') as f:
            restaurants_json = json.load(f)

        # Transform JSON structure to match expected format
        restaurants = []
        for entry in restaurants_json:
            restaurant = entry['restaurant']
            locations = entry['locations']
            menu_items = entry['menu_items']

            # Use the first location for main data
            main_location = locations[0] if locations else {}

            restaurants.append({
                'name': restaurant['name'],
                'description': restaurant['description'],
                'cuisine_types': [restaurant['primary_cuisine']] + restaurant.get('secondary_cuisines', []),
                'area': main_location.get('area', ''),
                'address': main_location.get('address', ''),
                'map_embed_url': main_location.get('map_embed_url', ''),
                'price_range_average': 1000 if restaurant['price_level'] <= 2 else 2000 if restaurant['price_level'] <= 3 else 3000,
                'rating_overall': 4.2,  # Default rating
                'features': ['Dine-in', 'Takeaway'],
                'menu_items': menu_items,
                'locations': locations
            })

        return restaurants
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/seed-database")
async def seed_database():
    """Seed database with latest restaurant data from JSON"""
    try:
        success = await data_loader_service.seed_database()
        if success:
            return {"message": "Database seeded successfully", "status": "success"}
        else:
            raise HTTPException(status_code=500, detail="Failed to seed database")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error seeding database: {str(e)}")

@router.get("/latest")
async def get_restaurants_latest():
    """Get restaurants from latest JSON structure"""
    try:
        restaurants_data = data_loader_service.load_restaurants_from_json()
        return {
            "count": len(restaurants_data),
            "restaurants": restaurants_data[:5],  # Return first 5 for preview
            "message": "Latest restaurant data loaded successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/restaurant-maps", response_model=Dict[str, str])
async def get_restaurant_maps():
    """Get restaurant name to map URL mapping from map service"""
    try:
        # Get all predefined map URLs from map service
        map_urls = map_service.get_all_restaurant_map_urls()

        # Also get restaurants from database and generate URLs for any missing ones
        supabase = create_supabase_client()
        response = supabase.from_("restaurants").select("name, latitude, longitude").execute()

        if response.data:
            for restaurant in response.data:
                restaurant_name = restaurant.get('name', '')
                if restaurant_name and restaurant_name not in map_urls:
                    # Generate map URL for restaurants not in predefined list
                    map_url = map_service.get_restaurant_map_url(
                        restaurant_name,
                        restaurant.get('latitude'),
                        restaurant.get('longitude')
                    )
                    map_urls[restaurant_name] = map_url

        return map_urls
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/search")
async def search_restaurants(
    q: Optional[str] = Query(None, description="Search query for restaurant name, area, or cuisine"),
    name: Optional[str] = Query(None, description="Restaurant name to search for"),
    area: Optional[str] = Query(None, description="Area to search in"),
    cuisine: Optional[str] = Query(None, description="Cuisine type"),
    limit: int = Query(10, description="Number of results to return")
):
    """Search restaurants by name, area, or cuisine"""
    try:
        supabase = create_supabase_client()

        # Build query
        query = supabase.from_('restaurants').select("""
            id, restaurant_id, name, city, image_url, cuisine_types, area, address, phone,
            latitude, longitude, price_range_min, price_range_max, price_range_average,
            rating_overall, rating_food, rating_service, rating_value,
            ambiance_type, noise_level, features
        """)

        # Apply filters - prioritize 'q' parameter for general search
        search_term = q or name
        if search_term:
            # Search in name, area, and cuisine types
            query = query.or_(f'name.ilike.%{search_term}%,area.ilike.%{search_term}%')

        if area and not q:  # Only apply area filter if not using general search
            query = query.ilike('area', f'%{area}%')
        if cuisine and not q:  # Only apply cuisine filter if not using general search
            query = query.contains('cuisine_types', [cuisine])

        # Order by rating for better results
        query = query.order('rating_overall', desc=True)

        # Execute query
        response = query.limit(limit).execute()

        return response.data or []

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching restaurants: {str(e)}")

@router.get("/{restaurant_id}/menu")
async def get_restaurant_menu(restaurant_id: str):
    """Get menu items for a specific restaurant"""
    try:
        supabase = create_supabase_client()

        # Get menu items for the restaurant
        response = supabase.from_('menu_items').select("""
            id, name, description, price, category, spice_level,
            is_vegetarian, is_available
        """).eq('restaurant_id', restaurant_id).execute()

        return response.data or []

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching menu: {str(e)}")

@router.get("/{restaurant_id}/branches")
async def get_restaurant_branches(restaurant_id: str):
    """Get all branches for a specific restaurant"""
    try:
        supabase = create_supabase_client()

        # Get branches for the restaurant
        response = supabase.from_('restaurant_branches').select("""
            branch_id, name, area, address, phone, latitude, longitude,
            is_main_branch, features
        """).eq('restaurant_id', restaurant_id).execute()

        return response.data or []

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching branches: {str(e)}")

@router.get("/{restaurant_id}")
async def get_restaurant_details(restaurant_id: str):
    """Get complete details for a specific restaurant including menu and branches"""
    try:
        supabase = create_supabase_client()

        # Get restaurant details
        restaurant_response = supabase.from_('restaurants').select("""
            id, restaurant_id, name, city, image_url, cuisine_types, area, address, phone,
            latitude, longitude, price_range_min, price_range_max, price_range_average,
            rating_overall, rating_food, rating_service, rating_value,
            ambiance_type, noise_level, features
        """).eq('id', restaurant_id).execute()

        if not restaurant_response.data:
            raise HTTPException(status_code=404, detail="Restaurant not found")

        restaurant = restaurant_response.data[0]

        # Get menu items
        menu_response = supabase.from_('menu_items').select("""
            id, name, description, price, category, spice_level,
            is_vegetarian, is_available
        """).eq('restaurant_id', restaurant_id).execute()

        # Get branches
        branches_response = supabase.from_('restaurant_branches').select("""
            branch_id, name, area, address, phone, latitude, longitude,
            is_main_branch, features
        """).eq('restaurant_id', restaurant_id).execute()

        # Combine all data
        restaurant['menu_items'] = menu_response.data or []
        restaurant['branches'] = branches_response.data or []

        return restaurant

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching restaurant details: {str(e)}")