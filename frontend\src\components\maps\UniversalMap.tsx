'use client';

import React, { useState, useEffect } from 'react';

interface Restaurant {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  rating: number;
  cuisine_types: string[];
  price_range_min: number;
  price_range_max: number;
  phone?: string;
  image_url?: string;
  distance?: number;
}

interface UniversalMapProps {
  restaurant: Restaurant;
  allRestaurants?: Restaurant[];
  onClose: () => void;
  onRestaurantSelect?: (restaurant: Restaurant) => void;
}

type MapProvider = 'openstreetmap' | 'google' | 'mapbox' | 'bing';

export const UniversalMap: React.FC<UniversalMapProps> = ({
  restaurant,
  allRestaurants = [],
  onClose,
  onRestaurantSelect
}) => {
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null);
  const [selectedRestaurant, setSelectedRestaurant] = useState<Restaurant>(restaurant);
  const [isVisible, setIsVisible] = useState(false);
  const [mapProvider, setMapProvider] = useState<MapProvider>('openstreetmap');
  const [locationPermission, setLocationPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');
  const [nearbyRestaurants, setNearbyRestaurants] = useState<Restaurant[]>([]);

  // Show modal after mount
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Get user location
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLoc = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          setUserLocation(userLoc);
          setLocationPermission('granted');
          
          // Calculate distances and filter nearby restaurants
          const restaurantsWithDistance = [restaurant, ...allRestaurants]
            .filter(r => r.latitude && r.longitude)
            .map(r => {
              const distance = calculateDistance(userLoc.lat, userLoc.lng, r.latitude, r.longitude);
              return {
                ...r,
                distance
              };
            })
            .filter(r => r.distance <= 15) // Within 15km
            .sort((a, b) => a.distance - b.distance);

          setNearbyRestaurants(restaurantsWithDistance);
        },
        (error) => {
          console.log('Location access denied:', error);
          setLocationPermission('denied');
          // Still show restaurants without distance
          setNearbyRestaurants([restaurant, ...allRestaurants].filter(r => r.latitude && r.longitude));
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    }
  }, [restaurant, allRestaurants]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  // Prevent body scroll
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => onClose(), 150);
  };

  const handleRestaurantSelect = (selectedRest: Restaurant) => {
    setSelectedRestaurant(selectedRest);
    if (onRestaurantSelect) {
      onRestaurantSelect(selectedRest);
    }
  };

  // Generate map URLs for different providers
  const getMapUrls = () => {
    const lat = selectedRestaurant.latitude;
    const lng = selectedRestaurant.longitude;
    const zoom = 15;
    
    return {
      openstreetmap: `https://www.openstreetmap.org/export/embed.html?bbox=${lng-0.01},${lat-0.01},${lng+0.01},${lat+0.01}&layer=mapnik&marker=${lat},${lng}`,
      google: `https://www.google.com/maps/embed/v1/place?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dw901SwHHqfeE0&q=${lat},${lng}&zoom=${zoom}`,
      mapbox: `https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/pin-s-restaurant+ff6b35(${lng},${lat})/${lng},${lat},${zoom}/600x400@2x?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw`,
      bing: `https://dev.virtualearth.net/REST/v1/Imagery/Map/Road/${lat},${lng}/${zoom}?mapSize=600,400&pp=${lat},${lng};66&key=YOUR_BING_KEY`
    };
  };

  const mapUrls = getMapUrls();

  return (
    <div 
      className={`fixed inset-0 bg-black transition-opacity duration-300 z-[9999] flex items-center justify-center p-4 ${
        isVisible ? 'bg-opacity-50' : 'bg-opacity-0'
      }`}
      onClick={handleClose}
    >
      <div 
        className={`bg-white rounded-3xl max-w-7xl w-full max-h-[95vh] overflow-hidden shadow-2xl transform transition-all duration-300 ${
          isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-red-500 via-orange-500 to-yellow-500 text-white p-6 relative">
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-3 transition-all duration-200 hover:scale-110"
          >
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          <div className="pr-16">
            <h2 className="text-3xl font-bold mb-3">{selectedRestaurant.name}</h2>
            <div className="flex items-center gap-6 text-lg opacity-90 flex-wrap">
              <span>⭐ {selectedRestaurant.rating}/5</span>
              <span>📍 {selectedRestaurant.address}</span>
              {selectedRestaurant.distance && (
                <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                  🚗 {selectedRestaurant.distance.toFixed(1)} km away
                </span>
              )}
              {selectedRestaurant.phone && (
                <span>📞 {selectedRestaurant.phone}</span>
              )}
            </div>
          </div>
        </div>

        {/* Map Provider Selector */}
        <div className="bg-gray-50 px-6 py-3 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-700">Map Provider:</span>
              <div className="flex gap-2">
                {[
                  { id: 'openstreetmap', name: 'OpenStreetMap', icon: '🗺️' },
                  { id: 'google', name: 'Google Maps', icon: '🌍' },
                  { id: 'mapbox', name: 'Mapbox', icon: '📍' }
                ].map((provider) => (
                  <button
                    key={provider.id}
                    onClick={() => setMapProvider(provider.id as MapProvider)}
                    className={`px-3 py-1 rounded-lg text-sm font-medium transition-all ${
                      mapProvider === provider.id
                        ? 'bg-orange-500 text-white'
                        : 'bg-white text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {provider.icon} {provider.name}
                  </button>
                ))}
              </div>
            </div>
            
            {userLocation && (
              <div className="text-sm text-gray-600">
                📍 Your location detected • {nearbyRestaurants.length} restaurants within 15km
              </div>
            )}
          </div>
        </div>

        {/* Location Permission Banner */}
        {locationPermission === 'denied' && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-yellow-400 text-xl">⚠️</span>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>Location access denied.</strong> Enable location services to see distances and get personalized recommendations.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Map Container */}
        <div className="flex-1 relative bg-gray-100" style={{ height: 'calc(95vh - 300px)' }}>
          {mapProvider === 'openstreetmap' && (
            <iframe
              src={mapUrls.openstreetmap}
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              title={`Map showing ${selectedRestaurant.name}`}
              className="w-full h-full"
            />
          )}
          
          {mapProvider === 'google' && (
            <iframe
              src={mapUrls.google}
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title={`Google Map showing ${selectedRestaurant.name}`}
              className="w-full h-full"
            />
          )}
          
          {mapProvider === 'mapbox' && (
            <div className="w-full h-full flex items-center justify-center bg-gray-100">
              <img
                src={mapUrls.mapbox}
                alt={`Mapbox map showing ${selectedRestaurant.name}`}
                className="max-w-full max-h-full object-contain rounded-lg"
                onError={() => {
                  // Fallback to OpenStreetMap if Mapbox fails
                  setMapProvider('openstreetmap');
                }}
              />
            </div>
          )}

          {/* Map Controls */}
          <div className="absolute top-4 right-4 z-10 flex flex-col gap-2">
            <button
              onClick={() => {
                const url = `https://www.google.com/maps/dir/?api=1&destination=${selectedRestaurant.latitude},${selectedRestaurant.longitude}`;
                window.open(url, '_blank');
              }}
              className="bg-white hover:bg-gray-50 p-3 rounded-lg shadow-lg border border-gray-200 transition-all duration-200 hover:scale-105"
              title="Get Directions"
            >
              <span className="text-lg">🧭</span>
            </button>
            
            <button
              onClick={() => {
                const url = `https://www.google.com/maps/search/?api=1&query=${selectedRestaurant.latitude},${selectedRestaurant.longitude}`;
                window.open(url, '_blank');
              }}
              className="bg-white hover:bg-gray-50 p-3 rounded-lg shadow-lg border border-gray-200 transition-all duration-200 hover:scale-105"
              title="Open in Google Maps"
            >
              <span className="text-lg">🌍</span>
            </button>
          </div>
        </div>

        {/* Nearby Restaurants Sidebar */}
        {nearbyRestaurants.length > 1 && (
          <div className="absolute left-4 top-32 bottom-32 w-80 bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden z-10">
            <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white p-4">
              <h3 className="font-bold text-lg">Nearby Restaurants</h3>
              <p className="text-sm opacity-90">Within 15km of your location</p>
            </div>
            
            <div className="overflow-y-auto h-full pb-4">
              {nearbyRestaurants.slice(0, 10).map((rest, index) => (
                <div
                  key={rest.id}
                  onClick={() => handleRestaurantSelect(rest)}
                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-orange-50 transition-colors ${
                    selectedRestaurant.id === rest.id ? 'bg-orange-100 border-l-4 border-l-orange-500' : ''
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                      index === 0 ? 'bg-green-500' : 'bg-orange-500'
                    }`}>
                      {index + 1}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-900 truncate">{rest.name}</h4>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <span>⭐ {rest.rating}</span>
                        {rest.distance && (
                          <span className="text-green-600 font-medium">
                            {rest.distance.toFixed(1)} km
                          </span>
                        )}
                      </div>
                      <p className="text-xs text-gray-500 truncate">{rest.cuisine_types.join(', ')}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Bottom Info Panel */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 p-6 border-t">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div>
                <h3 className="text-xl font-bold text-gray-900">{selectedRestaurant.name}</h3>
                <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                  <span>{selectedRestaurant.cuisine_types.join(', ')}</span>
                  <span>•</span>
                  <span>Rs {selectedRestaurant.price_range_min} - Rs {selectedRestaurant.price_range_max}</span>
                  {selectedRestaurant.distance && (
                    <>
                      <span>•</span>
                      <span className="text-green-600 font-medium">
                        {selectedRestaurant.distance.toFixed(1)} km away
                      </span>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => {
                  const url = `https://www.google.com/maps/dir/?api=1&destination=${selectedRestaurant.latitude},${selectedRestaurant.longitude}`;
                  window.open(url, '_blank');
                }}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 flex items-center gap-2"
              >
                <span>🧭</span> Get Directions
              </button>
              
              {selectedRestaurant.phone && (
                <button
                  onClick={() => window.open(`tel:${selectedRestaurant.phone}`, '_self')}
                  className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 flex items-center gap-2"
                >
                  <span>📞</span> Call Now
                </button>
              )}

              <button
                onClick={() => {
                  if (onRestaurantSelect) {
                    onRestaurantSelect(selectedRestaurant);
                  }
                  handleClose();
                }}
                className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 flex items-center gap-2"
              >
                <span>📋</span> View Menu
              </button>
            </div>
          </div>

          {/* Distance Stats */}
          {userLocation && nearbyRestaurants.length > 1 && (
            <div className="mt-4 pt-4 border-t border-orange-200">
              <div className="grid grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {nearbyRestaurants.filter(r => r.distance && r.distance <= 2).length}
                  </div>
                  <div className="text-sm text-gray-600">Within 2km</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {nearbyRestaurants.filter(r => r.distance && r.distance <= 5).length}
                  </div>
                  <div className="text-sm text-gray-600">Within 5km</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {nearbyRestaurants.filter(r => r.distance && r.distance <= 10).length}
                  </div>
                  <div className="text-sm text-gray-600">Within 10km</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {nearbyRestaurants[0]?.distance?.toFixed(1) || 'N/A'} km
                  </div>
                  <div className="text-sm text-gray-600">Closest</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
