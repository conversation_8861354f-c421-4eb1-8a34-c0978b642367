/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Authors: <AUTHORS>
 * Module: User profile and authentication state management
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  city?: string;
  age_group?: string;
  dietary_restrictions?: string[];
  avatar?: string;
  role?: string;
}

export interface UserPreferences {
  spiceLevel?: 'mild' | 'medium' | 'hot' | 'extra-hot';
  budgetRange?: [number, number];
  favoriteCuisines?: string[];
  locationPreferences?: string[];
  diningStyle?: string[];
  dietaryRestrictions?: string[];
}

export interface UserProfile extends User {
  foodPersonality?: string;
  streak?: number;
  achievements?: string[];
  preferences?: UserPreferences;
  activityHistory?: Array<{
    type: 'search' | 'visited' | 'saved' | 'review';
    query?: string;
    restaurant?: string;
    rating?: number;
    date: string;
  }>;
  tasteProfile?: {
    spicy: number;
    sweet: number;
    savory: number;
    adventurous: number;
    traditional: number;
  };
}

interface UserStore {
  currentUser: UserProfile | null;
  users: UserProfile[];
  isAuthenticated: boolean;
  setUser: (userData: { currentUser: User; isAuthenticated: boolean }) => void;
  setCurrentUser: (userId: string) => void;
  updatePreferences: (updates: Partial<UserPreferences & { tasteProfile?: UserProfile['tasteProfile'] }>) => void;
  addActivity: (activity: { type: 'search' | 'visited' | 'saved' | 'review'; query?: string; restaurant?: string; rating?: number; date: string }) => void;
  logout: () => void;
}

// No dummy users - all users come from authentication

export const useUserStore = create<UserStore>()(
  persist(
    (set, get) => ({
      currentUser: null,
      users: [],
      isAuthenticated: false,

      setUser: (userData: { currentUser: User; isAuthenticated: boolean }) => {
        const user = userData.currentUser;

        // Determine food personality based on dietary restrictions
        const getFoodPersonality = (restrictions: string[] = []) => {
          if (restrictions.includes('vegan')) return 'Plant-Based Explorer';
          if (restrictions.includes('vegetarian')) return 'Veggie Enthusiast';
          if (restrictions.includes('halal')) return 'Halal Food Lover';
          if (restrictions.includes('keto')) return 'Keto Warrior';
          if (restrictions.includes('gluten-free')) return 'Gluten-Free Foodie';
          return 'Food Explorer';
        };

        const userProfile: UserProfile = {
          ...user,
          foodPersonality: getFoodPersonality(user.dietary_restrictions),
          streak: 1,
          achievements: ['New Member', 'First Login'],
          preferences: {
            spiceLevel: 'medium',
            budgetRange: [500, 2000],
            favoriteCuisines: user.dietary_restrictions?.length ? user.dietary_restrictions : ['Pakistani'],
            locationPreferences: [user.city || 'Islamabad'],
            diningStyle: ['Casual Dining'],
            dietaryRestrictions: user.dietary_restrictions || []
          },
          activityHistory: [],
          tasteProfile: {
            spicy: 50,
            sweet: 50,
            savory: 50,
            adventurous: 50,
            traditional: 50
          }
        };

        set({
          currentUser: userProfile,
          isAuthenticated: userData.isAuthenticated
        });
      },

      setCurrentUser: (userId: string) => {
        const user = get().users.find(u => u.id === userId);
        if (user) {
          set({ currentUser: user, isAuthenticated: true });
        }
      },

      updatePreferences: (updates: Partial<UserPreferences & { tasteProfile?: UserProfile['tasteProfile'] }>) => {
        const currentUser = get().currentUser;
        if (currentUser) {
          const updatedUser: UserProfile = {
            ...currentUser,
            preferences: { ...currentUser.preferences, ...updates },
            tasteProfile: updates.tasteProfile ? { ...currentUser.tasteProfile, ...updates.tasteProfile } : currentUser.tasteProfile
          };
          set({
            currentUser: updatedUser,
            users: get().users.map(u => u.id === currentUser.id ? updatedUser : u)
          });
        }
      },

      addActivity: (activity: { type: 'search' | 'visited' | 'saved' | 'review'; query?: string; restaurant?: string; rating?: number; date: string }) => {
        const currentUser = get().currentUser;
        if (currentUser) {
          const activityHistory = currentUser.activityHistory || [];
          const updatedUser: UserProfile = {
            ...currentUser,
            activityHistory: [activity, ...activityHistory.slice(0, 9)] // Keep last 10
          };
          set({
            currentUser: updatedUser,
            users: get().users.map(u => u.id === currentUser.id ? updatedUser : u)
          });
        }
      },

      logout: () => {
        // Only clear authentication state, preserve user data for potential re-login
        set({
          currentUser: null,
          isAuthenticated: false
          // Note: We don't clear the 'users' array to preserve preferences data
        });
      }
    }),
    {
      name: 'rotishoti-user-store',
    }
  )
);
