"""
RotiShoti - AI-Powered Food Discovery Platform
Author: <PERSON><PERSON><PERSON>
Application configuration and environment variables
"""

import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    PROJECT_NAME: str = "RotiShoti API"
    PROJECT_VERSION: str = "1.0.0"
    PROJECT_DESCRIPTION: str = "AI-powered food discovery platform for Pakistani cuisine"
    PROJECT_AUTHOR: str = "Masab Farooque"

    SUPABASE_URL: str = os.getenv("SUPABASE_URL", "")
    SUPABASE_KEY: str = os.getenv("SUPABASE_KEY", "")

    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", "super-secret-jwt-key")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    GROQ_API_KEY: str = os.getenv("GROQ_API_KEY", "")
    GROQ_FACT_CHECKER_API_KEY: str = os.getenv("GROQ_FACT_CHECKER_API_KEY", "")

    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"

settings = Settings()