#!/usr/bin/env python3
"""
RotiShoti - Improved Database Seeding Script
Author: <PERSON><PERSON><PERSON>
Enhanced script to properly seed database with vector embeddings
"""

import json
import asyncio
import sys
import os
from pathlib import Path
import logging
from typing import List, Dict, Any

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.db.supabase import create_supabase_client
from app.services.ai_service import AIService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_seed.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ImprovedDatabaseSeeder:
    def __init__(self):
        self.supabase = create_supabase_client()
        self.ai_service = AIService()
        self.processed_count = 0
        self.failed_count = 0
        self.total_restaurants = 0
        self.total_menu_items = 0

    async def clear_existing_data(self):
        """Clear all existing data from database"""
        logger.info("🧹 Clearing existing data...")
        try:
            # Clear in correct order due to foreign key constraints
            self.supabase.from_('menu_items').delete().gte('created_at', '1900-01-01').execute()
            logger.info("✅ Cleared menu_items table")
            
            self.supabase.from_('restaurant_branches').delete().gte('created_at', '1900-01-01').execute()
            logger.info("✅ Cleared restaurant_branches table")
            
            self.supabase.from_('restaurants').delete().gte('created_at', '1900-01-01').execute()
            logger.info("✅ Cleared restaurants table")
            
            logger.info("🎉 Successfully cleared all existing data")
        except Exception as e:
            logger.warning(f"⚠️ Error clearing data (might be empty): {e}")

    def load_restaurant_data(self) -> List[Dict[str, Any]]:
        """Load restaurant data from JSON file"""
        json_path = Path(__file__).parent.parent / "data" / "latest_islamabad_data.json"
        
        if not json_path.exists():
            logger.error(f"❌ JSON file not found: {json_path}")
            return []
        
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                restaurants_data = json.load(f)
            
            self.total_restaurants = len(restaurants_data)
            self.total_menu_items = sum(len(r.get('menu_items', [])) for r in restaurants_data)
            
            logger.info(f"📊 Loaded {self.total_restaurants} restaurants with {self.total_menu_items} total menu items")
            return restaurants_data
            
        except Exception as e:
            logger.error(f"❌ Error loading JSON data: {e}")
            return []

    async def insert_restaurant(self, restaurant_data: Dict[str, Any]) -> str:
        """Insert restaurant and return restaurant_id"""
        try:
            # Get main branch info
            main_branch = restaurant_data.get('branches', [{}])[0] if restaurant_data.get('branches') else {}
            
            # Prepare restaurant payload
            restaurant_payload = {
                "restaurant_id": restaurant_data.get("restaurant_id"),
                "name": restaurant_data.get("name"),
                "city": restaurant_data.get("city", "Islamabad"),
                "image_url": restaurant_data.get("image_url"),
                "cuisine_types": restaurant_data.get("cuisine_types", []),
                
                # Main branch info
                "area": main_branch.get("area"),
                "address": main_branch.get("address"),
                "phone": main_branch.get("phone"),
                "latitude": main_branch.get("coordinates", [None, None])[0],
                "longitude": main_branch.get("coordinates", [None, None])[1],
                
                # Price range
                "price_range_min": restaurant_data.get("price_range", {}).get("min"),
                "price_range_max": restaurant_data.get("price_range", {}).get("max"),
                "price_range_average": restaurant_data.get("price_range", {}).get("average"),
                
                # Ratings
                "rating_overall": restaurant_data.get("ratings", {}).get("overall"),
                "rating_food": restaurant_data.get("ratings", {}).get("food_quality"),
                "rating_service": restaurant_data.get("ratings", {}).get("service"),
                "rating_value": restaurant_data.get("ratings", {}).get("value"),
                
                # Ambiance
                "ambiance_type": restaurant_data.get("ambiance", {}).get("type"),
                "noise_level": restaurant_data.get("ambiance", {}).get("noise_level"),
                
                # Features
                "features": main_branch.get("features", []),
                
                # Store full data as JSON
                "full_data": restaurant_data
            }
            
            # Insert restaurant
            response = self.supabase.from_('restaurants').insert(restaurant_payload).execute()
            
            if response.data and len(response.data) > 0:
                restaurant_id = response.data[0]['id']
                logger.info(f"✅ Inserted restaurant: {restaurant_data.get('name')}")
                return restaurant_id
            else:
                logger.error(f"❌ Failed to insert restaurant: {restaurant_data.get('name')}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error inserting restaurant {restaurant_data.get('name')}: {e}")
            return None

    async def insert_restaurant_branches(self, restaurant_id: str, restaurant_data: Dict[str, Any]):
        """Insert restaurant branches"""
        try:
            branches = restaurant_data.get('branches', [])
            for branch in branches:
                branch_payload = {
                    "restaurant_id": restaurant_id,
                    "branch_id": branch.get("branch_id"),
                    "name": branch.get("name"),
                    "area": branch.get("area"),
                    "address": branch.get("address"),
                    "phone": branch.get("phone"),
                    "latitude": branch.get("coordinates", [None, None])[0],
                    "longitude": branch.get("coordinates", [None, None])[1],
                    "is_main_branch": branch.get("is_main_branch", False),
                    "features": branch.get("features", [])
                }
                
                self.supabase.from_('restaurant_branches').insert(branch_payload).execute()
                
        except Exception as e:
            logger.error(f"❌ Error inserting branches for restaurant: {e}")

    async def generate_enhanced_embedding_text(self, item: Dict[str, Any], restaurant_data: Dict[str, Any]) -> str:
        """Generate enhanced searchable text for better embeddings"""
        # Create comprehensive searchable text
        components = [
            item.get('name', ''),
            item.get('description', ''),
            item.get('category', ''),
            restaurant_data.get('name', ''),
            ' '.join(restaurant_data.get('cuisine_types', [])),
            restaurant_data.get('branches', [{}])[0].get('area', '') if restaurant_data.get('branches') else ''
        ]
        
        # Add spice level and dietary info if available
        if item.get('spice_level'):
            components.append(f"spice level {item['spice_level']}")
        if item.get('is_vegetarian'):
            components.append('vegetarian')
        
        # Join and clean
        searchable_text = ' '.join(filter(None, components)).strip()
        return searchable_text

    async def insert_menu_items_with_embeddings(self, restaurant_id: str, restaurant_data: Dict[str, Any]):
        """Insert menu items with vector embeddings"""
        menu_items = restaurant_data.get('menu_items', [])
        restaurant_name = restaurant_data.get('name', 'Unknown')
        
        logger.info(f"🍽️ Processing {len(menu_items)} menu items for {restaurant_name}")
        
        for i, item in enumerate(menu_items, 1):
            try:
                # Generate enhanced searchable text
                searchable_text = await self.generate_enhanced_embedding_text(item, restaurant_data)
                
                # Generate embedding
                embedding = None
                try:
                    embedding = await self.ai_service.generate_embedding(searchable_text)
                    logger.debug(f"✅ Generated embedding for: {item.get('name')}")
                except Exception as embedding_err:
                    logger.warning(f"⚠️ Failed to generate embedding for '{item.get('name')}': {embedding_err}")
                    self.failed_count += 1
                    continue
                
                # Prepare menu item payload
                menu_item_payload = {
                    "restaurant_id": restaurant_id,
                    "name": item.get("name"),
                    "description": item.get("description", ""),
                    "price": item.get("price"),
                    "category": item.get("category", ""),
                    "spice_level": item.get("spice_level", "Medium"),
                    "is_vegetarian": item.get("is_vegetarian", False),
                    "is_available": item.get("is_available", True),
                    "searchable_text": searchable_text.lower(),
                    "embedding": embedding
                }
                
                # Insert menu item
                response = self.supabase.from_('menu_items').insert(menu_item_payload).execute()
                
                if response.data:
                    self.processed_count += 1
                    if i % 10 == 0:  # Log progress every 10 items
                        logger.info(f"📈 Progress: {i}/{len(menu_items)} items processed for {restaurant_name}")
                else:
                    logger.warning(f"⚠️ Failed to insert menu item: {item.get('name')}")
                    self.failed_count += 1
                
                # Small delay to avoid overwhelming the API
                await asyncio.sleep(0.05)
                
            except Exception as e:
                logger.error(f"❌ Error processing menu item '{item.get('name')}': {e}")
                self.failed_count += 1
                continue

    async def seed_database(self):
        """Main seeding function"""
        logger.info("🚀 Starting improved database seeding...")

        # Load restaurant data
        restaurants_data = self.load_restaurant_data()
        if not restaurants_data:
            logger.error("❌ No restaurant data to process")
            return False

        # Clear existing data
        await self.clear_existing_data()

        # Process each restaurant
        successful_restaurants = 0

        for i, restaurant_data in enumerate(restaurants_data, 1):
            try:
                restaurant_name = restaurant_data.get('name', 'Unknown')
                logger.info(f"🏪 Processing restaurant {i}/{self.total_restaurants}: {restaurant_name}")

                # Insert restaurant
                restaurant_id = await self.insert_restaurant(restaurant_data)
                if not restaurant_id:
                    continue

                # Insert branches
                await self.insert_restaurant_branches(restaurant_id, restaurant_data)

                # Insert menu items with embeddings
                await self.insert_menu_items_with_embeddings(restaurant_id, restaurant_data)

                successful_restaurants += 1
                logger.info(f"✅ Completed restaurant: {restaurant_name} ({successful_restaurants}/{self.total_restaurants})")

            except Exception as e:
                logger.error(f"❌ Error processing restaurant {restaurant_data.get('name')}: {e}")
                continue

        # Final summary
        logger.info("🎉 Database seeding completed!")
        logger.info(f"📊 Summary:")
        logger.info(f"   • Restaurants processed: {successful_restaurants}/{self.total_restaurants}")
        logger.info(f"   • Menu items processed: {self.processed_count}")
        logger.info(f"   • Failed items: {self.failed_count}")
        logger.info(f"   • Success rate: {(self.processed_count / self.total_menu_items * 100):.1f}%")

        return successful_restaurants > 0

async def main():
    """Main function"""
    seeder = ImprovedDatabaseSeeder()
    success = await seeder.seed_database()

    if success:
        logger.info("✅ Database seeding completed successfully!")
        return 0
    else:
        logger.error("❌ Database seeding failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
