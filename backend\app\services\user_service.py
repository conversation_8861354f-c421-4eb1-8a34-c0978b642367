from app.db.supabase import create_supabase_client
from app.db.models import UserInDB
from app.schemas.user_schemas import UserPreferencesUpdate
from typing import Dict, Any

class UserService:
    def __init__(self):
        self.supabase = create_supabase_client()

    async def get_user_by_email(self, email: str) -> UserInDB | None:
        response = self.supabase.from_('users').select('*').eq('email', email).execute()

        if response.data and len(response.data) > 0:
            return UserInDB(**response.data[0])
    
        return None

    async def get_user_by_id(self, user_id: str) -> UserInDB | None:
        response = self.supabase.from_('users').select('*').eq('id', user_id).single().execute()
        if response.data:
            return UserInDB(**response.data)
        return None

    async def create_user(self, user_data: Dict[str, Any]) -> UserInDB:
        response = self.supabase.from_('users').insert(user_data).execute()
        if response.data:
            return UserInDB(**response.data[0])
        raise Exception("Failed to create user")

    async def update_user_profile(self, user_id: str, profile_data: Dict[str, Any]) -> UserInDB:
        response = self.supabase.from_('users').update(profile_data).eq('id', user_id).execute()
        if response.data:
            return UserInDB(**response.data[0])
        raise Exception("Failed to update user profile")

    async def update_user_preferences(self, user_id: str, preferences: UserPreferencesUpdate) -> Dict[str, Any]:
        # This assumes user preferences are stored directly in the 'users' table
        # or in a separate 'user_preferences' table linked by user_id.
        # For simplicity, let's assume they are part of the 'users' table for now.
        # You might need to adjust this based on your actual Supabase schema.
        update_data = preferences.model_dump(exclude_unset=True)
        response = self.supabase.from_('users').update(update_data).eq('id', user_id).execute()
        if response.data:
            return response.data[0]
        raise Exception("Failed to update user preferences")

    async def log_user_interaction(self, interaction_data: Dict[str, Any]):
        response = self.supabase.from_('user_interactions').insert(interaction_data).execute()
        if not response.data:
            print(f"Warning: Failed to log user interaction: {interaction_data}")

    async def get_user_history(self, user_id: str) -> list[Dict[str, Any]]:
        response = self.supabase.from_('user_interactions').select('*').eq('user_id', user_id).order('created_at', desc=True).execute()
        return response.data or []

    async def like_restaurant(self, user_id: str, restaurant_id: str) -> None:
        await self.log_user_interaction({
            "user_id": user_id,
            "interaction_type": "like",
            "restaurant_id": restaurant_id
        })

    async def rate_restaurant(self, user_id: str, restaurant_id: str, rating: int, feedback: str = None) -> None:
        await self.log_user_interaction({
            "user_id": user_id,
            "interaction_type": "rate",
            "restaurant_id": restaurant_id,
            "rating": rating,
            "feedback": feedback
        })

user_service = UserService()