
import React from 'react';
import Image from 'next/image';

interface MenuItemCardProps {
  name: string;
  description: string;
  price: number;
  imageUrl?: string;
  category?: string;
  spiceLevel?: string;
}

const MenuItemCard: React.FC<MenuItemCardProps> = ({ name, description, price, imageUrl, category, spiceLevel }) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col sm:flex-row items-center p-4">
      {imageUrl && (
        <div className="relative w-full sm:w-32 h-32 flex-shrink-0 mb-4 sm:mb-0 sm:mr-4">
          <Image
            src={imageUrl}
            alt={name}
            layout="fill"
            objectFit="cover"
            className="rounded-md"
          />
        </div>
      )}
      <div className="flex-grow text-center sm:text-left">
        <h3 className="text-lg font-semibold text-gray-800">{name}</h3>
        <p className="text-sm text-gray-600 mb-2">{description}</p>
        <div className="flex items-center justify-center sm:justify-start space-x-2 text-sm text-gray-500">
          {category && <span>{category}</span>}
          {spiceLevel && <span>• Spice: {spiceLevel}</span>}
        </div>
      </div>
      <div className="flex-shrink-0 mt-4 sm:mt-0 sm:ml-4">
        <span className="text-xl font-bold text-orange-600">PKR {price.toLocaleString()}</span>
      </div>
    </div>
  );
};

export default MenuItemCard;
