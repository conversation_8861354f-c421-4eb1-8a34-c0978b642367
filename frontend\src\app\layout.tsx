/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Author: <PERSON><PERSON><PERSON>
 * Root layout component for Next.js application
 */

import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import MobileNavBar from "@/components/layout/MobileNavBar";
import SessionWrapper from "@/components/providers/SessionWrapper";
import FloatingChatButton from "@/components/ui/FloatingChatButton";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "RotiShoti - AI Powered Food Discovery",
  description:
    "Find your next craving with personalized AI recommendations for Pakistani cuisine.",
  authors: [{ name: "Masab Farooque", url: "https://github.com/Masab12" }],
  keywords: [
    "food",
    "restaurants",
    "AI",
    "Pakistan",
    "Islamabad",
    "recommendations",
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className} suppressHydrationWarning={true}>
        <SessionWrapper>
          <div className="flex flex-col min-h-screen">
            {/* Header is now included in individual pages for more control */}
            <main className="flex-grow">{children}</main>
            <Footer />
            <MobileNavBar />

            {/* Global Floating Chat Button */}
            <FloatingChatButton />
          </div>
        </SessionWrapper>
      </body>
    </html>
  );
}
