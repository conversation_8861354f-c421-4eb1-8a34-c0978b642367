import json
import os
from typing import List, Dict, Any
from app.db.supabase import create_supabase_client
from app.schemas.restaurant_schemas import RestaurantBase, Branch, PriceRange, Ratings, Ambiance
from app.db.models import MenuItem
import logging

logger = logging.getLogger(__name__)

class DataLoaderService:
    def __init__(self):
        self.supabase = create_supabase_client()
        self.json_path = os.path.join(os.path.dirname(__file__), '../../data/latest_islamabad_data.json')

    def load_restaurants_from_json(self) -> List[Dict[str, Any]]:
        """Load restaurants from the latest JSON file"""
        try:
            with open(self.json_path, 'r', encoding='utf-8') as f:
                restaurants_data = json.load(f)
            
            logger.info(f"Loaded {len(restaurants_data)} restaurants from JSON")
            return restaurants_data
        except Exception as e:
            logger.error(f"Error loading restaurants from JSON: {e}")
            return []

    def validate_restaurant_data(self, restaurant_data: Dict[str, Any]) -> bool:
        """Validate restaurant data structure"""
        required_fields = ['restaurant_id', 'name', 'city', 'cuisine_types', 'price_range', 'branches', 'menu_items', 'ratings', 'ambiance']
        
        for field in required_fields:
            if field not in restaurant_data:
                logger.warning(f"Missing required field: {field} in restaurant {restaurant_data.get('name', 'Unknown')}")
                return False
        
        # Validate branches
        if not restaurant_data['branches'] or len(restaurant_data['branches']) == 0:
            logger.warning(f"No branches found for restaurant {restaurant_data['name']}")
            return False
        
        # Validate menu items
        if not restaurant_data['menu_items'] or len(restaurant_data['menu_items']) == 0:
            logger.warning(f"No menu items found for restaurant {restaurant_data['name']}")
            return False
        
        return True

    def transform_restaurant_for_db(self, restaurant_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform restaurant data for database insertion"""
        try:
            # Get main branch info
            main_branch = next((branch for branch in restaurant_data['branches'] if branch.get('is_main_branch', False)), restaurant_data['branches'][0])
            
            transformed = {
                'restaurant_id': restaurant_data['restaurant_id'],
                'name': restaurant_data['name'],
                'city': restaurant_data['city'],
                'image_url': restaurant_data.get('image_url'),
                'cuisine_types': restaurant_data['cuisine_types'],
                
                # Main branch info for backward compatibility
                'area': main_branch['area'],
                'address': main_branch['address'],
                'phone': main_branch.get('phone'),
                'latitude': main_branch['coordinates'][0] if main_branch.get('coordinates') else None,
                'longitude': main_branch['coordinates'][1] if main_branch.get('coordinates') else None,
                
                # Price range
                'price_range_min': restaurant_data['price_range']['min'],
                'price_range_max': restaurant_data['price_range']['max'],
                'price_range_average': restaurant_data['price_range']['average'],
                
                # Ratings
                'rating_overall': restaurant_data['ratings']['overall'],
                'rating_food': restaurant_data['ratings'].get('food_quality'),
                'rating_service': restaurant_data['ratings'].get('service'),
                'rating_value': restaurant_data['ratings'].get('value'),
                
                # Ambiance
                'ambiance_type': restaurant_data['ambiance']['type'],
                'noise_level': restaurant_data['ambiance'].get('noise_level'),
                
                # Features from main branch
                'features': main_branch.get('features', []),
                
                # Store full data as JSON for advanced queries
                'full_data': restaurant_data
            }
            
            return transformed
        except Exception as e:
            logger.error(f"Error transforming restaurant data for {restaurant_data.get('name', 'Unknown')}: {e}")
            return None

    def create_menu_item_embeddings(self, menu_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create embeddings for menu items for semantic search"""
        enhanced_items = []
        
        for item in menu_items:
            # Create searchable text for embedding
            searchable_text = f"{item['name']} {item.get('description', '')} {item.get('category', '')}"
            
            enhanced_item = {
                'name': item['name'],
                'price': item['price'],
                'description': item.get('description', ''),
                'category': item.get('category', ''),
                'searchable_text': searchable_text.lower(),
                # TODO: Add actual vector embedding here when implementing vector search
                'embedding': None
            }
            
            enhanced_items.append(enhanced_item)
        
        return enhanced_items

    async def seed_database(self) -> bool:
        """Seed database with restaurant data"""
        try:
            restaurants_data = self.load_restaurants_from_json()
            
            if not restaurants_data:
                logger.error("No restaurant data to seed")
                return False
            
            # Clear existing data
            logger.info("Clearing existing restaurant data...")
            try:
                self.supabase.from_('menu_items').delete().gte('created_at', '1900-01-01').execute()
                self.supabase.from_('restaurant_branches').delete().gte('created_at', '1900-01-01').execute()
                self.supabase.from_('restaurants').delete().gte('created_at', '1900-01-01').execute()
                logger.info("Successfully cleared existing data")
            except Exception as e:
                logger.warning(f"Error clearing data (might be empty): {e}")
            
            successful_inserts = 0
            
            for restaurant_data in restaurants_data:
                if not self.validate_restaurant_data(restaurant_data):
                    continue
                
                # Transform and insert restaurant
                transformed_restaurant = self.transform_restaurant_for_db(restaurant_data)
                if not transformed_restaurant:
                    continue
                
                # Insert restaurant
                restaurant_response = self.supabase.from_('restaurants').insert(transformed_restaurant).execute()
                
                if not restaurant_response.data:
                    logger.error(f"Failed to insert restaurant: {restaurant_data['name']}")
                    continue
                
                restaurant_id = restaurant_response.data[0]['id']
                
                # Insert branches
                for branch_data in restaurant_data['branches']:
                    branch_insert = {
                        'restaurant_id': restaurant_id,
                        'branch_id': branch_data['branch_id'],
                        'name': branch_data['name'],
                        'area': branch_data['area'],
                        'address': branch_data['address'],
                        'phone': branch_data.get('phone'),
                        'latitude': branch_data['coordinates'][0] if branch_data.get('coordinates') else None,
                        'longitude': branch_data['coordinates'][1] if branch_data.get('coordinates') else None,
                        'is_main_branch': branch_data.get('is_main_branch', False),
                        'features': branch_data.get('features', [])
                    }
                    
                    self.supabase.from_('restaurant_branches').insert(branch_insert).execute()
                
                # Insert menu items with embeddings
                enhanced_menu_items = self.create_menu_item_embeddings(restaurant_data['menu_items'])
                
                for item in enhanced_menu_items:
                    menu_item_insert = {
                        'restaurant_id': restaurant_id,
                        'name': item['name'],
                        'price': item['price'],
                        'description': item['description'],
                        'category': item['category'],
                        'searchable_text': item['searchable_text'],
                        'embedding': item['embedding']
                    }
                    
                    self.supabase.from_('menu_items').insert(menu_item_insert).execute()
                
                successful_inserts += 1
                logger.info(f"Successfully inserted restaurant: {restaurant_data['name']} ({successful_inserts}/{len(restaurants_data)})")
            
            logger.info(f"Database seeding completed. {successful_inserts} restaurants inserted successfully.")
            return True
            
        except Exception as e:
            logger.error(f"Error seeding database: {e}")
            return False

# Create singleton instance
data_loader_service = DataLoaderService()
