import json
import os
import asyncio
import uuid # For generating UUIDs for new entries
import logging # For better output than print

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# Import the pre-initialized Supabase client function (from your app.db.supabase)
from app.db.supabase import create_supabase_client

# Import the pre-initialized ai_service instance (from app.services.ai_service)
from app.services.ai_service import ai_service 

# (Optional) Import models if you want to use them for type hinting/validation in the script
# from app.db.models import RestaurantInDB, MenuItem

async def seed_database():
    supabase = create_supabase_client() # Get a Supabase client instance
    logger.info("Starting database seeding...")

    # *** IMPORTANT: REMOVE THIS LINE! ***
    # The embedding model is automatically initialized when 'ai_service' is imported
    # because 'ai_service = AIService()' runs at the module level in ai_service.py.
    # await ai_service.initialize_embedding_model() # This method does not exist
    logger.info("Embedding model implicitly initialized during ai_service import.")

    # Load data from new JSON file
    data_file_path = os.path.join(os.path.dirname(__file__), "data", "latest_islamabad_data.json")
    if not os.path.exists(data_file_path):
        logger.error(f"Error: Data file not found at {data_file_path}")
        return

    with open(data_file_path, 'r', encoding='utf-8') as f:
        restaurants_data = json.load(f)

    logger.info(f"Loaded {len(restaurants_data)} restaurants from latest data file")

    # Clear existing data first
    logger.info("Clearing existing data...")
    try:
        supabase.from_('menu_items').delete().gte('created_at', '1900-01-01').execute()
        supabase.from_('restaurant_branches').delete().gte('created_at', '1900-01-01').execute()
        supabase.from_('restaurants').delete().gte('created_at', '1900-01-01').execute()
        logger.info("Successfully cleared existing data")
    except Exception as e:
        logger.warning(f"Error clearing data (might be empty): {e}")

    for restaurant_json in restaurants_data: # Process each restaurant
        try:
            # Get main branch info for backward compatibility
            main_branch = None
            branches = restaurant_json.get("branches", [])
            if branches:
                main_branch = next((branch for branch in branches if branch.get('is_main_branch', False)), branches[0])

            # Prepare restaurant data for new structure
            restaurant_payload = {
                "restaurant_id": restaurant_json.get("restaurant_id"),
                "name": restaurant_json.get("name"),
                "city": restaurant_json.get("city"),
                "image_url": restaurant_json.get("image_url"),
                "cuisine_types": restaurant_json.get("cuisine_types", []),

                # Main branch info (for backward compatibility)
                "area": main_branch.get("area") if main_branch else None,
                "address": main_branch.get("address") if main_branch else None,
                "phone": main_branch.get("phone") if main_branch else None,
                "latitude": main_branch.get("coordinates", [None, None])[0] if main_branch and main_branch.get("coordinates") else None,
                "longitude": main_branch.get("coordinates", [None, None])[1] if main_branch and main_branch.get("coordinates") else None,

                # Price range
                "price_range_min": restaurant_json.get("price_range", {}).get("min"),
                "price_range_max": restaurant_json.get("price_range", {}).get("max"),
                "price_range_average": restaurant_json.get("price_range", {}).get("average"),

                # Ratings
                "rating_overall": restaurant_json.get("ratings", {}).get("overall"),
                "rating_food": restaurant_json.get("ratings", {}).get("food_quality"),
                "rating_service": restaurant_json.get("ratings", {}).get("service"),
                "rating_value": restaurant_json.get("ratings", {}).get("value"),

                # Ambiance
                "ambiance_type": restaurant_json.get("ambiance", {}).get("type"),
                "noise_level": restaurant_json.get("ambiance", {}).get("noise_level"),

                # Features from main branch
                "features": main_branch.get("features", []) if main_branch else [],

                # Store full data as JSON for advanced queries
                "full_data": restaurant_json
            }

            # Insert restaurant
            response = supabase.from_('restaurants').insert(restaurant_payload).execute()

            if response.data and len(response.data) > 0:
                restaurant_id = response.data[0]['id'] # Get the ID generated by Supabase
                logger.info(f"Inserted restaurant: {restaurant_json.get('name')} with ID: {restaurant_id}")

                # Insert branches
                branches = restaurant_json.get("branches", [])
                for branch_data in branches:
                    branch_payload = {
                        "restaurant_id": restaurant_id,
                        "branch_id": branch_data.get("branch_id"),
                        "name": branch_data.get("name"),
                        "area": branch_data.get("area"),
                        "address": branch_data.get("address"),
                        "phone": branch_data.get("phone"),
                        "latitude": branch_data.get("coordinates", [None, None])[0] if branch_data.get("coordinates") else None,
                        "longitude": branch_data.get("coordinates", [None, None])[1] if branch_data.get("coordinates") else None,
                        "is_main_branch": branch_data.get("is_main_branch", False),
                        "features": branch_data.get("features", [])
                    }

                    branch_response = supabase.from_('restaurant_branches').insert(branch_payload).execute()
                    if not branch_response.data:
                        logger.warning(f"Failed to insert branch '{branch_data.get('name')}' for '{restaurant_json.get('name')}'. Error: {branch_response.error}")

                # Insert menu items with embeddings
                menu_items = restaurant_json.get("menu_items", [])
                for item in menu_items:
                    item_text = f"{item.get('name', '')} - {item.get('description', '')}"
                    embedding = None
                    try:
                        embedding = await ai_service.generate_embedding(item_text)
                    except Exception as embedding_err:
                        logger.warning(f"Failed to generate embedding for item '{item.get('name')}' of '{restaurant_json.get('name')}': {embedding_err}. Setting embedding to None for this item.")
                        # It's better to log and proceed than crash the whole seeding process

                    # Prepare menu item data with searchable text and embeddings
                    menu_item_payload = {
                        "restaurant_id": restaurant_id,
                        "name": item.get("name"),
                        "description": item.get("description"),
                        "price": item.get("price"),
                        "category": item.get("category"),
                        "spice_level": item.get("spice_level"),
                        "is_vegetarian": item.get("is_vegetarian", False),
                        "is_available": item.get("is_available", True),
                        "searchable_text": item_text.lower(),
                        "embedding": embedding,  # Vector embedding for semantic search
                    }
                    menu_response = supabase.from_('menu_items').insert(menu_item_payload).execute()
                    if not menu_response.data:
                        logger.warning(f"Failed to insert menu item '{item.get('name')}' for '{restaurant_json.get('name')}'. Error: {menu_response.error}")
            else:
                logger.warning(f"Failed to insert restaurant {restaurant_json.get('name')}. Error: {response.error}")
        except Exception as e:
            logger.error(f"Error processing restaurant {restaurant_json.get('name')}: {e}")

    logger.info("Database seeding complete.")

if __name__ == "__main__":
    asyncio.run(seed_database())