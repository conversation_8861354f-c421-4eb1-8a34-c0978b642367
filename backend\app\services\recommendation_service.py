"""
RotiShoti - AI-Powered Food Discovery Platform
Authors: <AUTHORS>
Module: Recommendation Service - Business logic for AI-powered recommendations
"""

from typing import List, Dict, Any, Optional
from app.schemas.recommendation_schemas import RecommendationQuery, RecommendationResponse
from app.services.restaurant_service import restaurant_service
from app.services.ai_service import ai_service
from app.core.logging import get_service_logger

logger = get_service_logger()


class RecommendationService:
    """Service for generating AI-powered restaurant recommendations."""
    
    def __init__(self):
        self.restaurant_service = restaurant_service
        self.ai_service = ai_service

    async def generate_recommendations(
        self,
        user_query: str,
        user_id: str,
        location: str = "Islamabad",
        budget: Optional[str] = None,
        dietary_restrictions: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate AI-powered restaurant recommendations based on user query.
        
        Args:
            user_query: Natural language query from user
            user_id: User identifier for personalization
            location: City/location for recommendations
            budget: Budget preference (low, medium, high)
            dietary_restrictions: List of dietary restrictions
            
        Returns:
            List of restaurant recommendations with AI descriptions
        """
        try:
            logger.info(f"Generating recommendations for user {user_id} in {location}")
            
            # Get restaurants from database
            restaurants = await self.restaurant_service.get_restaurants_with_menu_items(location)
            
            if not restaurants:
                logger.warning(f"No restaurants found for location: {location}")
                return []
            
            # Apply filters based on preferences
            filtered_restaurants = self._apply_filters(
                restaurants, budget, dietary_restrictions
            )
            
            # Generate AI descriptions for each restaurant
            recommendations = []
            for restaurant in filtered_restaurants[:5]:  # Limit to top 5
                ai_description = await self._generate_ai_description(
                    restaurant, user_query, budget
                )
                
                recommendations.append({
                    "restaurant": restaurant,
                    "ai_description": ai_description,
                    "relevance_score": self._calculate_relevance_score(
                        restaurant, user_query
                    )
                })
            
            # Sort by relevance score
            recommendations.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            logger.info(f"Generated {len(recommendations)} recommendations")
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return []

    async def get_popular_recommendations(self, city: str = "Islamabad") -> List[Dict[str, Any]]:
        """
        Get popular restaurant recommendations based on ratings and reviews.
        
        Args:
            city: City to get popular restaurants from
            
        Returns:
            List of popular restaurants
        """
        try:
            logger.info(f"Getting popular recommendations for {city}")
            
            restaurants = await self.restaurant_service.get_restaurants_by_city(city)
            
            # Sort by rating and return top restaurants
            popular_restaurants = sorted(
                restaurants,
                key=lambda x: x.get('rating_overall', 0),
                reverse=True
            )[:10]
            
            return popular_restaurants
            
        except Exception as e:
            logger.error(f"Error getting popular recommendations: {e}")
            return []

    async def get_personalized_recommendations(
        self,
        user_preferences: Dict[str, Any],
        city: str = "Islamabad"
    ) -> List[Dict[str, Any]]:
        """
        Get personalized recommendations based on user preferences.
        
        Args:
            user_preferences: User's food preferences and history
            city: City to get recommendations from
            
        Returns:
            List of personalized restaurant recommendations
        """
        try:
            logger.info(f"Getting personalized recommendations for {city}")
            
            restaurants = await self.restaurant_service.get_restaurants_with_menu_items(city)
            
            # Filter by user's favorite cuisines
            favorite_cuisines = user_preferences.get('favorite_cuisines', [])
            if favorite_cuisines:
                restaurants = [
                    r for r in restaurants
                    if any(cuisine in r.get('cuisine_types', []) for cuisine in favorite_cuisines)
                ]
            
            # Filter by budget preference
            budget = user_preferences.get('budget_range')
            if budget:
                restaurants = self.restaurant_service.filter_restaurants_by_budget(
                    restaurants, budget
                )
            
            return restaurants[:10]
            
        except Exception as e:
            logger.error(f"Error getting personalized recommendations: {e}")
            return []

    def _apply_filters(
        self,
        restaurants: List[Dict[str, Any]],
        budget: Optional[str] = None,
        dietary_restrictions: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Apply filters to restaurant list based on preferences."""
        filtered = restaurants.copy()
        
        # Budget filtering
        if budget:
            budget_ranges = {
                'low': (0, 1000),
                'medium': (1000, 3000),
                'high': (3000, 10000)
            }
            
            if budget in budget_ranges:
                min_price, max_price = budget_ranges[budget]
                filtered = [
                    r for r in filtered
                    if min_price <= r.get('price_range_average', 0) <= max_price
                ]
        
        # Dietary restrictions filtering
        if dietary_restrictions and 'none' not in dietary_restrictions:
            # This would require additional data about dietary options
            # For now, we'll keep all restaurants
            pass
        
        return filtered

    async def _generate_ai_description(
        self,
        restaurant: Dict[str, Any],
        user_query: str,
        budget: Optional[str] = None
    ) -> str:
        """Generate AI description for a restaurant recommendation."""
        try:
            prompt = f"""
            Generate a brief, engaging description (2-3 sentences) for this restaurant recommendation:
            
            Restaurant: {restaurant.get('name')}
            Cuisine: {', '.join(restaurant.get('cuisine_types', []))}
            Location: {restaurant.get('area')}
            Rating: {restaurant.get('rating_overall')}/5
            Average Price: ₨{restaurant.get('price_range_average')}
            
            User Query: {user_query}
            Budget: {budget or 'Not specified'}
            
            Make it sound natural, enthusiastic, and relevant to the user's query.
            Use Pakistani expressions naturally but don't overdo it.
            """
            
            description = await self.ai_service.get_llm_response(prompt)
            return description.strip()
            
        except Exception as e:
            logger.error(f"Error generating AI description: {e}")
            return f"Great choice! {restaurant.get('name')} offers excellent {', '.join(restaurant.get('cuisine_types', []))} cuisine."

    def _calculate_relevance_score(self, restaurant: Dict[str, Any], user_query: str) -> float:
        """Calculate relevance score for a restaurant based on user query."""
        score = 0.0
        query_lower = user_query.lower()
        
        # Name matching
        if restaurant.get('name', '').lower() in query_lower:
            score += 0.3
        
        # Cuisine matching
        for cuisine in restaurant.get('cuisine_types', []):
            if cuisine.lower() in query_lower:
                score += 0.2
        
        # Rating boost
        rating = restaurant.get('rating_overall', 0)
        score += (rating / 5.0) * 0.3
        
        # Popular keywords
        keywords = ['burger', 'pizza', 'biryani', 'karahi', 'bbq', 'fast food']
        for keyword in keywords:
            if keyword in query_lower:
                # Check if restaurant serves this type of food
                menu_items = restaurant.get('menu_items', [])
                for item in menu_items:
                    if keyword in item.get('name', '').lower():
                        score += 0.2
                        break
        
        return min(score, 1.0)  # Cap at 1.0


# Create global instance
recommendation_service = RecommendationService()
