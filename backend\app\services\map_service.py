"""
RotiShoti - AI-Powered Food Discovery Platform
Author: Masab Farooque
Map Service - Handles map URL generation and location services
"""

from typing import Optional, Dict, Any
import urllib.parse


class MapService:
    def __init__(self):
        # Default map configuration
        self.default_zoom = 15
        self.default_map_type = "roadmap"
        
        # Restaurant-specific map URLs (can be moved to database later)
        self.restaurant_map_urls = {
            'Ranchers': 'https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d53129.74705387924!2d73.03405685138796!3d33.6672868312704!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbd69a19d5d55%3A0xd7fea0552b166b20!2sRanchers!5e0!3m2!1sen!2s!4v1751965255205!5m2!1sen!2s',
            'El Momento': 'https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d414.8206887769245!2d73.0732137!3d33.7201901!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbfccee08dcf9%3A0x986104adf505561f!2sEl%20Momento%20Islamabad!5e0!3m2!1sen!2s!4v1751965407955!5m2!1sen!2s',
            'Savour Foods': 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d106229.23995283147!2d72.9754635816166!3d33.69176991378149!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbfa2ae45b0c1%3A0x68f50820a41d12a!2sSavour%20Foods%20Islamabad!5e0!3m2!1sen!2s!4v1751965546697!5m2!1sen!2s',
            'Tuscany Courtyard': 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d207.3735225021651!2d73.0781685836828!3d33.73542741356615!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbf079cabb7f5%3A0xc8a2c73246e0492e!2sTuscany%20Courtyard%20Islamabad!5e0!3m2!1sen!2s!4v1751965778489!5m2!1sen!2s',
            'Char Grill Central (CGC)': 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3318.334368105111!2d73.05805540000001!3d33.726168900000005!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbf096fdff119%3A0x50ecd516b7a6af0c!2sChar%20Grill%20Central%20-%20CGC!5e0!3m2!1sen!2s!4v1751965906542!5m2!1sen!2s',
            'CGC': 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3318.334368105111!2d73.05805540000001!3d33.726168900000005!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbf096fdff119%3A0x50ecd516b7a6af0c!2sChar%20Grill%20Central%20-%20CGC!5e0!3m2!1sen!2s!4v1751965906542!5m2!1sen!2s',
            '2 Broke Engineers': 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3318.6371017325073!2d73.0540601!3d33.7183381!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbfdd49bcd119%3A0x5e95e59e97744b09!2s2%20Broke%20Engineers%20Restaurant!5e0!3m2!1sen!2s!4v1751966054099!5m2!1sen!2s',
            'Hanif Rajput Rooftop Grill': 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d13279.344587821366!2d73.02256992268735!3d33.68730667096147!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbf3c53d5d33f%3A0xf970ce50b21b27bb!2sHanif%20Rajput%20RoofTop%20Grill!5e0!3m2!1sen!2s!4v1751966213311!5m2!1sen!2s',
            '1969 Restaurant': 'https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d13279.447986641599!2d73.0730552!3d33.6866374!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbfe36fc35f5d%3A0x8832165b240f6cba!2s1969%20Restaurant!5e0!3m2!1sen!2s!4v1751966437277!5m2!1sen!2s',
            "Salt'n Pepper": 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3318.2!2d73.075655!3d33.719356!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbf0c5d5d5d5d%3A0x1234567890abcdef!2sSalt%27n%20Pepper%20Blue%20Area!5e0!3m2!1sen!2s!4v1751966500000!5m2!1sen!2s',
            'Rewayat': 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3318.8!2d73.069629!3d33.667548!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbd5a5a5a5a5a%3A0xabcdef1234567890!2sRewayat%20I-8%20Markaz!5e0!3m2!1sen!2s!4v1751966600000!5m2!1sen!2s',
            'Haleem Ghar': 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3318.1!2d73.0715!3d33.7214!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x38dfbf1c1c1c1c1c%3A0x9876543210fedcba!2sHaleem%20Ghar%20Blue%20Area!5e0!3m2!1sen!2s!4v1751966700000!5m2!1sen!2s'
        }

    def get_restaurant_map_url(self, restaurant_name: str, latitude: Optional[float] = None, longitude: Optional[float] = None) -> str:
        """
        Get map embed URL for a restaurant
        Priority: 1. Predefined URLs, 2. Generated from coordinates, 3. Search-based fallback
        """
        # First, try to get predefined URL
        if restaurant_name in self.restaurant_map_urls:
            return self.restaurant_map_urls[restaurant_name]
        
        # Second, try to generate from coordinates
        if latitude and longitude:
            return self.generate_embed_url_from_coordinates(latitude, longitude, restaurant_name)
        
        # Fallback: search-based URL
        return self.generate_search_based_url(restaurant_name)

    def generate_embed_url_from_coordinates(self, latitude: float, longitude: float, name: str = "") -> str:
        """
        Generate Google Maps embed URL from coordinates with proper formatting
        """
        # Create a proper Google Maps embed URL with coordinates
        # Format: https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3318!2d{longitude}!3d{latitude}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0:0x0!2z{encoded_name}!5e0!3m2!1sen!2s!4v{timestamp}!5m2!1sen!2s

        import time
        timestamp = int(time.time() * 1000)  # Current timestamp in milliseconds

        # Calculate zoom distance based on standard city view
        zoom_distance = 3318  # Standard distance for city-level zoom

        # Create the pb parameter with proper coordinate formatting
        pb_param = (
            f"!1m18!1m12!1m3!1d{zoom_distance}!2d{longitude}!3d{latitude}!"
            f"2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!"
            f"1s0x0%3A0x0!2z{urllib.parse.quote(name) if name else 'Restaurant'}!"
            f"5e0!3m2!1sen!2s!4v{timestamp}!5m2!1sen!2s"
        )

        return f"https://www.google.com/maps/embed?pb={pb_param}"

    def generate_search_based_url(self, restaurant_name: str, city: str = "Islamabad") -> str:
        """
        Generate search-based Google Maps embed URL
        Note: This requires a Google Maps API key for production use
        """
        query = f"{restaurant_name}, {city}, Pakistan"
        encoded_query = urllib.parse.quote(query)
        
        # For development, use a generic search URL
        # In production, you would use: https://www.google.com/maps/embed/v1/search?key=YOUR_API_KEY&q={encoded_query}
        return f"https://www.google.com/maps/search/{encoded_query}"

    def get_directions_url(self, restaurant_name: str, latitude: Optional[float] = None, longitude: Optional[float] = None) -> str:
        """
        Generate Google Maps directions URL
        """
        if latitude and longitude:
            return f"https://www.google.com/maps/dir/?api=1&destination={latitude},{longitude}"
        else:
            encoded_name = urllib.parse.quote(f"{restaurant_name}, Islamabad, Pakistan")
            return f"https://www.google.com/maps/dir/?api=1&destination={encoded_name}"

    def get_all_restaurant_map_urls(self) -> Dict[str, str]:
        """
        Get all predefined restaurant map URLs
        """
        return self.restaurant_map_urls.copy()

    def add_restaurant_map_url(self, restaurant_name: str, map_url: str) -> None:
        """
        Add a new restaurant map URL (for dynamic updates)
        """
        self.restaurant_map_urls[restaurant_name] = map_url

    def validate_map_url(self, url: str) -> bool:
        """
        Validate if a URL is a valid Google Maps embed URL
        """
        valid_patterns = [
            "https://www.google.com/maps/embed",
            "https://maps.google.com/embed",
            "https://www.google.com/maps/search"
        ]
        
        return any(url.startswith(pattern) for pattern in valid_patterns)

    def format_restaurant_with_map(self, restaurant: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add map URL to restaurant data using branch coordinates
        """
        restaurant_copy = restaurant.copy()

        # Extract coordinates from the main branch
        latitude, longitude = None, None
        branches = restaurant.get('branches', [])
        if branches:
            main_branch = next((b for b in branches if b.get('is_main_branch')), branches[0])
            coordinates = main_branch.get('coordinates', [])
            if len(coordinates) >= 2:
                latitude, longitude = coordinates[0], coordinates[1]

        map_url = self.get_restaurant_map_url(
            restaurant.get('name', ''),
            latitude,
            longitude
        )

        restaurant_copy['map_embed_url'] = map_url
        restaurant_copy['directions_url'] = self.get_directions_url(
            restaurant.get('name', ''),
            latitude,
            longitude
        )

        return restaurant_copy


# Create global instance
map_service = MapService()
