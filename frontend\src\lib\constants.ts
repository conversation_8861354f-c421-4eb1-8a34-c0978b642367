/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Authors: <AUTHORS>
 * Module: Application constants and configuration
 */

// Application metadata
export const APP_CONFIG = {
  name: 'RotiShoti',
  description: 'AI-Powered Food Discovery Platform for Pakistani Cuisine',
  version: '2.0.0',
  authors: ['Masab Farooque', '<PERSON><PERSON>'],
  repository: 'https://github.com/Masab12/RotiShoti',
} as const;

// API configuration
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
} as const;

// API Routes
export const API_ROUTES = {
  AUTH: {
    REGISTER: '/api/v1/auth/register',
    LOGIN: '/api/v1/auth/login',
    PROFILE: '/api/v1/auth/profile',
  },
  RESTAURANTS: {
    ALL: '/api/v1/restaurants',
    SEARCH: '/api/v1/restaurants/search',
    NEAR_ME: '/api/v1/restaurants/near_me',
  },
  RECOMMENDATIONS: {
    QUERY: '/api/v1/recommendations/query',
    POPULAR: '/api/v1/recommendations/popular',
    PERSONALIZED: '/api/v1/recommendations/personalized',
    FEEDBACK: '/api/v1/recommendations/feedback',
  },
  USERS: {
    HISTORY: '/api/v1/users/me/history',
    LIKE: '/api/v1/users/me/like',
    RATE: '/api/v1/users/me/rate',
    PREFERENCES: '/api/v1/users/me/preferences',
  },
};

// Default Values
export const DEFAULT_LOCATION = {
  lat: 33.7294,
  lng: 73.0931,
  address: 'Islamabad, Pakistan',
};

export const APP_NAME = 'RotiShoti';

// Other constants like pagination limits, etc.
export const PAGINATION_LIMIT = 10;