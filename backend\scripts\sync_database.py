#!/usr/bin/env python3
"""
Sync database with latest JSON data to ensure 100% accuracy
"""

import json
import asyncio
import sys
import os
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.db.supabase import create_supabase_client
from app.services.ai_service import AIService

async def sync_database():
    """Sync database with latest JSON data"""
    
    # Load LATEST JSON data
    json_path = Path(__file__).parent.parent / "data" / "latest_islamabad_data.json"
    
    if not json_path.exists():
        print(f"❌ JSON file not found: {json_path}")
        return
    
    with open(json_path, 'r', encoding='utf-8') as f:
        restaurants_data = json.load(f)
    
    print(f"📊 Loaded {len(restaurants_data)} restaurants from JSON")
    
    # Get Supabase client
    supabase = create_supabase_client()
    ai_service = AIService()
    
    # Clear existing data
    print("🗑️ Clearing existing data...")
    try:
        # Get all records and delete them
        menu_items = supabase.table('menu_items').select('id').execute()
        if menu_items.data:
            for item in menu_items.data:
                supabase.table('menu_items').delete().eq('id', item['id']).execute()

        restaurants = supabase.table('restaurants').select('id').execute()
        if restaurants.data:
            for restaurant in restaurants.data:
                supabase.table('restaurants').delete().eq('id', restaurant['id']).execute()

        print("✅ Existing data cleared")
    except Exception as e:
        print(f"⚠️ Error clearing data: {e} - continuing anyway")
    
    # Insert restaurants and menu items
    for restaurant_data in restaurants_data:
        try:
            # Get main branch info (use first branch as default)
            main_branch = restaurant_data['branches'][0] if restaurant_data.get('branches') else {}

            # Insert restaurant with correct schema
            restaurant_insert = {
                'restaurant_id': restaurant_data['restaurant_id'],
                'name': restaurant_data['name'],
                'area': main_branch.get('area', 'Islamabad'),
                'address': main_branch.get('address', ''),
                'city': restaurant_data.get('city', 'Islamabad'),
                'image_url': restaurant_data.get('image_url', ''),
                'cuisine_types': restaurant_data['cuisine_types'],
                'phone': main_branch.get('phone', ''),
                'price_range_min': restaurant_data['price_range']['min'],
                'price_range_max': restaurant_data['price_range']['max'],
                'price_range_average': restaurant_data['price_range'].get('average', restaurant_data['price_range']['min']),
                'rating_overall': restaurant_data.get('ratings', {}).get('overall', 4.0),
                'rating_food': restaurant_data.get('ratings', {}).get('food', 4.0),
                'rating_service': restaurant_data.get('ratings', {}).get('service', 4.0),
                'rating_value': restaurant_data.get('ratings', {}).get('value', 4.0),
                'ambiance_type': restaurant_data.get('ambiance', {}).get('type', 'Casual'),
                'noise_level': restaurant_data.get('ambiance', {}).get('noise_level', 'Moderate'),
                'latitude': main_branch.get('coordinates', [33.7, 73.1])[0],
                'longitude': main_branch.get('coordinates', [33.7, 73.1])[1],
                'features': main_branch.get('features', []),
                'full_data': restaurant_data  # Store complete JSON for reference
            }
            
            restaurant_result = supabase.table('restaurants').insert(restaurant_insert).execute()
            restaurant_id = restaurant_result.data[0]['id']
            
            print(f"✅ Inserted restaurant: {restaurant_data['name']}")
            
            # Insert menu items with embeddings
            for item in restaurant_data['menu_items']:
                # Create searchable text for embedding
                searchable_text = f"{item['name']} {item.get('description', '')} {item.get('category', '')} {restaurant_data['name']} {' '.join(restaurant_data['cuisine_types'])}".strip()
                
                # Generate embedding
                embedding = await ai_service.generate_embedding(searchable_text)
                
                menu_item_insert = {
                    'restaurant_id': restaurant_id,
                    'name': item['name'],
                    'description': item.get('description', ''),
                    'price': item['price'],
                    'category': item.get('category', ''),
                    'spice_level': item.get('spice_level', 'Medium'),
                    'embedding': embedding
                }
                
                supabase.table('menu_items').insert(menu_item_insert).execute()
            
            print(f"✅ Inserted {len(restaurant_data['menu_items'])} menu items for {restaurant_data['name']}")
            
        except Exception as e:
            print(f"❌ Error inserting {restaurant_data['name']}: {str(e)}")
            print(f"   Restaurant data keys: {list(restaurant_data.keys())}")
            if 'branches' in restaurant_data:
                print(f"   Branches: {len(restaurant_data['branches'])}")
            continue
    
    print("🎉 Database sync completed!")
    
    # Verify sync
    restaurants_count = supabase.table('restaurants').select('id').execute()
    menu_items_count = supabase.table('menu_items').select('id').execute()
    
    print(f"📊 Final counts:")
    print(f"   Restaurants: {len(restaurants_count.data)}")
    print(f"   Menu Items: {len(menu_items_count.data)}")

if __name__ == "__main__":
    asyncio.run(sync_database())
