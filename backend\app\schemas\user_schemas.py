from pydantic import BaseModel
from typing import Optional, List

class UserPreferencesUpdate(BaseModel):
    cuisines: Optional[List[str]] = None
    spice_tolerance: Optional[str] = None
    budget_range: Optional[List[int]] = None
    dining_style: Optional[List[str]] = None
    preferred_areas: Optional[List[str]] = None

class UserActivityLog(BaseModel):
    interaction_type: str
    restaurant_id: Optional[str] = None
    query: Optional[str] = None
    rating: Optional[int] = None
    feedback: Optional[str] = None