'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';

interface Restaurant {
  id: string;
  name: string;
  cuisine_types: string[];
  area: string;
  address: string;
  rating_overall: number;
  price_range_min: number;
  price_range_max: number;
  image_url?: string;
}

interface RealTimeSearchProps {
  onRestaurantSelect?: (restaurant: Restaurant) => void;
  placeholder?: string;
  className?: string;
}

export const RealTimeSearch: React.FC<RealTimeSearchProps> = ({
  onRestaurantSelect,
  placeholder = "Search for restaurants, dishes, or cuisines...",
  className = ""
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<Restaurant[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.trim().length >= 2) {
        performSearch(query.trim());
      } else {
        setResults([]);
        setShowResults(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query]);

  // Handle clicks outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const performSearch = async (searchQuery: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`http://localhost:8000/api/v1/restaurants/search?q=${encodeURIComponent(searchQuery)}`);
      if (response.ok) {
        const data = await response.json();
        setResults(data.slice(0, 8)); // Limit to 8 results
        setShowResults(true);
        setSelectedIndex(-1);
      }
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showResults || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : prev));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handleRestaurantClick(results[selectedIndex]);
        } else if (query.trim()) {
          // Navigate to search results page
          router.push(`/restaurants?q=${encodeURIComponent(query.trim())}`);
          setShowResults(false);
        }
        break;
      case 'Escape':
        setShowResults(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleRestaurantClick = (restaurant: Restaurant) => {
    if (onRestaurantSelect) {
      onRestaurantSelect(restaurant);
    } else {
      // Navigate to restaurant page or open modal
      router.push(`/restaurants/${restaurant.id}`);
    }
    setShowResults(false);
    setQuery('');
    setSelectedIndex(-1);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      router.push(`/restaurants?q=${encodeURIComponent(query.trim())}`);
      setShowResults(false);
    }
  };

  const getRestaurantImage = (name: string) => {
    const imageIds = [
      'UxRhrU8fPHQ', 'ZuIDLSz3XLg', 'N_Y88TWmGwA', 'jpkfc5_d-DI',
      'lP5MCM6nZ5A', 'MQUqbmszGGM', 'dphM2U1xq0U', 'IGfIGP5ONV0'
    ];
    const randomId = imageIds[Math.abs(name.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % imageIds.length];
    return `https://images.unsplash.com/${randomId}?w=80&h=80&fit=crop`;
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => {
              if (results.length > 0) setShowResults(true);
            }}
            placeholder={placeholder}
            className="w-full h-12 pl-12 pr-24 rounded-2xl border-2 border-orange-200 focus:border-orange-400 focus:outline-none bg-orange-50 text-gray-800 placeholder-gray-500 transition-all duration-200"
          />
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-orange-400 border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <svg className="w-5 h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            )}
          </div>
          <button
            type="submit"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-xl hover:from-red-600 hover:to-orange-600 transition-all duration-200 font-medium text-sm"
          >
            Search
          </button>
        </div>
      </form>

      {/* Search Results Dropdown */}
      {showResults && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-2xl shadow-2xl border border-orange-100 max-h-96 overflow-y-auto z-50">
          {results.length > 0 ? (
            <>
              <div className="p-4 border-b border-orange-100">
                <p className="text-sm text-gray-600">
                  Found {results.length} restaurant{results.length !== 1 ? 's' : ''}
                </p>
              </div>
              {results.map((restaurant, index) => (
                <div
                  key={restaurant.id}
                  className={`p-4 hover:bg-orange-50 cursor-pointer transition-colors border-b border-gray-100 last:border-b-0 ${
                    index === selectedIndex ? 'bg-orange-50' : ''
                  }`}
                  onClick={() => handleRestaurantClick(restaurant)}
                >
                  <div className="flex items-center gap-4">
                    <img
                      src={restaurant.image_url || getRestaurantImage(restaurant.name)}
                      alt={restaurant.name}
                      className="w-12 h-12 rounded-xl object-cover"
                    />
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{restaurant.name}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>📍 {restaurant.area}</span>
                        <span>⭐ {restaurant.rating_overall}/5</span>
                        <span>💰 Rs {restaurant.price_range_min}-{restaurant.price_range_max}</span>
                      </div>
                      <div className="flex gap-2 mt-1">
                        {restaurant.cuisine_types.slice(0, 2).map((cuisine, idx) => (
                          <span
                            key={idx}
                            className="bg-orange-100 text-orange-700 px-2 py-1 rounded-full text-xs"
                          >
                            {cuisine}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="text-orange-500">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              ))}
              {query.trim() && (
                <div className="p-4 border-t border-orange-100">
                  <button
                    onClick={() => {
                      router.push(`/restaurants?q=${encodeURIComponent(query.trim())}`);
                      setShowResults(false);
                    }}
                    className="w-full text-left text-orange-600 hover:text-orange-700 font-medium"
                  >
                    View all results for "{query}" →
                  </button>
                </div>
              )}
            </>
          ) : (
            <div className="p-8 text-center">
              <div className="text-4xl mb-2">🔍</div>
              <p className="text-gray-600">No restaurants found</p>
              <p className="text-sm text-gray-500">Try searching for a different term</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
