'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, MessageCircle } from 'lucide-react';
import { useUserStore } from '@/store/userStore';
import { RotiShotiAgent, ThinkingAnimation } from './RotiShotiAgent';
import { RestaurantProfile } from '../restaurant/RestaurantProfile';
import { UniversalMap } from '../maps/UniversalMap';
import '../../styles/design-system.css';

interface RestaurantCard {
  id?: string;
  name: string;
  area: string;
  address: string;
  rating: string;
  item_name: string;
  price: string;
  map_embed_url: string;
  latitude?: number;
  longitude?: number;
  cuisine_types?: string[];
  phone?: string;
  price_range_min?: number;
  price_range_max?: number;
  image_url?: string;
}

interface Message {
  id: string;
  type: 'user' | 'bot' | 'system';
  content: string;
  timestamp: Date;
  isTyping?: boolean;
  restaurants?: RestaurantCard[];
  confidence?: number;
  factChecked?: boolean;
  accuracyScore?: number;
  primaryModel?: string;
  queryIntent?: string;
  processingMetadata?: {
    vector_search_results?: number;
    restaurant_cards_generated?: number;
    budget_info?: any;
    user_personality?: string;
  };
}

interface ChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialQuery?: string;
}

const ChatModal: React.FC<ChatModalProps> = ({
  isOpen,
  onClose,
  initialQuery = ''
}) => {
  const { currentUser } = useUserStore();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [selectedRestaurant, setSelectedRestaurant] = useState<any>(null);
  const [showMap, setShowMap] = useState(false);
  const [mapRestaurant, setMapRestaurant] = useState<any>(null);
  const [allRestaurants, setAllRestaurants] = useState<any[]>([]);

  const [isTyping, setIsTyping] = useState(false);
  const [conversationSummary, setConversationSummary] = useState<string>('');
  const [sessionStats, setSessionStats] = useState({
    startTime: new Date(),
    messageCount: 0,
    restaurantsViewed: 0,
    topCuisines: [] as string[]
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Intelligent conversation persistence
  const chatStorageKey = `rotishoti-chat-${currentUser?.id || 'anonymous'}`;
  const sessionStorageKey = `rotishoti-session-${Date.now()}`;

  // Load saved conversation on mount
  useEffect(() => {
    if (isOpen) {
      try {
        const savedMessages = localStorage.getItem(chatStorageKey);
        if (savedMessages) {
          const parsedMessages = JSON.parse(savedMessages);
          // Only load recent messages (last 24 hours)
          const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
          const recentMessages = parsedMessages
            .map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp) // Convert string back to Date object
            }))
            .filter((msg: Message) =>
              msg.timestamp > oneDayAgo
            );
          if (recentMessages.length > 0) {
            setMessages(recentMessages);
            setShowSuggestions(false);
          }
        }
      } catch (error) {
        console.error('Error loading saved conversation:', error);
        // Clear corrupted data
        localStorage.removeItem(chatStorageKey);
      }
    }
  }, [isOpen, chatStorageKey]);

  // Save conversation intelligently
  useEffect(() => {
    if (messages.length > 0) {
      try {
        // Save to localStorage for persistence
        localStorage.setItem(chatStorageKey, JSON.stringify(messages));

        // Also save session metadata
        const sessionData = {
          lastActivity: new Date().toISOString(),
          messageCount: messages.length,
          userId: currentUser?.id || 'anonymous',
          userPreferences: {
            budget: currentUser?.preferences?.budgetRange,
            cuisines: currentUser?.preferences?.favoriteCuisines,
            dietary: currentUser?.preferences?.dietaryRestrictions
          }
        };
        sessionStorage.setItem(sessionStorageKey, JSON.stringify(sessionData));
      } catch (error) {
        console.error('Error saving conversation:', error);
      }
    }
  }, [messages, chatStorageKey, sessionStorageKey, currentUser]);

  // Generate conversation summary for context
  useEffect(() => {
    if (messages.length >= 5) {
      const userMessages = messages.filter(m => m.type === 'user').slice(-3);
      const restaurantMentions = messages
        .filter(m => m.restaurants && m.restaurants.length > 0)
        .flatMap(m => m.restaurants?.map(r => r.name) || [])
        .slice(0, 5);

      const summary = `Recent conversation: User asked about ${userMessages.map(m => m.content).join(', ')}. ${
        restaurantMentions.length > 0 ? `Discussed restaurants: ${restaurantMentions.join(', ')}.` : ''
      }`;

      setConversationSummary(summary);
    }
  }, [messages]);

  // Auto-focus input when modal opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  // Handle initial query
  useEffect(() => {
    if (initialQuery && isOpen && messages.length === 0) {
      setInputValue(initialQuery);
      setTimeout(() => handleSendMessage(initialQuery), 500);
    }
  }, [initialQuery, isOpen]);

  // Auto-scroll for bot messages
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.type === 'bot' && !lastMessage.isTyping) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  }, [messages]);

  const handleSendMessage = async (messageText?: string) => {
    const text = messageText || inputValue.trim();
    if (!text || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: text,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setShowSuggestions(false);
    setIsTyping(true);

    // Add typing indicator
    const typingMessage: Message = {
      id: (Date.now() + 1).toString(),
      type: 'bot',
      content: '',
      timestamp: new Date(),
      isTyping: true
    };
    setMessages(prev => [...prev, typingMessage]);

    try {
      const response = await fetch('http://localhost:8000/api/v1/chat/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_query: text,
          location: 'Islamabad',
          budget: currentUser?.preferences?.budgetRange ?
            (currentUser.preferences.budgetRange[1] > 2000 ? 'high' :
             currentUser.preferences.budgetRange[1] > 1000 ? 'medium' : 'low') : 'medium',
          user_profile: currentUser ? {
            name: currentUser.name || 'User',
            food_personality: currentUser.foodPersonality || 'adventurous',
            favorite_cuisines: currentUser.preferences?.favoriteCuisines || ['Pakistani'],
            spice_level: currentUser.preferences?.spiceLevel || 'medium',
            dining_style: currentUser.preferences?.diningStyle || ['casual'],
            taste_profile: currentUser.tasteProfile || {},
            budget_range: currentUser.preferences?.budgetRange || [500, 2500]
          } : null,
          conversation_history: messages
            .filter(msg => msg.type !== 'system' && !msg.isTyping)
            .slice(-15) // Increased context window for better continuity
            .map(msg => ({
              role: msg.type === 'user' ? 'user' : 'assistant',
              content: msg.content,
              timestamp: msg.timestamp,
              // Include restaurant context for better recommendations
              restaurants: msg.restaurants?.map(r => ({
                name: r.name,
                cuisine: r.cuisine_types,
                area: r.area,
                price: r.price
              }))
            }))
        })
      });

      const data = await response.json();

      // Remove typing indicator and add real response
      setMessages(prev => prev.filter(msg => !msg.isTyping));

      // Enhanced bot message with AI intelligence metadata (backend handles intelligence now)
      const botMessage: Message = {
        id: (Date.now() + 2).toString(),
        type: 'bot',
        content: data.response || 'Sorry, I couldn\'t process that request.',
        timestamp: new Date(),
        restaurants: data.restaurants || [],
        confidence: data.confidence || 1.0,
        factChecked: data.fact_checked || false,
        accuracyScore: data.accuracy_score,
        primaryModel: data.primary_model,
        queryIntent: data.query_intent,
        processingMetadata: data.processing_metadata
      };

      setMessages(prev => [...prev, botMessage]);

      // Update session stats
      setSessionStats(prev => ({
        ...prev,
        messageCount: prev.messageCount + 1,
        restaurantsViewed: prev.restaurantsViewed + (data.restaurants?.length || 0),
        topCuisines: [
          ...prev.topCuisines,
          ...(data.restaurants?.flatMap((r: any) => r.cuisine_types || []) || [])
        ].slice(0, 10) // Keep top 10 cuisines
      }));

    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev => prev.filter(msg => !msg.isTyping));

      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        type: 'bot',
        content: 'Sorry, I\'m having trouble connecting right now. Please try again.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const handleRestaurantAction = async (restaurant: RestaurantCard, action: 'menu' | 'directions') => {
    if (action === 'menu') {
      try {
        let restaurantData = null;
        
        if (restaurant.id) {
          const response = await fetch(`http://localhost:8000/api/v1/restaurants/${restaurant.id}`);
          if (response.ok) {
            restaurantData = await response.json();
          }
        }
        
        if (!restaurantData) {
          const searchResponse = await fetch(`http://localhost:8000/api/v1/restaurants/`);
          if (searchResponse.ok) {
            const allRestaurants = await searchResponse.json();
            restaurantData = allRestaurants.find((r: any) => 
              r.name.toLowerCase() === restaurant.name.toLowerCase()
            );
          }
        }
        
        if (restaurantData) {
          setSelectedRestaurant(restaurantData);
        } else {
          setSelectedRestaurant({
            id: restaurant.id || restaurant.name.toLowerCase().replace(/\s+/g, '-'),
            name: restaurant.name,
            area: restaurant.area,
            address: restaurant.address,
            latitude: restaurant.latitude || 33.6844,
            longitude: restaurant.longitude || 73.0479,
            rating_overall: parseFloat(restaurant.rating.replace('/5', '')) || 4.0,
            price_range_min: restaurant.price_range_min || 200,
            price_range_max: restaurant.price_range_max || 2000,
            price_range_average: Math.floor(((restaurant.price_range_min || 200) + (restaurant.price_range_max || 2000)) / 2),
            cuisine_types: restaurant.cuisine_types || ['Pakistani'],
            ambiance_type: 'Casual Dining',
            features: ['Dine-in', 'Takeaway'],
            phone: restaurant.phone || '051-1234567',
            image_url: restaurant.image_url
          });
        }
      } catch (error) {
        console.error('Error fetching restaurant details:', error);
        alert('Unable to load restaurant menu. Please try again.');
      }
    } else if (action === 'directions') {
      let latitude = restaurant.latitude;
      let longitude = restaurant.longitude;
      
      if (!latitude || !longitude) {
        try {
          if (restaurant.id) {
            const response = await fetch(`http://localhost:8000/api/v1/restaurants/${restaurant.id}`);
            if (response.ok) {
              const restaurantData = await response.json();
              latitude = restaurantData.latitude;
              longitude = restaurantData.longitude;
            }
          }
        } catch (error) {
          console.error('Error fetching restaurant coordinates:', error);
        }
      }
      
      if (!latitude || !longitude) {
        latitude = 33.6844;
        longitude = 73.0479;
        alert(`Location data for ${restaurant.name} is not available. Showing approximate location in Islamabad.`);
      }

      const mapRestaurantData = {
        id: restaurant.id || `restaurant-${Date.now()}`,
        name: restaurant.name,
        address: restaurant.address || `${restaurant.area}, Islamabad`,
        latitude: latitude,
        longitude: longitude,
        rating: parseFloat(restaurant.rating.replace('/5', '')) || 4.0,
        cuisine_types: restaurant.cuisine_types || ['Pakistani', 'Desi'],
        price_range_min: restaurant.price_range_min || 500,
        price_range_max: restaurant.price_range_max || 2000,
        phone: restaurant.phone || '051-1234567',
        image_url: restaurant.image_url
      };

      const currentRestaurants = messages
        .filter(msg => msg.restaurants && msg.restaurants.length > 0)
        .flatMap(msg => msg.restaurants)
        .filter(r => r != null)
        .map(r => ({
          id: r.id || `restaurant-${Date.now()}-${Math.random()}`,
          name: r.name || 'Unknown Restaurant',
          address: r.address || `${r.area || 'Islamabad'}, Islamabad`,
          latitude: r.latitude || 33.6844,
          longitude: r.longitude || 73.0479,
          rating: parseFloat((r.rating || '4.0/5').replace('/5', '')) || 4.0,
          cuisine_types: r.cuisine_types || ['Pakistani', 'Desi'],
          price_range_min: r.price_range_min || 500,
          price_range_max: r.price_range_max || 2000,
          phone: r.phone || '051-1234567',
          image_url: r.image_url
        }));

      setMapRestaurant(mapRestaurantData);
      setAllRestaurants(currentRestaurants);
      setShowMap(true);
    }
  };

  const clearChat = () => {
    setMessages([]);
    setShowSuggestions(true);

    // Clear saved conversation data
    try {
      localStorage.removeItem(chatStorageKey);
      sessionStorage.removeItem(sessionStorageKey);
    } catch (error) {
      console.error('Error clearing saved conversation:', error);
    }
  };

  // Dynamic suggestion chips based on conversation context
  const getDynamicSuggestions = () => {
    const baseChips = [
      "🍔 Best burgers in Islamabad",
      "🍕 Pizza places near me",
      "🍛 Biryani under Rs 1000",
      "🌶️ Spicy Pakistani food",
      "🥗 Vegetarian restaurants",
      "☕ Coffee shops in F-7"
    ];

    // Context-aware suggestions based on conversation
    if (messages.length > 0) {
      const lastBotMessage = messages.filter(m => m.type === 'bot').slice(-1)[0];
      if (lastBotMessage?.restaurants && lastBotMessage.restaurants.length > 0) {
        return [
          "🗺️ Show me on map",
          "💰 Cheaper alternatives",
          "🍽️ Similar restaurants",
          "📋 Full menu details",
          "⭐ Higher rated options",
          "🚗 Closer locations"
        ];
      }

      // If user asked about specific cuisine, suggest related ones
      const lastUserMessage = messages.filter(m => m.type === 'user').slice(-1)[0];
      if (lastUserMessage?.content.toLowerCase().includes('pizza')) {
        return [
          "🍝 Italian restaurants",
          "🥪 Sandwich places",
          "🍔 Burger joints",
          "🌮 Fast food options"
        ];
      }
      if (lastUserMessage?.content.toLowerCase().includes('biryani')) {
        return [
          "🍛 Pulao places",
          "🍖 Karahi spots",
          "🥘 Pakistani cuisine",
          "🍚 Rice dishes"
        ];
      }
    }

    return baseChips;
  };

  const suggestionChips = getDynamicSuggestions();

  const handleSuggestionClick = (suggestion: string) => {
    const cleanSuggestion = suggestion.replace(/^[🍔🍕🍛🌶️🥗☕]\s/, '');
    setInputValue(cleanSuggestion);
    handleSendMessage(cleanSuggestion);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Modal Container - Full Screen */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="relative w-full h-full bg-white overflow-hidden flex flex-col"
          style={{
            background: 'linear-gradient(135deg, #FFF8F0 0%, #FFFFFF 100%)'
          }}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <RotiShotiAgent
                state={isTyping ? 'thinking' : 'idle'}
                size="small"
                showName={false}
              />
              <div>
                <h3 className="font-bold text-lg">RotiShoti Assistant</h3>
                <p className="text-orange-100 text-sm">
                  {currentUser ? `Hi ${currentUser.name}!` : 'Your AI Food Guide'}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
                title="Close"
              >
                <X size={18} />
              </button>
            </div>
          </div>

          {/* Chat Content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.length === 0 ? (
                <div className="text-center py-8">
                  <RotiShotiAgent
                    state="greeting"
                    size="large"
                    showName={true}
                    personality={(currentUser?.foodPersonality as 'friendly' | 'professional' | 'enthusiastic') || 'friendly'}
                  />
                  <div className="mt-6">
                    <h4 className="text-xl font-bold text-gray-800 mb-2">
                      {currentUser ? `Welcome back, ${currentUser.name}! 🍽️` : 'Welcome to RotiShoti! 🍽️'}
                    </h4>
                    <p className="text-gray-600 mb-6">
                      {currentUser?.foodPersonality === 'enthusiastic' ?
                        "OMG! I'm SO excited to help you find AMAZING food! 🚀" :
                        currentUser?.foodPersonality === 'professional' ?
                        "I'm your AI food consultant, ready to provide personalized dining recommendations." :
                        "I'm your AI food guide. Ask me about restaurants, cuisines, or get personalized recommendations!"
                      }
                    </p>

                    {/* Personalized Quick Stats */}
                    {currentUser && (
                      <div className="mb-4 p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border border-orange-200">
                        <p className="text-sm text-gray-700">
                          <span className="font-medium">Your preferences:</span> {' '}
                          {currentUser.preferences?.favoriteCuisines?.slice(0, 2).join(', ') || 'Pakistani, Fast Food'} • {' '}
                          Budget: {currentUser.preferences?.budgetRange ?
                            (currentUser.preferences.budgetRange[1] > 2000 ? 'High' :
                             currentUser.preferences.budgetRange[1] > 1000 ? 'Medium' : 'Low') : 'Medium'} • {' '}
                          {currentUser.preferences?.dietaryRestrictions?.length ?
                            `${currentUser.preferences.dietaryRestrictions.join(', ')}` :
                            'No dietary restrictions'
                          }
                        </p>
                      </div>
                    )}

                    {showSuggestions && (
                      <div className="space-y-3">
                        <p className="text-sm font-medium text-gray-700 mb-3">
                          {currentUser?.foodPersonality === 'enthusiastic' ?
                            "Let's find something AMAZING! Try:" :
                            currentUser?.foodPersonality === 'professional' ?
                            "Select a query to begin:" :
                            "Try asking:"
                          }
                        </p>
                        <div className="flex flex-wrap gap-2 justify-center">
                          {suggestionChips.map((chip, index) => (
                            <button
                              key={index}
                              onClick={() => handleSuggestionClick(chip)}
                              className="suggestion-chip px-3 py-2 bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 rounded-full text-sm font-medium hover:from-orange-200 hover:to-red-200 transition-all duration-200 border border-orange-200"
                            >
                              {chip}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                ) : (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start items-start gap-3'}`}
                    >
                      {/* Bot Avatar */}
                      {message.type === 'bot' && (
                        <div className="flex-shrink-0 mt-1">
                          <RotiShotiAgent
                            state={message.isTyping ? 'thinking' : 'responding'}
                            size="small"
                            showName={false}
                          />
                        </div>
                      )}

                      <div className={`max-w-[85%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                        {/* Message Bubble */}
                        <div
                          className={`p-3 rounded-2xl shadow-sm ${
                            message.type === 'user'
                              ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white'
                              : 'bg-white border border-gray-200 text-gray-800'
                          }`}
                        >
                          {message.isTyping ? (
                            <ThinkingAnimation
                              personality={(currentUser?.foodPersonality as 'friendly' | 'professional' | 'enthusiastic') || 'friendly'}
                              message={sessionStats.messageCount > 5 ? 'Finding even better options...' : undefined}
                            />
                          ) : (
                            <>
                              <p className="text-sm leading-relaxed">{message.content}</p>

                              {/* Dynamic Restaurant Cards */}
                              {message.restaurants && message.restaurants.length > 0 && (
                                <div className="mt-3">
                                  {message.restaurants.length === 1 ? (
                                    // Single restaurant - Large detailed card
                                    <div className="p-4 rounded-xl border-2 border-orange-200 bg-gradient-to-r from-orange-50 to-red-50 hover:border-orange-300 transition-all duration-300 transform hover:scale-[1.02]">
                                      <div className="flex justify-between items-start mb-3">
                                        <div className="flex-1">
                                          <h4 className="font-bold text-gray-900 text-base">{message.restaurants[0].name}</h4>
                                          <p className="text-sm text-gray-600 flex items-center gap-1">
                                            📍 {message.restaurants[0].area} • ⭐ {message.restaurants[0].rating}
                                          </p>
                                          {message.restaurants[0].cuisine_types && (
                                            <p className="text-xs text-orange-600 mt-1">
                                              {message.restaurants[0].cuisine_types.join(', ')}
                                            </p>
                                          )}
                                        </div>
                                      </div>

                                      <div className="mb-3 p-2 bg-white rounded-lg">
                                        <p className="text-sm font-semibold text-gray-800">{message.restaurants[0].item_name}</p>
                                        <p className="text-lg font-bold text-orange-600">Rs {message.restaurants[0].price}</p>
                                      </div>

                                      <div className="flex gap-2">
                                        <button
                                          onClick={() => message.restaurants?.[0] && handleRestaurantAction(message.restaurants[0], 'menu')}
                                          className="flex-1 bg-gradient-to-r from-orange-500 to-red-500 text-white py-2 px-3 rounded-lg text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-all duration-200"
                                        >
                                          📋 Full Menu
                                        </button>
                                        <button
                                          onClick={() => message.restaurants?.[0] && handleRestaurantAction(message.restaurants[0], 'directions')}
                                          className="flex-1 bg-gradient-to-r from-blue-500 to-indigo-500 text-white py-2 px-3 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-indigo-600 transition-all duration-200"
                                        >
                                          🗺️ Location
                                        </button>
                                      </div>
                                    </div>
                                  ) : (
                                    // Multiple restaurants - Compact cards
                                    <div className="space-y-2 max-h-64 overflow-y-auto">
                                      {message.restaurants.map((restaurant, index) => (
                                        <div
                                          key={index}
                                          className="p-3 rounded-lg border bg-white hover:bg-gray-50 transition-colors shadow-sm"
                                        >
                                          <div className="flex justify-between items-start mb-2">
                                            <div className="flex-1">
                                              <h4 className="font-semibold text-gray-800 text-sm">{restaurant.name}</h4>
                                              <p className="text-xs text-gray-600">📍 {restaurant.area} • ⭐ {restaurant.rating}</p>
                                            </div>
                                            <div className="text-right">
                                              <p className="text-xs font-medium text-gray-800">{restaurant.item_name}</p>
                                              <p className="text-sm font-bold text-orange-600">Rs {restaurant.price}</p>
                                            </div>
                                          </div>

                                          <div className="flex gap-2">
                                            <button
                                              onClick={() => handleRestaurantAction(restaurant, 'menu')}
                                              className="flex-1 bg-orange-500 text-white py-1.5 px-3 rounded-lg text-xs font-medium hover:bg-orange-600 transition-colors"
                                            >
                                              📋 Menu
                                            </button>
                                            <button
                                              onClick={() => handleRestaurantAction(restaurant, 'directions')}
                                              className="flex-1 bg-blue-500 text-white py-1.5 px-3 rounded-lg text-xs font-medium hover:bg-blue-600 transition-colors"
                                            >
                                              🗺️ Map
                                            </button>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              )}
                            </>
                          )}
                        </div>

                        {/* Timestamp */}
                        <p className="text-xs text-gray-500 mt-1 px-2">
                          {message.timestamp instanceof Date
                            ? message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                            : new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                          }
                        </p>
                      </div>
                    </div>
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Input Area */}
            <div className="p-4 bg-white border-t border-gray-200">
              <div className="flex gap-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
                  placeholder={`Ask me anything, ${currentUser?.name || 'Foodie'}...`}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                  disabled={isLoading}
                />
                <button
                  onClick={() => handleSendMessage()}
                  disabled={!inputValue.trim() || isLoading}
                  className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-4 py-2 rounded-xl hover:from-orange-600 hover:to-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>

              {messages.length > 0 && (
                <button
                  onClick={clearChat}
                  className="mt-2 text-xs text-gray-500 hover:text-gray-700 transition-colors"
                >
                  Clear conversation
                </button>
              )}
            </div>
          </div>

          {/* Restaurant Profile Modal */}
          {selectedRestaurant && (
            <RestaurantProfile
              restaurant={selectedRestaurant}
              onClose={() => setSelectedRestaurant(null)}
            />
          )}

          {/* Map Modal */}
          {showMap && mapRestaurant && (
            <UniversalMap
              restaurant={mapRestaurant}
              allRestaurants={allRestaurants}
              onClose={() => {
                setShowMap(false);
                setMapRestaurant(null);
              }}
              onRestaurantSelect={(restaurant) => {
                setSelectedRestaurant(restaurant);
                setShowMap(false);
                setMapRestaurant(null);
              }}
            />
          )}
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ChatModal;
