/**
 * RotiShoti AI - Professional Food Discovery Platform
 * AI by: <PERSON><PERSON><PERSON>, <PERSON><PERSON>
 * Redesigned with modern, professional foodie aesthetic
 */

'use client';

import React, { useState, useEffect } from 'react';
import Header from '@/components/layout/Header';
import ChatPanel from '@/components/ui/ChatPanel.v3';
import { WeatherWidget } from '@/components/ui/WeatherWidget';
import { RotiShotiAgent } from '@/components/ui/RotiShotiAgent';
import { useUserStore } from '@/store/userStore';
import { useChatStore } from '@/store/chatStore';
import AuthGuard from '@/components/auth/AuthGuard';
import '../styles/design-system.css';

export default function Home() {
  return (
    <AuthGuard>
      <HomePage />
    </AuthGuard>
  );
}

function HomePage() {
  const { currentUser } = useUserStore();
  const { setCurrentUser } = useChatStore();
  const [chatKey] = useState('main-chat'); // Persistent chat key
  const [trendingRestaurants, setTrendingRestaurants] = useState<any[]>([]);
  const [locationBasedSuggestions, setLocationBasedSuggestions] = useState(true);
  const [personalizedSuggestions, setPersonalizedSuggestions] = useState(true);

  // Set current user in chat store when user changes
  useEffect(() => {
    if (currentUser?.id) {
      setCurrentUser(currentUser.id);
    }
  }, [currentUser?.id, setCurrentUser]);

  // Fetch trending restaurants
  useEffect(() => {
    fetchTrendingRestaurants();
  }, []);

  const fetchTrendingRestaurants = async () => {
    try {
      // Add timeout and retry logic
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch('http://localhost:8000/api/v1/restaurants', {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        // Sort by rating to get trending restaurants
        const trending = data.sort((a: any, b: any) => (b.rating_overall || 0) - (a.rating_overall || 0));
        setTrendingRestaurants(trending);
      }
    } catch (error) {
      console.error('Error fetching trending restaurants:', error);
    }
  };





  return (
    <div className="min-h-screen bg-cream font-body">
      {/* Food Pattern Background */}
      <div className="fixed inset-0 opacity-5 pointer-events-none">
        <div className="absolute top-10 left-10 text-6xl">🍕</div>
        <div className="absolute top-32 right-20 text-4xl">🍔</div>
        <div className="absolute bottom-20 left-20 text-5xl">🍜</div>
        <div className="absolute bottom-32 right-10 text-4xl">🥘</div>
        <div className="absolute top-1/2 left-1/3 text-3xl">🍛</div>
        <div className="absolute top-1/3 right-1/3 text-4xl">🌮</div>
      </div>

      <Header />

      {/* Hero Section */}
      <section className="relative py-12 md:py-20">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-12">
            {/* RotiShoti AI Agent */}
            <div className="flex justify-center mb-8">
              <RotiShotiAgent state="idle" size="large" showName={true} />
            </div>

            {/* Welcome Message */}
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-6 text-chocolate">
              Hello, <span className="text-saffron font-bold">{currentUser?.name || 'Foodie'}</span>! 👋
            </h1>

            <p className="text-xl font-body mb-8 text-chocolate/80 max-w-2xl mx-auto">
              Your coolest, most food-obsessed AI friend who knows all the best spots in town.
              <br />
              <span className="font-semibold text-saffron">What are you craving today?</span>
            </p>



            {/* Weather Widget */}
            <div className="flex justify-center mb-12">
              <div className="w-full max-w-sm">
                <WeatherWidget />
              </div>
            </div>
          </div>

          {/* Main Chat Interface - PROMINENT SECTION */}
          <div className="max-w-7xl mx-auto mb-16">
            <div className="text-center mb-8">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                🤖 RotiShoti <span className="text-gradient bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">AI Assistant</span>
              </h2>

              {/* Smart Suggestion Toggles */}
              <div className="flex flex-wrap justify-center gap-4 mb-8">
                <button
                  onClick={() => setLocationBasedSuggestions(!locationBasedSuggestions)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
                    locationBasedSuggestions
                      ? 'bg-green-500 text-white shadow-lg'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  <span>{locationBasedSuggestions ? '📍' : '🌍'}</span>
                  Location-Based Suggestions
                  <span className={`w-4 h-4 rounded-full ${locationBasedSuggestions ? 'bg-white' : 'bg-gray-400'}`}>
                    {locationBasedSuggestions ? '✓' : ''}
                  </span>
                </button>

                <button
                  onClick={() => setPersonalizedSuggestions(!personalizedSuggestions)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
                    personalizedSuggestions
                      ? 'bg-blue-500 text-white shadow-lg'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  <span>{personalizedSuggestions ? '👤' : '👥'}</span>
                  Personalized Suggestions
                  <span className={`w-4 h-4 rounded-full ${personalizedSuggestions ? 'bg-white' : 'bg-gray-400'}`}>
                    {personalizedSuggestions ? '✓' : ''}
                  </span>
                </button>
              </div>
            </div>

            {/* AI Chat Preview Card */}
            <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border-4 border-orange-200">
              <div className="bg-gradient-to-r from-red-500 via-orange-500 to-yellow-500 text-white p-6">
                <div className="flex items-center justify-center gap-3 mb-4">
                  <span className="text-3xl">🤖</span>
                  <h3 className="text-2xl font-bold">RotiShoti AI Assistant</h3>
                  <span className="text-3xl">🍽️</span>
                </div>
                <p className="text-center text-orange-100">
                  Your intelligent food guide powered by advanced AI
                </p>
              </div>

              <div className="p-8 text-center">
                <div className="mb-6">
                  <div className="w-20 h-20 bg-gradient-to-r from-orange-100 to-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">💬</span>
                  </div>
                  <h4 className="text-xl font-bold text-gray-800 mb-2">
                    Chat with RotiShoti AI
                  </h4>
                  <p className="text-gray-600 mb-6">
                    Get personalized restaurant recommendations, discover new cuisines, and find the perfect meal for any occasion.
                  </p>
                </div>

                <button
                  onClick={() => {
                    // This will be handled by the FloatingChatButton
                    const event = new CustomEvent('openChat', { detail: { query: '' } });
                    window.dispatchEvent(event);
                  }}
                  className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-orange-600 hover:to-red-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  Start Chatting 🚀
                </button>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-3">
                  {[
                    "🍔 Best burgers in Islamabad",
                    "🍕 Pizza places near me",
                    "🍛 Biryani under Rs 1000",
                    "🌶️ Spicy Pakistani food"
                  ].map((query, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        const cleanQuery = query.replace(/^[🍔🍕🍛🌶️]\s/, '');
                        const event = new CustomEvent('openChat', { detail: { query: cleanQuery } });
                        window.dispatchEvent(event);
                      }}
                      className="px-4 py-2 bg-gradient-to-r from-orange-50 to-red-50 text-orange-800 rounded-xl text-sm font-medium hover:from-orange-100 hover:to-red-100 transition-all duration-200 border border-orange-200 hover:border-orange-300"
                    >
                      {query}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Example Queries */}
            <div className="mt-8 text-center">
              <p className="text-gray-600 mb-4">Try asking me:</p>
              <div className="flex flex-wrap justify-center gap-3">
                {[
                  "Where is De Flambe?",
                  "Who made RotiShoti?",
                  "Best biryani under Rs 1000",
                  "Restaurants near F-7",
                  "What's good for dinner?"
                ].map((query, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      // Auto-fill chat with query
                      const event = new CustomEvent('fillChatQuery', { detail: query });
                      window.dispatchEvent(event);
                    }}
                    className="bg-orange-100 hover:bg-orange-200 text-orange-700 px-4 py-2 rounded-full text-sm font-medium transition-colors"
                  >
                    "{query}"
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Trending Restaurants Section */}
          {trendingRestaurants.length > 0 && (
            <div className="max-w-7xl mx-auto mb-12">
              <div className="text-center mb-8">
                <h2 className="text-4xl font-bold text-gray-900 mb-4">
                  🔥 Trending <span className="text-gradient bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">Restaurants</span>
                </h2>
                <p className="text-xl text-gray-600">Discover the most popular dining spots in Islamabad</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {trendingRestaurants.map((restaurant, index) => (
                  <div
                    key={restaurant.id}
                    className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:scale-105 relative"
                  >
                    {/* Trending Badge */}
                    {index < 3 && (
                      <div className="absolute top-4 left-4 z-10 bg-gradient-to-r from-red-500 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                        #{index + 1} Trending
                      </div>
                    )}

                    {/* Restaurant Image */}
                    <div className="h-40 bg-gradient-to-br from-orange-400 to-red-500 relative">
                      {restaurant.image_url ? (
                        <img
                          src={restaurant.image_url}
                          alt={restaurant.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <span className="text-5xl text-white">🍽️</span>
                        </div>
                      )}
                    </div>

                    {/* Restaurant Info */}
                    <div className="p-5">
                      <h3 className="text-lg font-bold text-gray-900 mb-2">{restaurant.name}</h3>

                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-yellow-500 font-semibold">⭐ {restaurant.rating_overall || 4.0}/5</span>
                        <span className="text-gray-600 text-sm">{restaurant.cuisine_types?.join(', ') || 'Pakistani'}</span>
                      </div>

                      <p className="text-gray-600 mb-3 text-sm flex items-center gap-2">
                        <span>📍</span> {restaurant.area || 'Islamabad'}
                      </p>

                      <div className="flex items-center justify-between">
                        <span className="text-green-600 font-semibold text-sm">
                          Rs {restaurant.price_range_min || 500} - Rs {restaurant.price_range_max || 2000}
                        </span>
                        <button
                          onClick={() => window.open(`/restaurants/${restaurant.id}`, '_blank')}
                          className="bg-gradient-to-r from-orange-500 to-red-500 text-white py-2 px-4 rounded-lg text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-all duration-200"
                        >
                          View Menu
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="text-center mt-8">
                <button
                  onClick={() => window.open('/restaurants', '_blank')}
                  className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-8 py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-200 hover:scale-105"
                >
                  View All Restaurants →
                </button>
              </div>
            </div>
          )}


        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-display-md mb-4" style={{color: 'var(--gray-800)'}}>
              Why Choose <span className="text-gradient">RotiShoti</span>?
            </h2>
            <p className="text-body-lg" style={{color: 'var(--gray-600)'}}>
              Your personal food discovery companion powered by AI
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="card text-center p-8">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full flex-center" style={{background: 'var(--primary-100)'}}>
                <span className="text-2xl">🤖</span>
              </div>
              <h3 className="text-display-sm mb-4" style={{color: 'var(--gray-800)'}}>AI-Powered</h3>
              <p className="text-body" style={{color: 'var(--gray-600)'}}>
                Smart recommendations based on your taste preferences and budget
              </p>
            </div>

            <div className="card text-center p-8">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full flex-center" style={{background: 'var(--accent-green-light)'}}>
                <span className="text-2xl">📍</span>
              </div>
              <h3 className="text-display-sm mb-4" style={{color: 'var(--gray-800)'}}>Local Focus</h3>
              <p className="text-body" style={{color: 'var(--gray-600)'}}>
                Discover the best halal food spots in your area with real-time updates
              </p>
            </div>

            <div className="card text-center p-8">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full flex-center" style={{background: 'var(--accent-yellow-light)'}}>
                <span className="text-2xl">💰</span>
              </div>
              <h3 className="text-display-sm mb-4" style={{color: 'var(--gray-800)'}}>Budget-Friendly</h3>
              <p className="text-body" style={{color: 'var(--gray-600)'}}>
                Find amazing food within your budget range, from street food to fine dining
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer Section */}
      <footer className="py-12" style={{background: 'var(--gray-100)'}}>
        <div className="container text-center">
          <div className="mb-6">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-white rounded-full" style={{boxShadow: 'var(--shadow-md)'}}>
              <span>🇵🇰</span>
              <span className="text-gradient font-semibold">پاکستانی کھانوں کا بہترین تجربہ</span>
              <span>🇵🇰</span>
            </div>
          </div>
          <p className="text-body-sm" style={{color: 'var(--gray-600)'}}>
            AI by <span className="font-semibold">Masab Farooque</span> & <span className="font-semibold">Saad Ilyas</span>
          </p>
        </div>
      </footer>



    </div>
  );
}
