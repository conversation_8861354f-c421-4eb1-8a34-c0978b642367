'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { UniversalMap } from '@/components/maps/UniversalMap';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  is_vegetarian?: boolean;
  spice_level?: string;
}

interface Restaurant {
  id: string;
  name: string;
  cuisine_types: string[];
  area: string;
  address: string;
  phone?: string;
  rating_overall: number;
  price_range_min: number;
  price_range_max: number;
  image_url?: string;
  features: string[];
  latitude: number;
  longitude: number;
  ambiance_type: string;
  menu_items?: MenuItem[];
}

const RestaurantDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'menu' | 'location'>('overview');
  const [showMap, setShowMap] = useState(false);

  useEffect(() => {
    if (params.id) {
      fetchRestaurantDetails(params.id as string);
    }
  }, [params.id]);

  const fetchRestaurantDetails = async (restaurantId: string) => {
    try {
      // Fetch restaurant details by ID
      const restaurantResponse = await fetch(`http://localhost:8000/api/v1/restaurants/${restaurantId}`);
      if (restaurantResponse.ok) {
        const restaurantData = await restaurantResponse.json();
        setRestaurant(restaurantData);

        // Menu items are included in the restaurant response
        if (restaurantData.menu_items) {
          setMenuItems(restaurantData.menu_items);
        }
      } else {
        console.error('Restaurant not found');
      }
    } catch (error) {
      console.error('Error fetching restaurant details:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRestaurantImage = (name: string) => {
    const imageIds = [
      'UxRhrU8fPHQ', 'ZuIDLSz3XLg', 'N_Y88TWmGwA', 'jpkfc5_d-DI',
      'lP5MCM6nZ5A', 'MQUqbmszGGM', 'dphM2U1xq0U', 'IGfIGP5ONV0'
    ];
    const randomId = imageIds[Math.abs(name.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % imageIds.length];
    return `https://images.unsplash.com/${randomId}?w=800&h=400&fit=crop`;
  };

  // Group menu items by category
  const groupedMenuItems = menuItems.reduce((acc, item) => {
    const category = item.category || 'Other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(item);
    return acc;
  }, {} as Record<string, MenuItem[]>);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600 text-lg">Loading restaurant details...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!restaurant) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="text-6xl mb-4">🍽️</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Restaurant not found</h2>
            <p className="text-gray-600 mb-6">The restaurant you're looking for doesn't exist.</p>
            <button
              onClick={() => router.push('/restaurants')}
              className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-6 py-3 rounded-xl hover:from-red-600 hover:to-orange-600 transition-all duration-200 font-medium"
            >
              Browse All Restaurants
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="bg-white rounded-3xl shadow-2xl overflow-hidden mb-8">
          <div className="relative h-64 md:h-80">
            <img
              src={restaurant.image_url || getRestaurantImage(restaurant.name)}
              alt={restaurant.name}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40"></div>
            <div className="absolute bottom-6 left-6 text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-2">{restaurant.name}</h1>
              <div className="flex items-center gap-4 text-lg">
                <span>⭐ {restaurant.rating_overall}/5</span>
                <span>📍 {restaurant.area}</span>
                <span>💰 Rs {restaurant.price_range_min}-{restaurant.price_range_max}</span>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: 'Overview', icon: '🏪' },
                { id: 'menu', label: 'Menu', icon: '📋' },
                { id: 'location', label: 'Location', icon: '📍' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">About {restaurant.name}</h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Cuisine Types</h4>
                      <div className="flex flex-wrap gap-2">
                        {restaurant.cuisine_types.map((cuisine, index) => (
                          <span
                            key={index}
                            className="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm font-medium"
                          >
                            {cuisine}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Features</h4>
                      <div className="flex flex-wrap gap-2">
                        {restaurant.features.map((feature, index) => (
                          <span
                            key={index}
                            className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Contact Information</h4>
                      <p className="text-gray-600">📍 {restaurant.address}</p>
                      {restaurant.phone && <p className="text-gray-600">📞 {restaurant.phone}</p>}
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Quick Stats</h3>
                  <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-orange-600">{restaurant.rating_overall}/5</div>
                        <div className="text-sm text-gray-600">Overall Rating</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-orange-600">{restaurant.ambiance_type}</div>
                        <div className="text-sm text-gray-600">Ambiance</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'menu' && (
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Menu</h3>
                {Object.keys(groupedMenuItems).length > 0 ? (
                  <div className="space-y-8">
                    {Object.entries(groupedMenuItems).map(([category, items]) => (
                      <div key={category}>
                        <h4 className="text-xl font-bold text-gray-900 mb-4 border-b border-orange-200 pb-2">
                          {category}
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {items.map((item) => (
                            <div key={item.id} className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                              <div className="flex justify-between items-start mb-2">
                                <h5 className="font-semibold text-gray-900">{item.name}</h5>
                                <span className="text-lg font-bold text-orange-600">Rs {item.price}</span>
                              </div>
                              {item.description && (
                                <p className="text-gray-600 text-sm mb-2">{item.description}</p>
                              )}
                              <div className="flex items-center gap-2">
                                {item.is_vegetarian && (
                                  <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs">
                                    🌱 Vegetarian
                                  </span>
                                )}
                                {item.spice_level && (
                                  <span className="bg-red-100 text-red-700 px-2 py-1 rounded-full text-xs">
                                    🌶️ {item.spice_level}
                                  </span>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-4xl mb-4">📋</div>
                    <p className="text-gray-600">Menu information not available</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'location' && (
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Location & Directions</h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  <div className="lg:col-span-2">
                    <div className="bg-white rounded-2xl overflow-hidden shadow-lg h-96">
                      <button
                        onClick={() => setShowMap(true)}
                        className="w-full h-full bg-gradient-to-br from-blue-50 to-green-50 hover:from-blue-100 hover:to-green-100 transition-all duration-200 flex items-center justify-center group"
                      >
                        <div className="text-center">
                          <div className="text-6xl mb-4 group-hover:scale-110 transition-transform">🗺️</div>
                          <h4 className="text-xl font-bold text-gray-900 mb-2">Interactive Map</h4>
                          <p className="text-gray-600">Click to view location and nearby restaurants</p>
                        </div>
                      </button>
                    </div>
                  </div>
                  <div>
                    <div className="bg-white rounded-2xl p-6 shadow-lg">
                      <h4 className="font-bold text-gray-900 mb-4">Address</h4>
                      <p className="text-gray-600 mb-4">{restaurant.address}</p>
                      
                      {restaurant.phone && (
                        <>
                          <h4 className="font-bold text-gray-900 mb-2">Phone</h4>
                          <p className="text-gray-600 mb-4">{restaurant.phone}</p>
                        </>
                      )}

                      <div className="space-y-3">
                        <button
                          onClick={() => {
                            const url = `https://www.google.com/maps/dir/?api=1&destination=${restaurant.latitude || 33.6844},${restaurant.longitude || 73.0479}`;
                            window.open(url, '_blank');
                          }}
                          className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium"
                        >
                          🧭 Get Directions
                        </button>
                        
                        {restaurant.phone && (
                          <button
                            onClick={() => window.open(`tel:${restaurant.phone}`, '_self')}
                            className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium"
                          >
                            📞 Call Restaurant
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Universal Map Modal */}
      {showMap && restaurant && (
        <UniversalMap
          restaurant={{
            id: restaurant.id,
            name: restaurant.name,
            address: restaurant.address,
            latitude: restaurant.latitude,
            longitude: restaurant.longitude,
            rating: restaurant.rating_overall,
            cuisine_types: restaurant.cuisine_types,
            price_range_min: restaurant.price_range_min,
            price_range_max: restaurant.price_range_max,
            phone: restaurant.phone
          }}
          onClose={() => setShowMap(false)}
        />
      )}

      <Footer />
    </div>
  );
};

export default RestaurantDetailPage;
