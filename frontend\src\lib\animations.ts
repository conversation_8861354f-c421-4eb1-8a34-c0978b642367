/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Authors: <AUTHORS>
 * Module: Professional animation utilities and configurations
 */

// Animation duration constants
export const ANIMATION_DURATION = {
  fast: 150,
  normal: 300,
  slow: 500,
  slower: 750,
} as const;

// Easing functions
export const EASING = {
  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
} as const;

// Common animation classes
export const ANIMATION_CLASSES = {
  // Fade animations
  fadeIn: 'animate-fade-in',
  fadeOut: 'animate-fade-out',
  fadeInUp: 'animate-fade-in-up',
  fadeInDown: 'animate-fade-in-down',
  fadeInLeft: 'animate-fade-in-left',
  fadeInRight: 'animate-fade-in-right',

  // Scale animations
  scaleIn: 'animate-scale-in',
  scaleOut: 'animate-scale-out',
  pulse: 'animate-pulse',
  bounce: 'animate-bounce',

  // Slide animations
  slideInUp: 'animate-slide-in-up',
  slideInDown: 'animate-slide-in-down',
  slideInLeft: 'animate-slide-in-left',
  slideInRight: 'animate-slide-in-right',

  // Rotation animations
  spin: 'animate-spin',
  ping: 'animate-ping',

  // Custom animations
  float: 'animate-float',
  glow: 'animate-glow',
  shimmer: 'animate-shimmer',
  typewriter: 'animate-typewriter',
} as const;

// Animation delay classes
export const ANIMATION_DELAYS = {
  none: 'delay-0',
  xs: 'delay-75',
  sm: 'delay-100',
  md: 'delay-150',
  lg: 'delay-200',
  xl: 'delay-300',
  '2xl': 'delay-500',
  '3xl': 'delay-700',
  '4xl': 'delay-1000',
} as const;

// Stagger animation helper
export const getStaggerDelay = (index: number, baseDelay: number = 100): string => {
  const delay = index * baseDelay;
  return `delay-[${delay}ms]`;
};

// Page transition variants
export const PAGE_TRANSITIONS = {
  fadeSlide: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3, ease: 'easeInOut' },
  },
  scale: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 1.05 },
    transition: { duration: 0.2, ease: 'easeOut' },
  },
  slide: {
    initial: { x: '100%' },
    animate: { x: 0 },
    exit: { x: '-100%' },
    transition: { duration: 0.3, ease: 'easeInOut' },
  },
} as const;

// Component animation variants
export const COMPONENT_ANIMATIONS = {
  card: {
    hover: {
      scale: 1.02,
      y: -4,
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      transition: { duration: 0.2, ease: 'easeOut' },
    },
    tap: {
      scale: 0.98,
      transition: { duration: 0.1 },
    },
  },
  button: {
    hover: {
      scale: 1.05,
      transition: { duration: 0.2, ease: 'easeOut' },
    },
    tap: {
      scale: 0.95,
      transition: { duration: 0.1 },
    },
  },
  modal: {
    overlay: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.2 },
    },
    content: {
      initial: { opacity: 0, scale: 0.95, y: 20 },
      animate: { opacity: 1, scale: 1, y: 0 },
      exit: { opacity: 0, scale: 0.95, y: 20 },
      transition: { duration: 0.2, ease: 'easeOut' },
    },
  },
  list: {
    container: {
      animate: {
        transition: {
          staggerChildren: 0.1,
        },
      },
    },
    item: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -20 },
      transition: { duration: 0.3, ease: 'easeOut' },
    },
  },
} as const;

// Loading animation variants
export const LOADING_ANIMATIONS = {
  spinner: {
    animate: {
      rotate: 360,
      transition: {
        duration: 1,
        repeat: Infinity,
        ease: 'linear',
      },
    },
  },
  dots: {
    animate: {
      scale: [1, 1.2, 1],
      transition: {
        duration: 0.6,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  },
  pulse: {
    animate: {
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  },
} as const;

// Chat animation variants
export const CHAT_ANIMATIONS = {
  message: {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: { opacity: 1, y: 0, scale: 1 },
    transition: { duration: 0.3, ease: 'easeOut' },
  },
  typing: {
    animate: {
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  },
  bubble: {
    initial: { scale: 0, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    transition: { 
      type: 'spring',
      stiffness: 500,
      damping: 30,
    },
  },
} as const;

// Utility functions for animations
export const createStaggerAnimation = (
  children: number,
  staggerDelay: number = 0.1
) => ({
  animate: {
    transition: {
      staggerChildren: staggerDelay,
    },
  },
});

export const createSlideAnimation = (
  direction: 'up' | 'down' | 'left' | 'right' = 'up',
  distance: number = 20
) => {
  const directions = {
    up: { y: distance },
    down: { y: -distance },
    left: { x: distance },
    right: { x: -distance },
  };

  return {
    initial: { opacity: 0, ...directions[direction] },
    animate: { opacity: 1, x: 0, y: 0 },
    exit: { opacity: 0, ...directions[direction] },
    transition: { duration: 0.3, ease: 'easeOut' },
  };
};

export const createBounceAnimation = (scale: number = 1.1) => ({
  animate: {
    scale: [1, scale, 1],
    transition: {
      duration: 0.6,
      ease: 'easeInOut',
    },
  },
});

// Pakistani-themed animations
export const PAKISTANI_ANIMATIONS = {
  flag: {
    wave: {
      animate: {
        rotate: [0, 5, -5, 0],
        transition: {
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut',
        },
      },
    },
  },
  crescent: {
    glow: {
      animate: {
        boxShadow: [
          '0 0 5px rgba(34, 197, 94, 0.5)',
          '0 0 20px rgba(34, 197, 94, 0.8)',
          '0 0 5px rgba(34, 197, 94, 0.5)',
        ],
        transition: {
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut',
        },
      },
    },
  },
} as const;

// Export animation class names for Tailwind CSS
export const TAILWIND_ANIMATIONS = `
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes fade-in-up {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  @keyframes scale-in {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
  }
  
  @keyframes slide-in-up {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  @keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(251, 146, 60, 0.5); }
    50% { box-shadow: 0 0 20px rgba(251, 146, 60, 0.8); }
  }
  
  @keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
  }
  
  .animate-fade-in { animation: fade-in 0.3s ease-out; }
  .animate-fade-in-up { animation: fade-in-up 0.3s ease-out; }
  .animate-scale-in { animation: scale-in 0.2s ease-out; }
  .animate-slide-in-up { animation: slide-in-up 0.3s ease-out; }
  .animate-float { animation: float 3s ease-in-out infinite; }
  .animate-glow { animation: glow 2s ease-in-out infinite; }
  .animate-shimmer { animation: shimmer 2s linear infinite; }
`;
