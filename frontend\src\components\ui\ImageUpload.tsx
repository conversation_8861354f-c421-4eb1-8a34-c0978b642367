"use client";

import { useState, useRef } from 'react';
import { Camera, Upload, X } from 'lucide-react';

interface ImageUploadProps {
  currentImage?: string;
  onImageChange: (imageUrl: string) => void;
  className?: string;
}

export default function ImageUpload({ currentImage, onImageChange, className = "" }: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(currentImage || "");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size should be less than 5MB');
      return;
    }

    setIsUploading(true);

    try {
      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setPreviewUrl(result);
        onImageChange(result);
      };
      reader.readAsDataURL(file);

      // TODO: In a real app, you would upload to a cloud service like AWS S3, Cloudinary, etc.
      // For now, we'll use the base64 data URL
      
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setPreviewUrl("");
    onImageChange("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`relative ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />
      
      <div className="relative inline-block">
        <img
          src={previewUrl || "/default-avatar.svg"}
          alt="Profile"
          className="w-32 h-32 rounded-full object-cover border-4 border-orange-200 shadow-lg"
        />
        
        {/* Upload Button */}
        <button
          type="button"
          onClick={triggerFileInput}
          disabled={isUploading}
          className="absolute bottom-0 right-0 bg-orange-500 text-white rounded-full p-3 hover:bg-orange-600 transition-colors shadow-lg disabled:opacity-50"
        >
          {isUploading ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <Camera className="w-4 h-4" />
          )}
        </button>

        {/* Remove Button */}
        {previewUrl && previewUrl !== "/default-avatar.svg" && (
          <button
            type="button"
            onClick={handleRemoveImage}
            className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors shadow-lg"
          >
            <X className="w-3 h-3" />
          </button>
        )}
      </div>

      {/* Upload Instructions */}
      <div className="mt-3 text-center">
        <button
          type="button"
          onClick={triggerFileInput}
          disabled={isUploading}
          className="text-sm text-orange-600 hover:text-orange-700 font-medium disabled:opacity-50"
        >
          {isUploading ? 'Uploading...' : 'Change Photo'}
        </button>
        <p className="text-xs text-gray-500 mt-1">
          JPG, PNG up to 5MB
        </p>
      </div>
    </div>
  );
}
