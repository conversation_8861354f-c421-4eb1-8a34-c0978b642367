'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useUserStore } from '@/store/userStore';
import '../../styles/design-system.css';
import { RestaurantProfile } from '../restaurant/RestaurantProfile';
import { UniversalMap } from '../maps/UniversalMap';
import { RotiShotiAgent, ThinkingAnimation } from './RotiShotiAgent';

interface RestaurantCard {
  id?: string;
  name: string;
  area: string;
  address: string;
  rating: string;
  item_name: string;
  price: string;
  map_embed_url: string;
  latitude?: number;
  longitude?: number;
  cuisine_types?: string[];
  phone?: string;
  price_range_min?: number;
  price_range_max?: number;
  image_url?: string;
}

interface Message {
  id: string;
  type: 'user' | 'bot' | 'system';
  content: string;
  timestamp: Date;
  isTyping?: boolean;
  restaurants?: RestaurantCard[];
  confidence?: number;
  factChecked?: boolean;
  accuracyScore?: number;
  primaryModel?: string;
}

interface ChatPanelProps {
  isOpen?: boolean;
  onClose?: () => void;
  isEmbedded?: boolean;
  chatKey?: string; // For persistent state
  className?: string;
  locationBased?: boolean;
  personalized?: boolean;
  initialQuery?: string;
}

const ChatPanel: React.FC<ChatPanelProps> = ({
  isOpen = true,
  onClose,
  isEmbedded = false,
  chatKey = 'default',
  className = '',
  locationBased = true,
  personalized = true,
  initialQuery = ''
}) => {
  const { currentUser } = useUserStore();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [selectedRestaurant, setSelectedRestaurant] = useState<any>(null);
  const [showMap, setShowMap] = useState(false);
  const [mapRestaurant, setMapRestaurant] = useState<any>(null);
  const [allRestaurants, setAllRestaurants] = useState<any[]>([]);
  const [menuItems, setMenuItems] = useState<any[]>([]);
  const [branches, setBranches] = useState<any[]>([]);
  const [userLocation, setUserLocation] = useState<{latitude: number, longitude: number} | null>(null);

  // Load chat state from localStorage
  useEffect(() => {
    const savedMessages = localStorage.getItem(`chat-${chatKey}`);
    if (savedMessages) {
      try {
        const parsed = JSON.parse(savedMessages);
        const restoredMessages = parsed.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        setMessages(restoredMessages);
        setShowSuggestions(restoredMessages.length === 0);
        return; // Don't trigger welcome message if we have saved messages
      } catch (error) {
        console.error('Error loading chat state:', error);
      }
    }

    // Only show welcome message if no saved messages and we have a user
    if (currentUser && messages.length === 0) {
      const welcomeMessage: Message = {
        id: 'welcome-' + Date.now(),
        type: 'system',
        content: `Hello ${currentUser.name}! 👋 What type of food are you looking for today?`,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [chatKey, currentUser]);

  // Listen for auto-fill query events
  useEffect(() => {
    const handleFillQuery = (event: CustomEvent) => {
      setInputValue(event.detail);
      setShowSuggestions(false);
    };

    window.addEventListener('fillChatQuery', handleFillQuery as EventListener);
    return () => {
      window.removeEventListener('fillChatQuery', handleFillQuery as EventListener);
    };
  }, []);

  // Get user location if location-based suggestions are enabled
  useEffect(() => {
    if (locationBased && !userLocation) {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            setUserLocation({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude
            });
          },
          (error) => {
            console.log('Location access denied or failed:', error);
            // Don't show error to user, just continue without location
          },
          {
            enableHighAccuracy: false,
            timeout: 10000,
            maximumAge: 300000 // 5 minutes
          }
        );
      }
    }
  }, [locationBased, userLocation]);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Handle restaurant actions
  const handleRestaurantAction = async (restaurant: RestaurantCard, action: 'menu' | 'directions') => {
    if (action === 'menu') {
      try {
        // First try to fetch by restaurant ID if available
        let restaurantData = null;

        if (restaurant.id) {
          const response = await fetch(`http://localhost:8000/api/v1/restaurants/${restaurant.id}`);
          if (response.ok) {
            restaurantData = await response.json();
          }
        }

        // If no ID or fetch failed, try to find by name from database
        if (!restaurantData) {
          const searchResponse = await fetch(`http://localhost:8000/api/v1/restaurants/`);
          if (searchResponse.ok) {
            const allRestaurants = await searchResponse.json();
            restaurantData = allRestaurants.find((r: any) =>
              r.name.toLowerCase() === restaurant.name.toLowerCase()
            );
          }
        }

        if (restaurantData) {
          setSelectedRestaurant(restaurantData);
        } else {
          // Fallback: create basic restaurant object with available data
          setSelectedRestaurant({
            id: restaurant.id || restaurant.name.toLowerCase().replace(/\s+/g, '-'),
            name: restaurant.name,
            area: restaurant.area,
            address: restaurant.address,
            latitude: restaurant.latitude || 33.6844,
            longitude: restaurant.longitude || 73.0479,
            rating_overall: parseFloat(restaurant.rating.replace('/5', '')) || 4.0,
            price_range_min: restaurant.price_range_min || 200,
            price_range_max: restaurant.price_range_max || 2000,
            price_range_average: Math.floor(((restaurant.price_range_min || 200) + (restaurant.price_range_max || 2000)) / 2),
            cuisine_types: restaurant.cuisine_types || ['Pakistani'],
            ambiance_type: 'Casual Dining',
            features: ['Dine-in', 'Takeaway'],
            phone: restaurant.phone || '051-1234567',
            image_url: restaurant.image_url
          });
        }
      } catch (error) {
        console.error('Error fetching restaurant details:', error);
        // Show error message to user
        alert('Unable to load restaurant menu. Please try again.');
      }
    } else if (action === 'directions') {
      // Convert restaurant card to map format with real data
      let latitude = restaurant.latitude;
      let longitude = restaurant.longitude;

      // If coordinates are missing, try to fetch from database
      if (!latitude || !longitude) {
        try {
          if (restaurant.id) {
            const response = await fetch(`http://localhost:8000/api/v1/restaurants/${restaurant.id}`);
            if (response.ok) {
              const restaurantData = await response.json();
              latitude = restaurantData.latitude;
              longitude = restaurantData.longitude;
            }
          }
        } catch (error) {
          console.error('Error fetching restaurant coordinates:', error);
        }
      }

      // If still no coordinates, use default Islamabad coordinates and show warning
      if (!latitude || !longitude) {
        console.warn(`Restaurant ${restaurant.name} has no coordinates, using default location`);
        latitude = 33.6844; // Default Islamabad latitude
        longitude = 73.0479; // Default Islamabad longitude
        alert(`Location data for ${restaurant.name} is not available. Showing approximate location in Islamabad.`);
      }

      const mapRestaurantData = {
        id: restaurant.id || `restaurant-${Date.now()}`,
        name: restaurant.name,
        address: restaurant.address || `${restaurant.area}, Islamabad`,
        latitude: latitude,
        longitude: longitude,
        rating: parseFloat(restaurant.rating.replace('/5', '')) || 4.0,
        cuisine_types: restaurant.cuisine_types || ['Pakistani', 'Desi'],
        price_range_min: restaurant.price_range_min || 500,
        price_range_max: restaurant.price_range_max || 2000,
        phone: restaurant.phone || '051-1234567',
        image_url: restaurant.image_url
      };

      // Get all restaurants from current messages for context
      const currentRestaurants = messages
        .filter(msg => msg.restaurants && msg.restaurants.length > 0)
        .flatMap(msg => msg.restaurants)
        .filter(r => r != null)
        .map(r => ({
          id: r.id || `restaurant-${Date.now()}-${Math.random()}`,
          name: r.name || 'Unknown Restaurant',
          address: r.address || `${r.area || 'Islamabad'}, Islamabad`,
          latitude: r.latitude || 33.6844, // Use default if missing
          longitude: r.longitude || 73.0479, // Use default if missing
          rating: parseFloat((r.rating || '4.0/5').replace('/5', '')) || 4.0,
          cuisine_types: r.cuisine_types || ['Pakistani', 'Desi'],
          price_range_min: r.price_range_min || 500,
          price_range_max: r.price_range_max || 2000,
          phone: r.phone || '051-1234567',
          image_url: r.image_url
        }));

      setMapRestaurant(mapRestaurantData);
      setAllRestaurants(currentRestaurants);
      setShowMap(true);
    }
  };

  // Dynamic placeholders for engaging UX
  const placeholders = [
    `Ask me anything about food, ${currentUser?.name || 'Foodie'}...`,
    `What's your craving today, ${currentUser?.name || 'Foodie'}?`,
    `Looking for something delicious, ${currentUser?.name || 'Foodie'}?`,
    `Tell me what you're hungry for, ${currentUser?.name || 'Foodie'}!`
  ];
  const [currentPlaceholder, setCurrentPlaceholder] = useState(0);

  // Enhanced quick prompts - let AI handle all responses with vector search
  const quickPrompts = [
    {
      id: 'burgers',
      label: '🍔 Burgers',
      query: 'Show me the best burgers available',
      response: null // Let AI handle with vector search
    },
    {
      id: 'biryani',
      label: '🍛 Biryani & Rice',
      query: 'I want biryani or rice dishes',
      response: null // Let AI handle with vector search
    },
    {
      id: 'karahi',
      label: '🍲 Karahi & Curry',
      query: 'Show me karahi and curry options',
      response: null // Let AI handle with vector search
    },
    {
      id: 'bbq',
      label: '🔥 BBQ & Grills',
      query: 'I want BBQ and grilled items',
      response: null // Let AI handle with vector search
    },
    {
      id: 'pizza',
      label: '🍕 Pizza & Fast Food',
      query: 'Show me pizza and fast food options',
      response: null // Let AI handle with vector search
    },
    {
      id: 'desserts',
      label: '🍰 Desserts & Sweets',
      query: 'I want desserts and sweet items',
      response: null // Let AI handle with vector search
    },
    {
      id: 'budget',
      label: '💰 Under Rs 1000',
      query: 'Show me options under Rs 1000',
      response: null // Let AI handle with vector search
    },
    {
      id: 'premium',
      label: '⭐ Premium (Rs 3000+)',
      query: 'Show me premium dining options above Rs 3000',
      response: null // Let AI handle with vector search
    }
  ];

  // Save chat state to localStorage whenever messages change
  useEffect(() => {
    if (messages.length > 0) {
      localStorage.setItem(`chat-${chatKey}`, JSON.stringify(messages));
    }
  }, [messages, chatKey]);

  // Only auto-scroll for bot messages, not user messages
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.type === 'bot' && !lastMessage.isTyping) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  }, [messages]);

  // Rotate placeholder text for engagement
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPlaceholder((prev) => (prev + 1) % placeholders.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [placeholders.length]);

  // Handle quick prompt selection - use AI for all responses
  const handleQuickPrompt = (prompt: typeof quickPrompts[0]) => {
    setShowSuggestions(false);
    setInputValue(prompt.query);

    // Automatically send the query to AI
    setTimeout(() => {
      handleSendMessage();
    }, 100);
  };

  // Handle manual message send
  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;
    
    setShowSuggestions(false);
    setIsLoading(true);
    
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    
    try {
      // Call actual API (FastAPI backend)
      const response = await fetch('http://localhost:8000/api/v1/chat/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_query: inputValue,
          location: 'Islamabad',
          user_location: userLocation,
          budget: currentUser?.preferences?.budgetRange ?
            (currentUser.preferences.budgetRange[1] > 2000 ? 'high' :
             currentUser.preferences.budgetRange[1] > 1000 ? 'medium' : 'low') : 'medium',
          user_profile: currentUser ? {
            name: currentUser.name || 'User',
            food_personality: currentUser.foodPersonality || 'adventurous',
            favorite_cuisines: currentUser.preferences?.favoriteCuisines || ['Pakistani'],
            spice_level: currentUser.preferences?.spiceLevel || 'medium',
            dining_style: currentUser.preferences?.diningStyle || ['casual'],
            taste_profile: currentUser.tasteProfile || {},
            budget_range: currentUser.preferences?.budgetRange || [500, 2500]
          } : null,
          conversation_history: messages
            .filter(msg => msg.type !== 'system' && !msg.isTyping) // Exclude system messages and typing indicators
            .slice(-10) // Keep last 10 messages for better context
            .map(msg => ({
              role: msg.type === 'user' ? 'user' : 'assistant',
              content: msg.content
            }))
        })
      });

      const data = await response.json();

      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: data.response || 'Sorry, I couldn\'t process that request.',
        timestamp: new Date(),
        restaurants: data.restaurants || [],
        confidence: data.confidence || 1.0,
        factChecked: data.fact_checked || false,
        accuracyScore: data.accuracy_score,
        primaryModel: data.primary_model
      };
      
      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      console.error('Chat error:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: 'Sorry, I\'m having trouble right now. Please try again!',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
    setShowSuggestions(true);
    localStorage.removeItem(`chat-${chatKey}`);
    // Re-trigger welcome message
    if (currentUser) {
      setTimeout(() => {
        const welcomeMessage: Message = {
          id: 'welcome-' + Date.now(),
          type: 'system',
          content: `Hello ${currentUser.name}! 👋 What type of food are you looking for today?`,
          timestamp: new Date()
        };
        setMessages([welcomeMessage]);
      }, 500);
    }
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Embedded Clear Button - Only show when embedded */}
      {isEmbedded && (
        <div className="p-2 border-b flex justify-end" style={{borderColor: 'var(--gray-200)'}}>
          <button
            onClick={clearChat}
            className="btn btn-ghost btn-sm"
            title="Clear chat"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Clear Chat
          </button>
        </div>
      )}

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 chat-scrollbar">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start items-start gap-3'} chat-message-enter`}
          >
            {/* RotiShoti Agent Avatar for bot messages */}
            {message.type === 'bot' && (
              <div className="flex-shrink-0 mt-1">
                <RotiShotiAgent
                  state={message.isTyping ? 'thinking' : 'responding'}
                  size="small"
                  showName={false}
                />
              </div>
            )}

            <div
              className={`max-w-[80%] p-4 rounded-2xl ${
                message.type === 'user'
                  ? 'text-white'
                  : message.type === 'system'
                  ? 'border-2'
                  : 'bg-white/80 backdrop-blur-sm border border-paprika/20'
              }`}
              style={{
                background: message.type === 'user'
                  ? 'var(--gradient-primary)'
                  : message.type === 'system'
                  ? 'var(--gradient-warm)'
                  : 'rgba(255, 255, 255, 0.9)',
                borderColor: message.type === 'system' ? 'var(--primary-200)' : 'rgba(230, 126, 34, 0.2)',
                boxShadow: 'var(--shadow-sm)'
              }}
            >
              {message.isTyping ? (
                <div className="flex items-center gap-3">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-saffron rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-paprika rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                    <div className="w-2 h-2 bg-cinnamon rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                  </div>
                  <span className="text-body-sm font-body text-chocolate/70">
                    RotiShoti Food AI Agent is finding the perfect spots...
                  </span>
                </div>
              ) : (
                <>
                  <div className="text-body" style={{
                    color: message.type === 'user' ? 'white' : 'var(--gray-800)',
                    lineHeight: '1.6'
                  }}>
                    {message.content
                      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove markdown bold
                      .replace(/\*(.*?)\*/g, '$1')     // Remove markdown italic
                      .split('\n\n')                   // Split by double newlines for paragraphs
                      .filter(paragraph => paragraph.trim()) // Remove empty paragraphs
                      .map((paragraph, index) => (
                        <p key={index} className="mb-3 last:mb-0">
                          {paragraph.trim()}
                        </p>
                      ))}
                  </div>

                  {/* Agentic Indicators */}
                  {message.type === 'bot' && (message.factChecked || message.confidence) && (
                    <div className="mt-2 flex items-center gap-2 text-caption" style={{color: 'var(--gray-500)'}}>
                      {message.factChecked && (
                        <span className="flex items-center gap-1">
                          <span className="text-green-500">✓</span>
                          Fact-checked
                        </span>
                      )}
                      {message.confidence && (
                        <span className="flex items-center gap-1">
                          <span className="text-blue-500">🤖</span>
                          {Math.round(message.confidence)}% confidence
                        </span>
                      )}
                      {message.primaryModel && (
                        <span className="flex items-center gap-1">
                          <span className="text-purple-500">⚡</span>
                          Dual-LLM
                        </span>
                      )}
                    </div>
                  )}

                  {/* Restaurant Cards - Horizontal Row Layout */}
                  {message.restaurants && message.restaurants.length > 0 && (
                    <div className="mt-3">
                      <div className="flex gap-3 overflow-x-auto pb-2" style={{
                        scrollbarWidth: 'thin',
                        scrollBehavior: 'smooth'
                      }}>
                        {message.restaurants.map((restaurant, index) => (
                          <div
                            key={index}
                            className="p-3 rounded-lg border hover-lift transition-all flex-shrink-0"
                            style={{
                              background: 'var(--gray-50)',
                              border: '1px solid var(--gray-200)',
                              borderRadius: 'var(--radius-md)',
                              minWidth: '240px',
                              maxWidth: '260px'
                            }}
                          >
                            {/* Restaurant Header */}
                            <div className="flex justify-between items-start mb-2">
                              <div className="flex-1 min-w-0">
                                <h4 className="text-body-sm font-semibold truncate" style={{color: 'var(--gray-800)'}}>
                                  {restaurant.name}
                                </h4>
                                <p className="text-caption truncate" style={{color: 'var(--gray-600)'}}>
                                  📍 {restaurant.area}
                                </p>
                              </div>
                              <div className="flex items-center gap-1 ml-2">
                                <span className="text-yellow-500 text-sm">⭐</span>
                                <span className="text-caption font-medium">{restaurant.rating}</span>
                              </div>
                            </div>

                            {/* Menu Item & Price */}
                            <div className="mb-3">
                              <p className="text-body-sm font-medium mb-1" style={{color: 'var(--gray-800)'}}>
                                {restaurant.item_name}
                              </p>
                              <p className="text-body font-bold" style={{color: 'var(--primary-600)'}}>
                                {restaurant.price}
                              </p>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex gap-2">
                              <button
                                onClick={() => handleRestaurantAction(restaurant, 'menu')}
                                className="btn btn-primary btn-sm text-xs px-2 py-1 flex-1"
                              >
                                📋 Menu
                              </button>
                              <button
                                onClick={() => handleRestaurantAction(restaurant, 'directions')}
                                className="btn btn-secondary btn-sm text-xs px-2 py-1 flex-1"
                              >
                                🗺️ Map
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}
              <p className="text-caption mt-2" style={{
                color: message.type === 'user' ? 'rgba(255,255,255,0.8)' : 'var(--gray-500)'
              }}>
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </p>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div 
              className="p-4 rounded-2xl"
              style={{background: 'var(--gray-100)', boxShadow: 'var(--shadow-sm)'}}
            >
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                </div>
                <span className="text-body-sm" style={{color: 'var(--gray-600)'}}>RotiShoti is thinking...</span>
              </div>
            </div>
          </div>
        )}

        {/* Quick Suggestions - Inside Chat */}
        {showSuggestions && messages.length <= 1 && (
          <div className="flex justify-center my-8">
            <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-3xl p-6 max-w-2xl w-full border border-orange-200 shadow-lg">
              <div className="text-center mb-6">
                <div className="text-4xl mb-3">🍽️</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">What are you craving today?</h3>
                <p className="text-gray-600">Try one of these popular searches or ask me anything!</p>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {quickPrompts.map((prompt) => (
                  <button
                    key={prompt.id}
                    onClick={() => handleQuickPrompt(prompt)}
                    className="bg-white hover:bg-orange-50 border border-orange-200 hover:border-orange-300 rounded-2xl p-4 text-left transition-all duration-200 hover:shadow-md hover:scale-105"
                  >
                    <div className="font-medium text-gray-900">{prompt.label}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t" style={{borderColor: 'var(--gray-200)'}}>
        <div className="flex gap-3">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholders[currentPlaceholder]}
            className="flex-1 p-3 border rounded-xl focus-ring"
            style={{
              borderColor: 'var(--gray-300)',
              borderRadius: 'var(--radius-xl)',
              fontSize: 'var(--text-body)'
            }}
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="btn btn-primary px-4"
            style={{borderRadius: 'var(--radius-xl)'}}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </div>

      {/* Restaurant Profile Modal */}
      {selectedRestaurant && (
        <RestaurantProfile
          restaurant={selectedRestaurant}
          onClose={() => {
            setSelectedRestaurant(null);
            setMenuItems([]);
            setBranches([]);
          }}
          demoMenuItems={menuItems.length > 0 ? menuItems : undefined}
          demoBranches={branches.length > 0 ? branches : undefined}
        />
      )}

      {/* Universal Map Modal */}
      {showMap && mapRestaurant && (
        <UniversalMap
          restaurant={mapRestaurant}
          allRestaurants={allRestaurants}
          onClose={() => {
            setShowMap(false);
            setMapRestaurant(null);
          }}
          onRestaurantSelect={(restaurant) => {
            setSelectedRestaurant(restaurant);
            setShowMap(false);
            setMapRestaurant(null);
          }}
        />
      )}
    </div>
  );
};

export default ChatPanel;
