/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Authors: <AUTHORS>
 * Component: Professional Card component with animations and variants
 */

import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'glass' | 'gradient';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  interactive?: boolean;
  loading?: boolean;
  glow?: boolean;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  (
    {
      className,
      variant = 'default',
      padding = 'md',
      rounded = 'lg',
      shadow = 'md',
      hover = false,
      interactive = false,
      loading = false,
      glow = false,
      children,
      ...props
    },
    ref
  ) => {
    const baseClasses = [
      'relative overflow-hidden transition-all duration-300',
      loading && 'animate-pulse',
    ];

    const variantClasses = {
      default: 'bg-white border border-gray-200',
      elevated: 'bg-white',
      outlined: 'bg-white border-2 border-gray-300',
      glass: 'bg-white/80 backdrop-blur-sm border border-white/20',
      gradient: 'bg-gradient-to-br from-green-50 to-orange-50 border border-green-200',
    };

    const paddingClasses = {
      none: '',
      sm: 'p-3',
      md: 'p-4',
      lg: 'p-6',
      xl: 'p-8',
    };

    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      xl: 'rounded-xl',
      full: 'rounded-full',
    };

    const shadowClasses = {
      none: '',
      sm: 'shadow-sm',
      md: 'shadow-md',
      lg: 'shadow-lg',
      xl: 'shadow-xl',
    };

    const interactiveClasses = interactive || hover ? [
      'cursor-pointer',
      'hover:shadow-xl hover:-translate-y-1',
      'active:translate-y-0 active:shadow-lg',
      'transform transition-all duration-200',
      glow && 'hover:shadow-green-500/20',
    ] : [];

    const classes = cn(
      baseClasses,
      variantClasses[variant],
      paddingClasses[padding],
      roundedClasses[rounded],
      shadowClasses[shadow],
      interactiveClasses,
      className
    );

    return (
      <div ref={ref} className={classes} {...props}>
        {loading && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-shimmer" />
        )}
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

// Card Header
export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
  avatar?: React.ReactNode;
}

export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, title, subtitle, action, avatar, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('flex items-start justify-between space-x-4', className)}
        {...props}
      >
        <div className="flex items-start space-x-3 flex-1 min-w-0">
          {avatar && <div className="flex-shrink-0">{avatar}</div>}
          <div className="flex-1 min-w-0">
            {title && (
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-500 truncate">{subtitle}</p>
            )}
            {children}
          </div>
        </div>
        {action && <div className="flex-shrink-0">{action}</div>}
      </div>
    );
  }
);

CardHeader.displayName = 'CardHeader';

// Card Content
export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  spacing?: 'none' | 'sm' | 'md' | 'lg';
}

export const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, spacing = 'md', children, ...props }, ref) => {
    const spacingClasses = {
      none: '',
      sm: 'space-y-2',
      md: 'space-y-4',
      lg: 'space-y-6',
    };

    return (
      <div
        ref={ref}
        className={cn(spacingClasses[spacing], className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardContent.displayName = 'CardContent';

// Card Footer
export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  justify?: 'start' | 'center' | 'end' | 'between';
  align?: 'start' | 'center' | 'end';
}

export const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, justify = 'end', align = 'center', children, ...props }, ref) => {
    const justifyClasses = {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
    };

    const alignClasses = {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'flex space-x-2',
          justifyClasses[justify],
          alignClasses[align],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardFooter.displayName = 'CardFooter';

// Restaurant Card (specialized for RotiShoti)
export interface RestaurantCardProps extends Omit<CardProps, 'children'> {
  restaurant: {
    name: string;
    cuisine: string;
    rating: number;
    priceRange: string;
    image?: string;
    distance?: string;
    deliveryTime?: string;
  };
  onSelect?: () => void;
  showMap?: boolean;
}

export const RestaurantCard = forwardRef<HTMLDivElement, RestaurantCardProps>(
  ({ restaurant, onSelect, showMap = false, className, ...props }, ref) => {
    return (
      <Card
        ref={ref}
        variant="elevated"
        interactive
        hover
        glow
        className={cn('group overflow-hidden', className)}
        onClick={onSelect}
        {...props}
      >
        {restaurant.image && (
          <div className="relative h-48 overflow-hidden">
            <img
              src={restaurant.image}
              alt={restaurant.name}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            {restaurant.deliveryTime && (
              <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                {restaurant.deliveryTime}
              </div>
            )}
          </div>
        )}
        
        <CardContent>
          <CardHeader
            title={restaurant.name}
            subtitle={restaurant.cuisine}
          />
          
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-1">
              <span className="text-yellow-400">★</span>
              <span className="font-medium">{restaurant.rating}</span>
            </div>
            <span className="text-gray-600">{restaurant.priceRange}</span>
            {restaurant.distance && (
              <span className="text-gray-500">{restaurant.distance}</span>
            )}
          </div>
          
          {showMap && (
            <div className="mt-3 h-32 bg-gray-100 rounded-md flex items-center justify-center">
              <span className="text-gray-500 text-sm">Map View</span>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }
);

RestaurantCard.displayName = 'RestaurantCard';

// Loading Card
export const LoadingCard = forwardRef<HTMLDivElement, Omit<CardProps, 'loading'>>(
  ({ className, ...props }, ref) => {
    return (
      <Card
        ref={ref}
        loading
        className={cn('animate-pulse', className)}
        {...props}
      >
        <div className="space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
        </div>
      </Card>
    );
  }
);

LoadingCard.displayName = 'LoadingCard';

// Stats Card
export interface StatsCardProps extends Omit<CardProps, 'children'> {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon?: React.ReactNode;
  color?: 'green' | 'orange' | 'blue' | 'red';
}

export const StatsCard = forwardRef<HTMLDivElement, StatsCardProps>(
  ({ title, value, change, icon, color = 'green', className, ...props }, ref) => {
    const colorClasses = {
      green: 'text-green-600 bg-green-50',
      orange: 'text-orange-600 bg-orange-50',
      blue: 'text-blue-600 bg-blue-50',
      red: 'text-red-600 bg-red-50',
    };

    return (
      <Card
        ref={ref}
        variant="elevated"
        className={cn('text-center', className)}
        {...props}
      >
        <CardContent>
          {icon && (
            <div className={cn('w-12 h-12 mx-auto mb-4 rounded-lg flex items-center justify-center', colorClasses[color])}>
              {icon}
            </div>
          )}
          <div className="text-2xl font-bold text-gray-900">{value}</div>
          <div className="text-sm text-gray-600">{title}</div>
          {change && (
            <div className={cn(
              'text-xs mt-1',
              change.type === 'increase' ? 'text-green-600' : 'text-red-600'
            )}>
              {change.type === 'increase' ? '↗' : '↘'} {Math.abs(change.value)}%
            </div>
          )}
        </CardContent>
      </Card>
    );
  }
);

StatsCard.displayName = 'StatsCard';

export default Card;
