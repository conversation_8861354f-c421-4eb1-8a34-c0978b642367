"""
RotiShoti - AI-Powered Food Discovery Platform
Author: <PERSON><PERSON><PERSON> - Contains all chat-related business logic
"""

import re
import sys
import io
from typing import Optional, List, Dict, Any, Tuple
from app.schemas.chat_schemas import ChatRequest, ChatMessage, UserProfile
from app.services.ai_service import ai_service
from app.services.restaurant_service import restaurant_service
from app.services.map_service import map_service
from app.services.vector_search_service import vector_search_service
from app.db.supabase import create_supabase_client

# Set up proper encoding for Windows console to handle Unicode characters
if sys.platform == "win32":
    try:
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except AttributeError:
        # Fallback for older Python versions
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')


class ChatService:
    def __init__(self):
        self.supabase = create_supabase_client()

    async def process_chat_request(self, request: ChatRequest) -> Dict[str, str]:
        """
        Main business logic for processing chat requests
        """
        try:
            # Check if this is a greeting or simple conversation
            greeting_response = self._handle_greeting(request.user_query, request.user_profile)
            if greeting_response:
                return greeting_response

            # Check if this is an AI identity question
            identity_response = self._handle_identity_question(request.user_query)
            if identity_response:
                return identity_response

            # Check if this is a location question
            location_response = await self._handle_location_question(request.user_query)
            if location_response:
                return location_response

            # Extract budget information from user query and profile
            budget_info = self._extract_budget_from_query_and_profile(request.user_query, request.user_profile)
            print(f"DEBUG: Budget info extracted: {budget_info}")
            
            # Get restaurant data based on location and budget
            restaurants = await self._get_filtered_restaurants(
                location=request.location or "Islamabad",
                budget_range=budget_info.get("budget_range")
            )

            # Debug: Log restaurant data (safe logging)
            try:
                print(f"DEBUG: Found {len(restaurants)} restaurants")
                for r in restaurants[:2]:  # Log first 2 restaurants
                    print(f"DEBUG: Restaurant: {r.get('name')} - Menu items: {len(r.get('menu_items', []))}")
            except UnicodeEncodeError:
                print("DEBUG: Restaurant data logged (encoding issue prevented detailed logging)")

            # If no restaurants found, return a message
            if not restaurants:
                return {
                    "response": "Sorry, I don't have any restaurants in my database for your location. Please try a different location or check back later.",
                    "restaurants": []
                }
            
            # Extract restaurant cards from the filtered restaurants FIRST
            restaurant_cards = self._extract_restaurant_cards(
                restaurants,
                budget_info.get("budget_range"),
                request.user_query,  # Pass the user query for category filtering
                request.conversation_history  # Pass conversation history for context
            )

            try:
                print(f"DEBUG: Extracted {len(restaurant_cards)} restaurant cards")
            except UnicodeEncodeError:
                print("DEBUG: Restaurant cards extracted (encoding issue in logging)")

            # Build context for AI using the ACTUAL filtered restaurant cards
            context = self._build_ai_context(
                request=request,
                restaurants=restaurants,
                budget_info=budget_info,
                actual_card_count=len(restaurant_cards)  # Pass the actual count
            )

            # Get AI response
            try:
                print(f"DEBUG: AI Prompt length: {len(context['prompt'])} characters")
                print(f"DEBUG: Restaurant data in prompt: {'Map URL:' in context['prompt']}")
            except UnicodeEncodeError:
                print("DEBUG: AI Prompt prepared (encoding issue in logging)")

            ai_response = await ai_service.get_llm_response(context["prompt"])

            # CRITICAL: Validate AI response against actual data
            validated_response = self._validate_ai_response(ai_response, restaurant_cards, request.user_query)

            return {
                "response": validated_response,
                "restaurants": restaurant_cards
            }
            
        except Exception as e:
            # Handle encoding errors safely
            try:
                error_msg = str(e)
                print(f"Chat Service Error: {error_msg}")
            except UnicodeEncodeError:
                print("Chat Service Error: Unicode encoding issue in error message")
            return {"response": "Sorry, I couldn't understand that. Please try again later."}

    def _handle_greeting(self, query: str, user_profile: Optional[UserProfile] = None) -> Optional[Dict[str, str]]:
        """
        Handle greetings and simple conversational queries
        """
        query_lower = query.lower().strip()

        # Define greeting patterns (English, Urdu, and Islamic)
        greeting_patterns = [
            'hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening',
            'how are you', 'what\'s up', 'whats up', 'sup',
            # Islamic/Urdu greetings
            'aoa', 'assalam', 'assalam u alaikum', 'assalam o alaikum', 'assalamu alaikum',
            'salam', 'salaam', 'peace be upon you', 'adab', 'namaste'
        ]

        # Check if query is a greeting
        if any(pattern in query_lower for pattern in greeting_patterns):
            user_name = user_profile.name if user_profile else "there"

            # Determine appropriate greeting response
            greeting_start = "Hello"
            if any(islamic_greeting in query_lower for islamic_greeting in ['aoa', 'assalam', 'salam']):
                greeting_start = "Wa Alaikum Assalam"

            # Get user's budget info for personalized greeting
            budget_text = ""
            if user_profile and hasattr(user_profile, 'budget_range') and user_profile.budget_range:
                budget_text = f" I see you prefer dining within ₨{user_profile.budget_range[0]} to ₨{user_profile.budget_range[1]}."

            # Get user's favorite cuisines
            cuisine_text = ""
            if user_profile and hasattr(user_profile, 'favorite_cuisines') and user_profile.favorite_cuisines:
                cuisine_text = f" I know you love {', '.join(user_profile.favorite_cuisines[:2])} food."

            greeting_response = f"{greeting_start} {user_name}! 👋 I'm RotiShoti AI, your personal food discovery assistant.{budget_text}{cuisine_text}\n\nWhat can I help you find today? You can ask me things like:\n• \"Find me some good biryani places\"\n• \"I want pizza under 1000 rupees\"\n• \"Show me halal restaurants nearby\"\n• \"What's good for dinner tonight?\""

            return {
                "response": greeting_response,
                "restaurants": []
            }

        return None

    def _handle_identity_question(self, user_query: str) -> Optional[Dict[str, str]]:
        """
        Handle questions about AI identity, creators, model, etc.
        """
        query_lower = user_query.lower()

        # Check if this is an identity question
        identity_keywords = [
            'who made you', 'who created you', 'who built you', 'who developed you',
            'what model are you', 'what ai are you', 'who are you', 'what are you',
            'your creator', 'your developer', 'your maker', 'your model',
            'which model', 'what llm', 'openai', 'chatgpt', 'claude', 'gpt',
            'who is your creator', 'who is your developer'
        ]

        if not any(keyword in query_lower for keyword in identity_keywords):
            return None

        # Provide identity information
        response = "I'm RotiShoti, a local AI food recommendation agent created by **Masab Farooque** and **Saad Ilyas**. I'm designed specifically to help you discover the best restaurants and food options in Islamabad based on your preferences and budget. I use local data and knowledge to give you personalized food recommendations!"

        return {
            "response": response,
            "restaurants": []
        }

    def _extract_budget_from_query_and_profile(self, query: str, user_profile: Optional[UserProfile] = None) -> Dict[str, Any]:
        """
        Extract budget information from user query and profile preferences
        """
        # First, try to get budget from user profile
        profile_budget_range = None
        if user_profile and hasattr(user_profile, 'budget_range') and user_profile.budget_range:
            profile_budget_range = user_profile.budget_range
            print(f"DEBUG: Found user profile budget: Rs{profile_budget_range[0]} to Rs{profile_budget_range[1]}")

        # Then check if user specified a different budget in their query
        query_budget_info = self._extract_budget_from_query(query)

        # If user specified budget in query, use that; otherwise use profile budget
        if query_budget_info.get("budget_range"):
            print("DEBUG: Using budget from user query")
            return query_budget_info
        elif profile_budget_range:
            print("DEBUG: Using budget from user profile")
            return {
                "budget_range": profile_budget_range,
                "user_budget": f"between Rs{profile_budget_range[0]} and Rs{profile_budget_range[1]}",
                "effective_budget": f"Rs{profile_budget_range[0]} to Rs{profile_budget_range[1]}"
            }
        else:
            print("DEBUG: No budget found in query or profile")
            return {"budget_range": None, "user_budget": None, "effective_budget": None}

    def _extract_budget_from_query_and_profile(self, query: str, user_profile: Optional[UserProfile] = None) -> Dict[str, Any]:
        """
        Extract budget information from user query and profile preferences.
        Priority: Query budget > Profile budget > No budget
        """
        # First, check if user specified budget in their query
        query_budget_info = self._extract_budget_from_query(query)

        print(f"DEBUG: Query budget extraction result: {query_budget_info}")

        if query_budget_info.get("budget_range"):
            print("DEBUG: Using budget from user query (overrides profile)")
            return query_budget_info

        # If no budget in query, use profile budget as default
        if user_profile and hasattr(user_profile, 'budget_range') and user_profile.budget_range:
            profile_budget_range = user_profile.budget_range
            print(f"DEBUG: Using budget from user profile: Rs{profile_budget_range[0]} to Rs{profile_budget_range[1]}")
            return {
                "budget_range": profile_budget_range,
                "user_budget": f"between Rs{profile_budget_range[0]} and Rs{profile_budget_range[1]}",
                "effective_budget": f"Rs{profile_budget_range[0]} to Rs{profile_budget_range[1]}"
            }

        print("DEBUG: No budget found in query or profile")
        return {"budget_range": None, "user_budget": None, "effective_budget": None}

    def _extract_budget_from_query(self, query: str) -> Dict[str, Any]:
        """
        Extract budget information from user query
        """
        user_budget = None
        budget_range = None
        query_lower = query.lower()

        print(f"DEBUG: Extracting budget from query: '{query_lower}'")
        
        # Look for range patterns like "1k to 5k", "1000-1500", "1000 to 1500"
        range_patterns = [
            r'(\d+)k?\s*(?:sy|se|to|-|tak)\s*(\d+)k?',  # "1k to 5k", "1000 to 5000"
            r'(\d+)k?\s*(?:sy|se)\s*(\d+)k?\s*(?:tk|tak|mai)',
            r'between\s*(\d+)k?\s*(?:and|to)\s*(\d+)k?',
            r'(\d+)k?\s*to\s*(\d+)k?'
        ]
        
        for pattern in range_patterns:
            match = re.search(pattern, query_lower)
            if match:
                min_budget = int(match.group(1))
                max_budget = int(match.group(2))

                # Check if the pattern contains 'k' and convert to thousands
                if 'k' in match.group(0):  # Check if 'k' is in the matched text
                    if 'k' in match.group(1) or not any(char.isalpha() for char in match.group(1)):
                        min_budget = min_budget * 1000
                    if 'k' in match.group(2) or not any(char.isalpha() for char in match.group(2)):
                        max_budget = max_budget * 1000

                budget_range = (min_budget, max_budget)
                user_budget = f"between Rs{min_budget} and Rs{max_budget}"
                print(f"DEBUG: Extracted range budget: Rs{min_budget} to Rs{max_budget}")
                break
        
        # If no range found, look for single budget mentions
        if not budget_range:
            single_budget_patterns = [
                r'under\s*(\d+)k(?:rs|rupees?|tk|taka)?',  # "under 5k", "under 5krs"
                r'under\s*(\d+)(?:rs|rupees?|tk|taka)?',   # "under 5000rs", "under 5000"
                r'below\s*(\d+)k(?:rs|rupees?|tk|taka)?',  # "below 5k"
                r'below\s*(\d+)(?:rs|rupees?|tk|taka)?',   # "below 5000rs"
                r'within\s*(\d+)k(?:rs|rupees?|tk|taka)?', # "within 5k"
                r'within\s*(\d+)(?:rs|rupees?|tk|taka)?',  # "within 5000rs"
                r'budget.*?(\d+)k',                        # "budget 5k"
                r'budget.*?(\d+)',                         # "budget 5000"
                r'(\d+)k\s*(?:rs|rupees?)(?:\s|$)',       # "5krs", "5k rupees"
                r'(\d+)\s*(?:rs|rupees?)(?:\s|$)',        # "5000rs", "5000 rupees"
                r'(\d+)k\s*(?:tk|taka)(?:\s|$)',          # "5ktk", "5k taka"
                r'(\d+)\s*(?:tk|taka)(?:\s|$)',           # "5000tk", "5000 taka"
                r'max\s*(\d+)k(?:rs|rupees?|tk|taka)?',   # "max 5k"
                r'max\s*(\d+)(?:rs|rupees?|tk|taka)?',    # "max 5000rs"
                r'up\s*to\s*(\d+)k(?:rs|rupees?|tk|taka)?', # "up to 5k"
                r'up\s*to\s*(\d+)(?:rs|rupees?|tk|taka)?'   # "up to 5000rs"
            ]
            
            for pattern in single_budget_patterns:
                match = re.search(pattern, query_lower)
                if match:
                    budget_amount = int(match.group(1))

                    # Check if it's a "k" pattern (thousands) - only if 'k' is actually in the matched text
                    if 'k' in pattern and 'k' in match.group(0):
                        budget_amount = budget_amount * 1000  # Convert k to thousands
                        print(f"DEBUG: Converted 'k' to thousands: {budget_amount}")
                    elif 'k' in pattern and 'k' not in match.group(0):
                        # Pattern has 'k' but match doesn't - don't multiply
                        print(f"DEBUG: Pattern has 'k' but match doesn't, keeping original: {budget_amount}")

                    # Check if it's an "under" or "below" pattern
                    if any(keyword in pattern for keyword in ['under', 'below', 'within', 'max', 'up']):
                        # Create a range from 0 to the specified amount
                        budget_range = (0, budget_amount)
                        user_budget = f"under Rs{budget_amount}"
                        print(f"DEBUG: Extracted 'under' budget: 0 to Rs{budget_amount}")
                    else:
                        # Single budget mention without "under"
                        user_budget = budget_amount
                        print(f"DEBUG: Extracted single budget: Rs{budget_amount}")
                    break
        
        result = {
            "user_budget": user_budget,
            "budget_range": budget_range,
            "effective_budget": user_budget or 'medium'
        }

        print(f"DEBUG: Budget extraction result: {result}")
        return result

    async def _get_filtered_restaurants(self, location: str, budget_range: Optional[Tuple[int, int]] = None) -> List[Dict]:
        """
        Get restaurants filtered by location and budget using vector search when possible
        """
        # Fetch restaurants with complete details including menu items
        response = self.supabase.from_("restaurants").select("""
            name, cuisine_types, area, address, latitude, longitude,
            price_range_average, rating_overall, features,
            menu_items(name, price, description, category)
        """).eq("city", location).execute()

        restaurants = response.data or []

        # Filter restaurants by budget if specified
        if budget_range:
            filtered_restaurants = []
            min_budget, max_budget = budget_range
            print(f"DEBUG: Filtering by budget range: Rs{min_budget} - Rs{max_budget}")

            for restaurant in restaurants:
                menu_items = restaurant.get('menu_items', [])
                print(f"DEBUG: Restaurant {restaurant.get('name')} has {len(menu_items)} menu items")

                # Filter menu items by budget range (with slight flexibility for high-end items)
                filtered_items = []
                for item in menu_items:
                    price = item.get('price', 0)
                    # Allow slight flexibility (up to 5% over budget for items above Rs 3000)
                    max_allowed = max_budget + (max_budget * 0.05 if max_budget >= 3000 else 0)
                    if min_budget <= price <= max_allowed:
                        filtered_items.append(item)

                print(f"DEBUG: After filtering: {len(filtered_items)} items in budget range")
                if filtered_items:
                    for item in filtered_items[:2]:  # Log first 2 items
                        print(f"DEBUG: Item: {item.get('name')} - Rs{item.get('price')}")

                # Only include restaurant if it has items in budget range
                if filtered_items:
                    restaurant['menu_items'] = filtered_items
                    filtered_restaurants.append(restaurant)

            return filtered_restaurants

        return restaurants

    async def _get_vector_search_results(self, query: str, location: str, budget_range: Optional[Tuple[int, int]] = None) -> List[Dict]:
        """
        Get restaurants using vector semantic search for better matching
        """
        try:
            # Use enhanced vector search to find relevant menu items
            search_results = await vector_search_service.semantic_search_menu_items(
                query=query,
                limit=40,  # Increased for better variety
                similarity_threshold=0.4,  # Lower threshold for better recall
                budget_range=budget_range,
                city=location
            )

            # Group results by restaurant
            restaurants_dict = {}
            for result in search_results:
                restaurant_name = result['restaurant_name']
                if restaurant_name not in restaurants_dict:
                    restaurants_dict[restaurant_name] = {
                        'name': restaurant_name,
                        'area': result['area'],
                        'cuisine_types': result['cuisine_types'],
                        'menu_items': [],
                        'vector_score': result['similarity_score']
                    }

                restaurants_dict[restaurant_name]['menu_items'].append({
                    'name': result['item_name'],
                    'description': result['description'],
                    'price': result['price'],
                    'category': result['category'],
                    'similarity_score': result['similarity_score']
                })

            # Convert to list and sort by best vector score
            restaurants = list(restaurants_dict.values())
            restaurants.sort(key=lambda x: x['vector_score'], reverse=True)

            print(f"DEBUG: Vector search found {len(restaurants)} restaurants with {len(search_results)} relevant items")

            return restaurants

        except Exception as e:
            print(f"DEBUG: Vector search failed, falling back to regular search: {str(e)}")
            return await self._get_filtered_restaurants(location, budget_range)

    def _build_ai_context(self, request: ChatRequest, restaurants: List[Dict], budget_info: Dict[str, Any], actual_card_count: int = 0) -> Dict[str, str]:
        """
        Build context and prompt for AI service
        """
        # Format restaurant data
        restaurant_data = self._format_restaurant_data(restaurants, budget_info.get("budget_range"))
        
        # Build conversation context
        conversation_context = self._build_conversation_context(request)
        
        # Build user profile context
        user_context = self._build_user_context(request.user_profile)
        
        # Create budget filter message
        budget_filter_msg = self._create_budget_filter_message(budget_info.get("budget_range"))
        
        # Build the complete prompt
        prompt = self._build_ai_prompt(
            request=request,
            budget_info=budget_info,
            restaurant_data=restaurant_data,
            conversation_context=conversation_context,
            user_context=user_context,
            budget_filter_msg=budget_filter_msg,
            actual_card_count=actual_card_count
        )
        
        return {"prompt": prompt}

    def _format_restaurant_data(self, restaurants: List[Dict], budget_range: Optional[Tuple[int, int]] = None) -> str:
        """
        Format restaurant data for AI consumption
        """
        restaurant_data = ""
        for r in restaurants:
            menu_items = r.get('menu_items', [])

            # Organize menu items by price (low to high)
            sorted_items = sorted(menu_items, key=lambda x: x.get('price', 0)) if menu_items else []

            # Get map embed URL from map service
            map_embed_url = map_service.get_restaurant_map_url(
                r.get('name', ''),
                r.get('latitude'),
                r.get('longitude')
            )

            restaurant_data += f"""
{r['name']} - {', '.join(r.get('cuisine_types', []))}
📍 Location: {r.get('area', '')} - {r.get('address', '')}
💰 Average Price: ₨{r.get('price_range_average', 'N/A')}
⭐ Rating: {r.get('rating_overall', 'N/A')}/5
🏷️ Features: {', '.join(r.get('features', []))}
🗺️ Map URL: {map_embed_url}
🍽️ Menu Items in your budget range ({f"₨{budget_range[0]} to ₨{budget_range[1]}" if budget_range else "all items"}):"""

            # Add filtered menu items with prices
            for item in sorted_items:
                restaurant_data += f"\n   • {item['name']} - ₨{item['price']} ({item.get('category', 'Main')})"

            restaurant_data += "\n\n"

        return restaurant_data

    def _extract_restaurant_cards(self, restaurants: List[Dict], budget_range: Optional[Tuple[int, int]] = None, user_query: str = "", conversation_history: List[Dict] = None) -> List[Dict]:
        """
        Extract restaurant cards for frontend display with category filtering
        """
        restaurant_cards = []
        seen_restaurants = set()  # Track restaurants to avoid duplicates

        # Extract food category from user query
        query_lower = user_query.lower()
        food_category = None

        # Define category keywords based on ACTUAL JSON categories
        category_keywords = {
            # Western Food Categories
            'wings': ['wings', 'wing'],
            'burger': ['burger', 'burgers'],
            'pizza': ['pizza', 'pizzas'],
            'steak': ['steak', 'steaks'],
            'pasta': ['pasta', 'fettucine', 'spaghetti'],

            # Pakistani/Desi Food Categories (based on JSON structure)
            'bbq': ['boti', 'malai', 'seekh', 'kabab', 'tikka', 'reshmi'],
            'karahi': ['karahi'],
            'handi': ['handi'],
            'biryani': ['biryani'],
            'pulao': ['pulao'],
            'nehari': ['nehari'],
            'haleem': ['haleem'],

            # Broad categories
            'chicken': ['chicken'],
            'rice': ['rice', 'biryani', 'pulao'],

            # Desi/Traditional (combination of Pakistani categories)
            'desi': ['karahi', 'handi', 'nehari', 'haleem', 'biryani', 'pulao', 'seekh', 'boti', 'malai', 'tikka', 'kabab'],
            'traditional': ['karahi', 'handi', 'nehari', 'haleem', 'biryani', 'pulao', 'seekh', 'boti', 'malai', 'tikka', 'kabab'],
            'pakistani': ['karahi', 'handi', 'nehari', 'haleem', 'biryani', 'pulao', 'seekh', 'boti', 'malai', 'tikka', 'kabab']
        }

        # Define JSON category mappings for precise filtering
        json_category_mappings = {
            'bbq': ['BBQ', 'Angeethi', 'Kabab'],
            'karahi': ['Chicken Karahi', 'Mutton Karahi', 'Handi/Karahi'],
            'handi': ['Handi', 'Handi/Karahi'],
            'biryani': ['Biryani', 'Dum Matka Biryani'],
            'pulao': ['Pulao Kabab', 'Kabul Jan'],
            'nehari': ['Haleem/Nehari'],
            'haleem': ['Haleem/Nehari', 'Khaas'],
            'desi': ['BBQ', 'Angeethi', 'Kabab', 'Chicken Karahi', 'Mutton Karahi', 'Handi', 'Biryani', 'Pulao Kabab', 'Haleem/Nehari', 'Desi Delights'],
            'traditional': ['BBQ', 'Angeethi', 'Kabab', 'Chicken Karahi', 'Mutton Karahi', 'Handi', 'Biryani', 'Pulao Kabab', 'Haleem/Nehari', 'Desi Delights'],
            'pakistani': ['BBQ', 'Angeethi', 'Kabab', 'Chicken Karahi', 'Mutton Karahi', 'Handi', 'Biryani', 'Pulao Kabab', 'Haleem/Nehari', 'Desi Delights']
        }

        # SMART CATEGORY DETECTION (including Urdu/Hindi terms)
        food_category = None

        # Check for Urdu/Hindi desi terms first (highest priority)
        if any(term in query_lower for term in ['desi', 'desi dishes', 'traditional', 'pakistani']):
            food_category = 'desi'
            print(f"DEBUG: DETECTED DESI QUERY - {user_query}")
        elif any(term in query_lower for term in ['karahi', 'karhai']):
            food_category = 'karahi'
            print(f"DEBUG: DETECTED KARAHI QUERY - {user_query}")
        elif any(term in query_lower for term in ['handi']):
            food_category = 'handi'
            print(f"DEBUG: DETECTED HANDI QUERY - {user_query}")
        elif any(term in query_lower for term in ['biryani']):
            food_category = 'biryani'
            print(f"DEBUG: DETECTED BIRYANI QUERY - {user_query}")
        elif any(term in query_lower for term in ['pulao']):
            food_category = 'pulao'
            print(f"DEBUG: DETECTED PULAO QUERY - {user_query}")
        elif any(term in query_lower for term in ['bbq', 'boti', 'malai', 'seekh', 'kabab']):
            food_category = 'bbq'
            print(f"DEBUG: DETECTED BBQ QUERY - {user_query}")
        else:
            # Fall back to original keyword matching
            for category, keywords in category_keywords.items():
                if any(keyword in query_lower for keyword in keywords):
                    food_category = category
                    break

        # If no category found in current query, check conversation history
        if not food_category and conversation_history:
            for msg in reversed(conversation_history):  # Check most recent first
                if hasattr(msg, 'role') and msg.role == 'user':
                    prev_query = msg.content.lower()
                    for category, keywords in category_keywords.items():
                        if any(keyword in prev_query for keyword in keywords):
                            food_category = category
                            break
                    if food_category:
                        break

        for r in restaurants:
            restaurant_name = r.get('name', '')

            # Skip if we've already processed this restaurant
            if restaurant_name in seen_restaurants:
                continue
            seen_restaurants.add(restaurant_name)

            menu_items = r.get('menu_items', [])

            # Filter menu items by budget if specified (with slight flexibility)
            if budget_range:
                filtered_items = []
                for item in menu_items:
                    price = item.get('price', 0)
                    # Allow slight flexibility (up to 5% over budget for items above Rs 3000)
                    max_allowed = budget_range[1] + (budget_range[1] * 0.05 if budget_range[1] >= 3000 else 0)
                    if budget_range[0] <= price <= max_allowed:
                        filtered_items.append(item)
            else:
                filtered_items = menu_items

            # Filter by food category if specified
            if food_category and filtered_items:
                category_filtered_items = []
                for item in filtered_items:
                    item_name = item.get('name', '').lower()

                    # SMART CATEGORY MATCHING using JSON categories + item names
                    item_name_lower = item_name.lower()
                    item_category = item.get('category', '').strip()

                    # Check if item matches using JSON category first (most accurate)
                    category_match = False
                    if food_category in json_category_mappings:
                        json_categories = json_category_mappings[food_category]
                        if item_category in json_categories:
                            category_match = True
                            print(f"DEBUG: JSON CATEGORY MATCH - {item_name} (category: {item_category})")

                    # If no JSON category match, check item name keywords
                    if not category_match and food_category in category_keywords:
                        keywords = category_keywords[food_category]
                        if any(keyword in item_name_lower for keyword in keywords):
                            category_match = True
                            print(f"DEBUG: NAME KEYWORD MATCH - {item_name} (keywords: {keywords})")

                    # Handle desi/traditional/pakistani queries with special logic
                    if food_category in ['desi', 'traditional', 'pakistani']:
                        western_categories = ['Giant Burgers', 'Sandwiches', 'Steaks', 'Pizza', 'Sharing Bites', 'Chinese', 'Continental']
                        western_keywords = ['steak', 'burger', 'pizza', 'pasta', 'sandwich', 'fries', 'wings', 'philly', 'texan', 'butter chicken']

                        # Exclude if it's a Western category or contains Western keywords
                        if (item_category in western_categories or
                            any(keyword in item_name_lower for keyword in western_keywords)):
                            print(f"DEBUG: EXCLUDING WESTERN DISH - {item_name} (category: {item_category})")
                            continue

                        # Include if it's a desi category or contains desi keywords OR has category match
                        desi_categories = json_category_mappings.get('desi', [])
                        desi_keywords = category_keywords.get('desi', [])

                        if (category_match or
                            item_category in desi_categories or
                            any(keyword in item_name_lower for keyword in desi_keywords)):
                            category_filtered_items.append(item)
                            print(f"DEBUG: DESI MATCH FOUND - {item_name} (category: {item_category})")

                    # For other categories, use normal matching
                    elif category_match:
                        category_filtered_items.append(item)
                        print(f"DEBUG: {food_category.upper()} MATCH FOUND - {item_name}")

                filtered_items = category_filtered_items

            # Get the best item to recommend (cheapest in budget range)
            if filtered_items:
                best_item = min(filtered_items, key=lambda x: x.get('price', 0))

                # Get map embed URL
                map_embed_url = map_service.get_restaurant_map_url(
                    r.get('name', ''),
                    r.get('latitude'),
                    r.get('longitude')
                )

                restaurant_cards.append({
                    "name": r.get('name', ''),
                    "area": r.get('area', ''),
                    "address": r.get('address', ''),
                    "rating": f"{r.get('rating_overall', 'N/A')}/5",
                    "item_name": best_item.get('name', ''),
                    "price": str(best_item.get('price', 0)),
                    "map_embed_url": map_embed_url,
                    # Add coordinate fields for distance calculation
                    "latitude": r.get('latitude'),
                    "longitude": r.get('longitude'),
                    # Add additional fields for frontend compatibility
                    "id": r.get('id', f"restaurant-{len(restaurant_cards)}"),
                    "cuisine_types": r.get('cuisine_types', []),
                    "phone": r.get('phone', ''),
                    "price_range_min": r.get('price_range_min', 0),
                    "price_range_max": r.get('price_range_max', 0),
                    "image_url": r.get('image_url', '')
                })

        return restaurant_cards



    def _build_conversation_context(self, request: ChatRequest) -> str:
        """
        Build conversation context from chat history
        """
        conversation_context = ""
        if request.conversation_history:
            conversation_context = "\n\nCONVERSATION HISTORY (most recent messages):\n"
            for msg in request.conversation_history[-4:]:  # Keep last 4 messages for better focus
                role = "User" if msg.role == "user" else "Assistant"
                conversation_context += f"{role}: {msg.content}\n"
            conversation_context += f"\nCURRENT USER QUESTION: {request.user_query}\n"
            conversation_context += "\n🚨 CRITICAL CONTEXT INSTRUCTION: Based on the conversation above, understand what the user is specifically asking about."
            conversation_context += "\n- If they say 'suggest more' or 'show me more', they want MORE of the SAME CATEGORY from their previous request"
            conversation_context += "\n- If they previously asked for 'burgers', and now say 'more', they want MORE BURGERS"
            conversation_context += "\n- If they previously asked for 'steaks', and now say 'more', they want MORE STEAKS"
            conversation_context += "\n- Maintain the same budget range and food category from the previous request"
            conversation_context += "\n- DO NOT change the food category unless explicitly asked"
        
        return conversation_context

    def _build_user_context(self, user_profile: Optional[UserProfile]) -> str:
        """
        Build user profile context
        """
        user_context = ""
        if user_profile:
            # Format budget range if available
            budget_text = "Not specified"
            if hasattr(user_profile, 'budget_range') and user_profile.budget_range:
                budget_text = f"₨{user_profile.budget_range[0]} to ₨{user_profile.budget_range[1]}"

            user_context = f"""
USER PROFILE:
- Name: {user_profile.name}
- Food Personality: {user_profile.food_personality}
- Favorite Cuisines: {', '.join(user_profile.favorite_cuisines)}
- Budget Range: {budget_text}
- Spice Level: {user_profile.spice_level}
- Dining Style: {', '.join(user_profile.dining_style)}
"""
        return user_context

    def _create_budget_filter_message(self, budget_range: Optional[Tuple[int, int]] = None) -> str:
        """
        Create budget filter message for AI
        """
        budget_filter_msg = ""
        if budget_range:
            min_budget, max_budget = budget_range
            budget_filter_msg = f"""
🎯 CRITICAL BUDGET FILTER - MUST FOLLOW EXACTLY:
- User's budget range: ₨{min_budget} to ₨{max_budget}
- NEVER recommend items below ₨{min_budget} (like ₨{min_budget-100} or ₨249)
- NEVER recommend items above ₨{max_budget} (like ₨{max_budget+100})
- Items costing ₨{min_budget-1} or less are FORBIDDEN
- Items costing ₨{max_budget+1} or more are FORBIDDEN
- ONLY show items that cost exactly ₨{min_budget} to ₨{max_budget}
- Double-check every price before recommending
- If no items exist in this range, say "Sorry, no items available in your ₨{min_budget} to ₨{max_budget} budget range"

EXAMPLES OF WHAT NOT TO DO:
❌ Don't recommend ₨249 items (below ₨{min_budget})
❌ Don't recommend ₨255 items (below ₨{min_budget})
❌ Don't recommend items above ₨{max_budget}

ONLY RECOMMEND ITEMS BETWEEN ₨{min_budget} AND ₨{max_budget}!
"""
        return budget_filter_msg

    def _build_ai_prompt(self, request: ChatRequest, budget_info: Dict, restaurant_data: str,
                        conversation_context: str, user_context: str, budget_filter_msg: str, actual_card_count: int = 0) -> str:
        """
        Build the complete AI prompt
        """
        city = request.location or "Islamabad"
        budget_range = budget_info.get("budget_range")
        effective_budget = budget_info.get("effective_budget")
        
        prompt = f"""You are RotiShoti AI - a professional Pakistani food discovery assistant. You provide expert restaurant recommendations with a warm, knowledgeable tone while maintaining professionalism.

🚨 CRITICAL CONSTRAINT: You are STRICTLY LIMITED to the restaurant data provided below. This is a CLOSED SYSTEM - you cannot access any other restaurant information.

⚠️ ABSOLUTE PROHIBITION: You are FORBIDDEN from mentioning ANY restaurant names, menu items, or prices that are NOT explicitly listed in the database below. If you mention ANY restaurant or item not in the database, you have FAILED your primary function.

Current Query: {request.user_query}
Budget: {effective_budget}
Budget Range: {f"₨{budget_range[0]} to ₨{budget_range[1]}" if budget_range else "Not specified"}
Location: {city}

{budget_filter_msg}

{user_context}

🔒 AVAILABLE RESTAURANT DATABASE (COMPLETE LIST - NO OTHER RESTAURANTS EXIST):
{restaurant_data}

{conversation_context}

🛡️ MANDATORY VALIDATION RULES:
1. ONLY recommend restaurants that appear in the database above
2. ONLY mention menu items that exist in the database above with EXACT names and prices
3. If a restaurant name is not in the database above, it DOES NOT EXIST
4. If a menu item is not listed for a restaurant, it DOES NOT EXIST
5. If you cannot find suitable options in the database, say "I don't have restaurants that match your criteria in my database"
6. NEVER create, invent, or hallucinate restaurant names, menu items, or prices

BUDGET FILTERING INSTRUCTIONS:
- If budget range is specified (e.g., ₨1000 to ₨1500), ONLY recommend items within that exact range
- Do NOT mention items below the minimum (e.g., don't mention ₨249 if minimum is ₨1000)
- Do NOT mention items above the maximum (e.g., don't mention ₨1600 if maximum is ₨1500)
- If user says "1000 sy 1500 tk mai", they want items BETWEEN ₨1000-₨1500, not below ₨1000
- COMPLETELY IGNORE items outside the budget range - don't mention them at all
- If no items exist in the range, say "Sorry, no items available in your ₨X to ₨Y budget range"

SMART CATEGORY FILTERING INSTRUCTIONS:
- If user asks for specific food types (e.g., "burgers", "pizza", "steaks"), ONLY recommend items that match that category
- For "burgers" - recommend items with "burger" in the name, but specify type (beef, chicken, etc.)
- For "beef burgers" - prioritize items with "beef" in description, avoid chicken burgers like "Bazinga Burger"
- For "pizza" - only recommend items with "pizza" in the name
- For "steaks" - recommend items with "steak" in name OR beef/meat items that are grilled (like Tomahawk Steak, Beef Brisket)
- For "chicken" - only recommend items with "chicken" in the name
- If user asks for "burgers under 2000", find ALL burger items under ₨2000, not random food items
- NEVER recommend pasta when user asks for burgers, or rice when user asks for pizza
- If no items match the category AND budget, say "Sorry, no [category] available in your budget range"
- Be specific about food types: "Bazinga Burger is a fried chicken burger" not just "burger"

BEFORE RESPONDING:
1. First filter by food category (if specified)
2. Then filter by budget range (if specified)
3. Only recommend from the final filtered list

PERSONALITY & BEHAVIOR:
- Be professional, concise, and helpful
- Provide clear, direct recommendations without casual greetings
- Use the user's name sparingly and naturally
- Focus on the food recommendations, not conversational pleasantries
- Keep responses brief and to the point
- Avoid religious greetings, casual expressions, and lengthy introductions
- Be informative and efficient

RESPONSE FORMAT:
DO NOT include detailed restaurant information in your text response. The restaurant cards will be displayed separately.

🚨 CRITICAL INSTRUCTION: You MUST say "I found {actual_card_count} options" in your response. Do NOT count yourself - use exactly {actual_card_count}.

🚨 RESPONSE FORMAT REQUIREMENT:
Your response should be brief and contextual:
- If this is a follow-up request (like "suggest more"), acknowledge the context: "Here are {actual_card_count} more [food category] options"
- If this is a new request, use: "I found {actual_card_count} [food category] options within your budget"
- Replace [food category] with the actual category (burgers, steaks, etc.)
- Keep it to 1 sentence maximum

🚨 MAINTAIN CONVERSATION CONTEXT - If they ask for "more", give them more of the SAME category from their previous request.

ABSOLUTELY FORBIDDEN:
- NO restaurant names, addresses, prices, or details
- NO numbered lists or bullet points
- NO "Recommended Restaurants" sections
- NO detailed descriptions
- NO additional text after the main sentence
- MAXIMUM 1 sentence only
- The restaurant cards will show all details separately

The detailed restaurant information will be shown in separate cards below your text response."""

        return prompt

    async def _handle_location_question(self, user_query: str) -> Optional[Dict[str, str]]:
        """
        Handle location-specific questions like "where is ranchers" or "branches of cheezious"
        """
        query_lower = user_query.lower()

        # Check if this is a location question
        location_keywords = ['where is', 'location of', 'address of', 'where can i find', 'branches of', 'locations of', 'find branches', 'how many branches']
        if not any(keyword in query_lower for keyword in location_keywords):
            return None

        # Extract restaurant name from the query
        restaurant_name = None

        # Try to find restaurant name in the query
        try:
            # Get all restaurants from database to match against
            response = self.supabase.from_("restaurants").select("name").execute()
            if response.data:
                for restaurant in response.data:
                    name = restaurant['name'].lower()
                    if name in query_lower or any(word in query_lower for word in name.split()):
                        restaurant_name = restaurant['name']
                        break
        except Exception as e:
            print(f"Error fetching restaurants for location query: {e}")

        if not restaurant_name:
            return {
                "response": "I couldn't identify which restaurant you're asking about. Please specify the restaurant name clearly.",
                "restaurants": []
            }

        # Get restaurant and branch information from database
        try:
            # Get main restaurant info
            restaurant_response = self.supabase.from_("restaurants").select("""
                name, area, address, features, latitude, longitude
            """).eq("name", restaurant_name).execute()

            if not restaurant_response.data:
                return {
                    "response": f"Sorry, I couldn't find location information for {restaurant_name}. Please check the restaurant name and try again.",
                    "restaurants": []
                }

            restaurant = restaurant_response.data[0]

            # Get all branches for this restaurant
            branches_response = self.supabase.from_("restaurant_branches").select("""
                name, area, address, features, latitude, longitude, is_main_branch
            """).eq("restaurant_id", restaurant_response.data[0].get('id', '')).execute()

            branches = branches_response.data or []

            # Build response
            if len(branches) > 1:
                # Multiple branches
                response = f"🏪 **{restaurant_name}** has {len(branches)} branches in Islamabad:\n\n"

                for i, branch in enumerate(branches, 1):
                    branch_type = " (Main Branch)" if branch.get('is_main_branch') else ""
                    response += f"**{i}. {branch['name']}{branch_type}**\n"
                    response += f"📍 {branch['area']}\n"
                    response += f"🏠 {branch['address']}\n"

                    if branch.get('features'):
                        features = ', '.join(branch['features']) if isinstance(branch['features'], list) else branch['features']
                        response += f"✨ Features: {features}\n"
                    response += "\n"

                response += "You can click the 🗺️ Map button on any restaurant card to get directions!"

            elif len(branches) == 1:
                # Single branch
                branch = branches[0]
                response = f"📍 **{restaurant_name}** is located at:\n\n"
                response += f"🏠 {branch['address']}\n"
                response += f"📍 {branch['area']}, Islamabad\n"

                if branch.get('features'):
                    features = ', '.join(branch['features']) if isinstance(branch['features'], list) else branch['features']
                    response += f"✨ Features: {features}\n"

                response += "\nClick the 🗺️ Map button for directions!"

            else:
                # No branches, use main restaurant info
                response = f"📍 **{restaurant_name}** is located at:\n\n"
                response += f"🏠 {restaurant['address']}\n"
                response += f"📍 {restaurant['area']}, Islamabad\n"

                if restaurant.get('features'):
                    features = ', '.join(restaurant['features']) if isinstance(restaurant['features'], list) else restaurant['features']
                    response += f"✨ Features: {features}\n"

                response += "\nClick the 🗺️ Map button for directions!"

            return {
                "response": response,
                "restaurants": []
            }

        except Exception as e:
            print(f"Error fetching location info: {e}")
            return {
                "response": f"Sorry, I encountered an error while looking up {restaurant_name}'s location. Please try again.",
                "restaurants": []
            }

    def _validate_ai_response(self, ai_response: str, restaurant_cards: List[Dict], user_query: str) -> str:
        """
        CRITICAL: Validate AI response to prevent hallucinations
        """
        try:
            # Extract all restaurant names and menu items from actual data
            valid_restaurants = set()
            valid_menu_items = set()
            valid_prices = set()

            for card in restaurant_cards:
                valid_restaurants.add(card['name'].lower())
                valid_menu_items.add(card['item_name'].lower())
                # Extract price number
                price_str = card['price'].replace('Rs', '').replace(',', '').strip()
                try:
                    valid_prices.add(int(price_str))
                except:
                    pass

            # Check if AI mentioned any restaurant not in our data
            response_lower = ai_response.lower()

            # List of common hallucination patterns
            hallucination_indicators = [
                'kfc', 'mcdonalds', 'pizza hut', 'dominos', 'subway', 'burger king',
                'hardees', 'papa johns', 'dunkin', 'starbucks'  # Common chains not in our data
            ]

            # Check for hallucinated restaurants
            for indicator in hallucination_indicators:
                if indicator in response_lower:
                    print(f"WARNING: AI mentioned {indicator} which is not in our data")
                    return self._generate_safe_response(restaurant_cards, user_query)

            # Check if AI mentioned prices not in our data
            import re
            mentioned_prices = re.findall(r'rs\s*(\d+)', response_lower)
            for price_str in mentioned_prices:
                try:
                    price = int(price_str)
                    if price not in valid_prices:
                        print(f"WARNING: AI mentioned price Rs{price} not in our data")
                        return self._generate_safe_response(restaurant_cards, user_query)
                except:
                    pass

            # If validation passes, return original response
            return ai_response

        except Exception as e:
            print(f"Error in validation: {e}")
            return self._generate_safe_response(restaurant_cards, user_query)

    def _generate_safe_response(self, restaurant_cards: List[Dict], user_query: str) -> str:
        """
        Generate a safe response using only verified data
        """
        if not restaurant_cards:
            return "Sorry, I couldn't find any restaurants matching your request. Please try a different search."

        # Get user name if available
        user_name = "there"  # Default

        # Create safe response with only verified data
        if len(restaurant_cards) == 1:
            card = restaurant_cards[0]
            return f"Hey {user_name}! I found {card['item_name']} at {card['name']} for {card['price']}. Check it out!"

        elif len(restaurant_cards) <= 3:
            items = []
            for card in restaurant_cards:
                items.append(f"{card['item_name']} at {card['name']} for {card['price']}")

            return f"Hey {user_name}! Found some options: {', '.join(items)}. All verified from our database!"

        else:
            # Multiple options
            first_three = restaurant_cards[:3]
            items = []
            for card in first_three:
                items.append(f"{card['item_name']} at {card['name']} for {card['price']}")

            return f"Hey {user_name}! Found several options including {', '.join(items)}. Check out all the cards below!"


# Create global instance
chat_service = ChatService()
