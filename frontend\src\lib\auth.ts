/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Authors: <AUTHORS>
 * Module: Authentication utilities and API calls
 */

import { API_CONFIG } from './constants';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  phone: string;
  city: string;
  age_group: string;
  dietary_restrictions: string[];
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
}

export interface UserProfile {
  id?: string;
  name: string;
  email: string;
  role?: string;
  phone?: string;
  city?: string;
  age_group?: string;
  dietary_restrictions?: string[];
}

/**
 * Login user with email and password
 */
export async function loginUser(credentials: LoginCredentials): Promise<AuthResponse> {
  const formData = new FormData();
  formData.append('username', credentials.email); // OAuth2 uses 'username' field
  formData.append('password', credentials.password);

  const response = await fetch(`${API_CONFIG.baseUrl}/api/v1/auth/login`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.detail || 'Login failed');
  }

  return response.json();
}

/**
 * Register new user
 */
export async function registerUser(userData: RegisterData): Promise<UserProfile> {
  const response = await fetch(`${API_CONFIG.baseUrl}/api/v1/auth/register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(userData),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.detail || 'Registration failed');
  }

  return response.json();
}

/**
 * Get user profile using access token
 */
export async function getUserProfile(token: string): Promise<UserProfile> {
  const response = await fetch(`${API_CONFIG.baseUrl}/api/v1/auth/profile`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.detail || 'Failed to get user profile');
  }

  return response.json();
}

/**
 * Update user profile
 */
export async function updateUserProfile(
  token: string, 
  profileData: Partial<UserProfile>
): Promise<UserProfile> {
  const response = await fetch(`${API_CONFIG.baseUrl}/api/v1/auth/profile`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(profileData),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.detail || 'Failed to update profile');
  }

  return response.json();
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(): boolean {
  if (typeof window === 'undefined') return false;
  const token = localStorage.getItem('access_token');
  return !!token;
}

/**
 * Get stored access token
 */
export function getAccessToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('access_token');
}

/**
 * Store access token
 */
export function setAccessToken(token: string): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem('access_token', token);
}

/**
 * Remove access token (logout)
 */
export function removeAccessToken(): void {
  if (typeof window === 'undefined') return;
  localStorage.removeItem('access_token');
}

/**
 * Logout user
 */
export function logoutUser(): void {
  removeAccessToken();
  // Clear any other user data from localStorage
  localStorage.removeItem('user_profile');

  // Clear chat store data
  if (typeof window !== 'undefined') {
    localStorage.removeItem('rotishoti-chat-storage');
  }

  // Redirect to login page
  if (typeof window !== 'undefined') {
    window.location.href = '/login';
  }
}

/**
 * Get user data from localStorage
 */
export function getStoredUser(): UserProfile | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const userData = localStorage.getItem('user_profile');
    return userData ? JSON.parse(userData) : null;
  } catch {
    return null;
  }
}

/**
 * Store user data in localStorage
 */
export function setStoredUser(user: UserProfile): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem('user_profile', JSON.stringify(user));
}

/**
 * Validate Pakistani phone number
 */
export function validatePhoneNumber(phone: string): boolean {
  const phoneRegex = /^(\+92|0)?[0-9]{10}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Format phone number for display
 */
export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.startsWith('92')) {
    return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`;
  }
  
  if (cleaned.startsWith('0')) {
    return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
  }
  
  return phone;
}

/**
 * Check if email is valid
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Auth error handler
 */
export function handleAuthError(error: any): string {
  if (error.message) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'An unexpected error occurred. Please try again.';
}

/**
 * Auto-refresh token (if needed in the future)
 */
export async function refreshToken(): Promise<boolean> {
  // This would be implemented if the backend supports refresh tokens
  // For now, just return false to indicate no refresh capability
  return false;
}

/**
 * Check if token is expired (basic check)
 */
export function isTokenExpired(token: string): boolean {
  try {
    // Basic JWT token expiry check
    // In a real app, you'd decode the JWT and check the exp claim
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Date.now() / 1000;
    return payload.exp < currentTime;
  } catch {
    // If we can't parse the token, consider it expired
    return true;
  }
}

/**
 * Auth API wrapper with error handling
 */
export const authAPI = {
  login: loginUser,
  register: registerUser,
  getProfile: getUserProfile,
  updateProfile: updateUserProfile,
  logout: logoutUser,
  isAuthenticated,
  getToken: getAccessToken,
  setToken: setAccessToken,
  removeToken: removeAccessToken,
};
