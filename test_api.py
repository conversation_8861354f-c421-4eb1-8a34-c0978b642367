#!/usr/bin/env python3
"""
Test script for RotiShoti Chat API
"""

import requests
import json
import time

def test_chat_api():
    """Test the chat API with burger query"""
    
    url = "http://127.0.0.1:8000/api/v1/chat/chat"
    
    # Test data
    test_data = {
        "user_query": "Burgers under 2000 rs",
        "location": "Islamabad", 
        "budget": "medium",
        "user_profile": {
            "name": "Test User",
            "food_personality": "adventurous",
            "favorite_cuisines": ["Pakistani"],
            "spice_level": "medium",
            "dining_style": ["casual"],
            "taste_profile": {}
        },
        "conversation_history": []
    }
    
    print("🔍 Testing Chat API...")
    print(f"📤 Request URL: {url}")
    print(f"📤 Request Data: {json.dumps(test_data, indent=2)}")
    print("\n" + "="*50)
    
    try:
        start_time = time.time()
        
        response = requests.post(
            url,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️  Response Time: {response_time:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("\n✅ SUCCESS!")
            print(f"📝 AI Response: {result.get('response', 'No response')}")
            print(f"🍽️  Number of Restaurants: {len(result.get('restaurants', []))}")
            
            # Check restaurants
            restaurants = result.get('restaurants', [])
            if restaurants:
                print("\n🏪 Restaurant Details:")
                for i, restaurant in enumerate(restaurants, 1):
                    print(f"\n{i}. {restaurant.get('name', 'Unknown')}")
                    print(f"   📍 Area: {restaurant.get('area', 'Unknown')}")
                    print(f"   🍔 Item: {restaurant.get('item_name', 'Unknown')}")
                    print(f"   💰 Price: ₨{restaurant.get('price', 'Unknown')}")
                    
                    # Check if it's actually a burger
                    item_name = restaurant.get('item_name', '').lower()
                    if 'burger' in item_name:
                        print(f"   ✅ CORRECT: This is a burger item")
                    else:
                        print(f"   ❌ ERROR: This is NOT a burger item!")
                        
                    # Check price constraint
                    try:
                        price = int(restaurant.get('price', 0))
                        if price <= 2000:
                            print(f"   ✅ CORRECT: Price ₨{price} is under ₨2000")
                        else:
                            print(f"   ❌ ERROR: Price ₨{price} is over ₨2000!")
                    except:
                        print(f"   ⚠️  WARNING: Could not parse price")
            else:
                print("\n❌ No restaurants returned")
                
        else:
            print(f"\n❌ ERROR: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ ERROR: Request timed out")
    except requests.exceptions.ConnectionError:
        print("🔌 ERROR: Could not connect to server")
    except Exception as e:
        print(f"💥 ERROR: {str(e)}")

if __name__ == "__main__":
    test_chat_api()
