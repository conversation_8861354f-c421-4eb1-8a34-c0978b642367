"use client";

import React from "react";
import { useUserStore } from "@/store/userStore";
import Link from "next/link";

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export default function AuthGuard({ children, fallback }: AuthGuardProps) {
  const { isAuthenticated, currentUser } = useUserStore();

  if (!isAuthenticated || !currentUser) {
    return fallback || <AuthRequired />;
  }

  return <>{children}</>;
}

function AuthRequired() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
        {/* Logo/Icon */}
        <div className="mb-6">
          <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-3xl">🍽️</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome to RotiShoti!
          </h1>
          <p className="text-gray-600">
            Your AI-powered Pakistani food discovery companion
          </p>
        </div>

        {/* Features */}
        <div className="mb-8 space-y-3">
          <div className="flex items-center text-left">
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
              <span className="text-orange-600">🤖</span>
            </div>
            <span className="text-gray-700">
              AI-powered restaurant recommendations
            </span>
          </div>
          <div className="flex items-center text-left">
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
              <span className="text-orange-600">💬</span>
            </div>
            <span className="text-gray-700">Personalized chat experience</span>
          </div>
          <div className="flex items-center text-left">
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
              <span className="text-orange-600">📍</span>
            </div>
            <span className="text-gray-700">
              Discover amazing Pakistani cuisine
            </span>
          </div>
        </div>

        {/* Call to Action */}
        <div className="space-y-4">
          <p className="text-gray-800 font-medium">
            Please sign in or create an account to start your culinary journey!
          </p>

          <div className="space-y-3">
            <Link
              href="/login"
              className="w-full bg-gradient-to-r from-orange-500 to-yellow-500 text-white font-semibold py-3 px-6 rounded-lg hover:from-orange-600 hover:to-yellow-600 transition-all duration-200 transform hover:scale-105 shadow-lg block"
            >
              Sign In
            </Link>

            {/* <Link
              href="/register"
              className="w-full bg-white border-2 border-orange-500 text-orange-500 font-semibold py-3 px-6 rounded-lg hover:bg-orange-50 transition-all duration-200 block"
            >
              Create Account
            </Link> */}
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Join thousands of food lovers discovering the best Pakistani
            restaurants in Islamabad!
          </p>
        </div>
      </div>
    </div>
  );
}
