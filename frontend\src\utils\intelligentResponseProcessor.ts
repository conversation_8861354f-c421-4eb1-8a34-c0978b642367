/**
 * Intelligent Response Processor for RotiShoti AI
 * Enhances AI responses with context awareness and smart formatting
 */

interface RestaurantCard {
  id?: string;
  name: string;
  area: string;
  address: string;
  rating: string;
  item_name: string;
  price: string;
  map_embed_url: string;
  latitude?: number;
  longitude?: number;
  cuisine_types?: string[];
  phone?: string;
  price_range_min?: number;
  price_range_max?: number;
  image_url?: string;
}

interface Message {
  id: string;
  type: 'user' | 'bot' | 'system';
  content: string;
  timestamp: Date;
  restaurants?: RestaurantCard[];
}

interface UserProfile {
  name?: string;
  budget_preference?: string;
  favorite_cuisines?: string[];
  dietary_restrictions?: string[];
  personality?: 'friendly' | 'professional' | 'enthusiastic';
}

export class IntelligentResponseProcessor {
  
  /**
   * Analyze query type and intent
   */
  static analyzeQuery(query: string): {
    type: 'search' | 'comparison' | 'recommendation' | 'location' | 'menu' | 'general';
    intent: string;
    keywords: string[];
    budget?: { min: number; max: number };
    cuisine?: string[];
    location?: string;
  } {
    const lowerQuery = query.toLowerCase();
    
    // Extract budget information
    const budgetMatch = lowerQuery.match(/under\s+(?:rs\s*)?(\d+)|below\s+(?:rs\s*)?(\d+)|less\s+than\s+(?:rs\s*)?(\d+)/);
    const budget = budgetMatch ? { min: 0, max: parseInt(budgetMatch[1] || budgetMatch[2] || budgetMatch[3]) } : undefined;
    
    // Extract cuisine types
    const cuisineKeywords = ['pakistani', 'chinese', 'italian', 'fast food', 'desi', 'continental', 'bbq', 'pizza', 'burger', 'biryani', 'karahi'];
    const cuisine = cuisineKeywords.filter(c => lowerQuery.includes(c));
    
    // Extract location
    const locationMatch = lowerQuery.match(/in\s+([a-z0-9-]+)|near\s+([a-z0-9-]+)|at\s+([a-z0-9-]+)/);
    const location = locationMatch ? locationMatch[1] || locationMatch[2] || locationMatch[3] : undefined;
    
    // Determine query type
    let type: 'search' | 'comparison' | 'recommendation' | 'location' | 'menu' | 'general' = 'general';
    let intent = 'general_inquiry';
    
    if (lowerQuery.includes('recommend') || lowerQuery.includes('suggest') || lowerQuery.includes('best')) {
      type = 'recommendation';
      intent = 'get_recommendations';
    } else if (lowerQuery.includes('compare') || lowerQuery.includes('vs') || lowerQuery.includes('versus')) {
      type = 'comparison';
      intent = 'compare_options';
    } else if (lowerQuery.includes('where') || lowerQuery.includes('location') || lowerQuery.includes('address')) {
      type = 'location';
      intent = 'find_location';
    } else if (lowerQuery.includes('menu') || lowerQuery.includes('dishes') || lowerQuery.includes('items')) {
      type = 'menu';
      intent = 'view_menu';
    } else if (cuisine.length > 0 || budget || location) {
      type = 'search';
      intent = 'search_restaurants';
    }
    
    const keywords = lowerQuery.split(' ').filter(word => word.length > 2);
    
    return { type, intent, keywords, budget, cuisine, location };
  }

  /**
   * Enhance AI response based on context and user profile
   */
  static enhanceResponse(
    originalResponse: string,
    restaurants: RestaurantCard[],
    userProfile?: UserProfile,
    conversationHistory?: Message[]
  ): string {
    let enhancedResponse = originalResponse;
    
    // Add personality-based enhancements
    if (userProfile?.personality === 'enthusiastic') {
      enhancedResponse = this.addEnthusiasticTone(enhancedResponse, restaurants);
    } else if (userProfile?.personality === 'professional') {
      enhancedResponse = this.addProfessionalTone(enhancedResponse, restaurants);
    } else {
      enhancedResponse = this.addFriendlyTone(enhancedResponse, restaurants);
    }
    
    // Add context from conversation history
    if (conversationHistory && conversationHistory.length > 0) {
      enhancedResponse = this.addContextualReferences(enhancedResponse, conversationHistory);
    }
    
    // Add personalized recommendations
    if (userProfile) {
      enhancedResponse = this.addPersonalizedInsights(enhancedResponse, restaurants, userProfile);
    }
    
    return enhancedResponse;
  }

  private static addEnthusiasticTone(response: string, restaurants: RestaurantCard[]): string {
    const enthusiasticPrefixes = [
      "OMG, you're in for a TREAT! 🎉",
      "WOW! I found some AMAZING spots! 🔥",
      "This is SO exciting! 🚀",
      "You're gonna LOVE these places! 💖"
    ];
    
    const prefix = enthusiasticPrefixes[Math.floor(Math.random() * enthusiasticPrefixes.length)];
    
    if (restaurants.length > 0) {
      return `${prefix} ${response} These places are absolutely INCREDIBLE! 🌟`;
    }
    
    return `${prefix} ${response}`;
  }

  private static addProfessionalTone(response: string, restaurants: RestaurantCard[]): string {
    if (restaurants.length > 0) {
      return `Based on your criteria, I've identified ${restaurants.length} establishments that meet your requirements. ${response} Each recommendation has been carefully evaluated for quality, value, and relevance to your preferences.`;
    }
    
    return `${response} I'm here to provide comprehensive dining recommendations tailored to your specific needs.`;
  }

  private static addFriendlyTone(response: string, restaurants: RestaurantCard[]): string {
    const friendlyPrefixes = [
      "Great question! 😊",
      "I'd be happy to help! 🍽️",
      "Perfect timing for some food exploration! 🎯",
      "Let me share some tasty options! 😋"
    ];
    
    if (restaurants.length > 0) {
      const prefix = friendlyPrefixes[Math.floor(Math.random() * friendlyPrefixes.length)];
      return `${prefix} ${response} Hope you find something delicious! 🌟`;
    }
    
    return response;
  }

  private static addContextualReferences(response: string, history: Message[]): string {
    const recentRestaurants = history
      .filter(m => m.restaurants && m.restaurants.length > 0)
      .flatMap(m => m.restaurants || [])
      .slice(-3);
    
    if (recentRestaurants.length > 0) {
      const restaurantNames = recentRestaurants.map(r => r.name).join(', ');
      return `${response} Since you were also interested in ${restaurantNames}, you might enjoy exploring similar options in those areas too!`;
    }
    
    return response;
  }

  private static addPersonalizedInsights(
    response: string, 
    restaurants: RestaurantCard[], 
    userProfile: UserProfile
  ): string {
    let insights: string[] = [];
    
    // Budget insights
    if (userProfile.budget_preference === 'low' && restaurants.some(r => parseInt(r.price) < 500)) {
      insights.push("I made sure to include budget-friendly options under Rs 500! 💰");
    }
    
    // Cuisine preferences
    if (userProfile.favorite_cuisines?.length) {
      const matchingCuisines = restaurants.filter(r => 
        r.cuisine_types?.some(c => userProfile.favorite_cuisines?.includes(c))
      );
      if (matchingCuisines.length > 0) {
        insights.push(`Found ${matchingCuisines.length} places serving your favorite cuisines! 🎯`);
      }
    }
    
    // Dietary restrictions
    if (userProfile.dietary_restrictions?.includes('vegetarian')) {
      insights.push("I've highlighted vegetarian-friendly options for you! 🥗");
    }
    
    if (insights.length > 0) {
      return `${response}\n\n💡 Personal note: ${insights.join(' ')}`;
    }
    
    return response;
  }

  /**
   * Generate smart follow-up suggestions
   */
  static generateFollowUpSuggestions(
    query: string,
    restaurants: RestaurantCard[],
    userProfile?: UserProfile
  ): string[] {
    const analysis = this.analyzeQuery(query);
    const suggestions: string[] = [];
    
    if (restaurants.length > 0) {
      suggestions.push("🗺️ Show me locations on map");
      suggestions.push("💰 Find cheaper alternatives");
      suggestions.push("⭐ Show higher rated options");
      
      if (analysis.type === 'search') {
        suggestions.push("🍽️ Similar restaurants nearby");
        suggestions.push("📋 View full menus");
      }
      
      if (restaurants.length > 1) {
        suggestions.push("🔄 Compare these options");
      }
    } else {
      suggestions.push("🔍 Try different search terms");
      suggestions.push("📍 Search in different areas");
      suggestions.push("💡 Get general recommendations");
    }
    
    return suggestions.slice(0, 4); // Limit to 4 suggestions
  }

  /**
   * Format response for better readability
   */
  static formatResponse(response: string): string {
    // Add proper spacing and formatting
    return response
      .replace(/\n\n+/g, '\n\n') // Remove excessive line breaks
      .replace(/([.!?])\s*([A-Z])/g, '$1 $2') // Ensure proper spacing after sentences
      .trim();
  }
}
