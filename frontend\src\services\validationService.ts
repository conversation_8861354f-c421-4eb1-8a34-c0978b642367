export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

export class ValidationService {
  static validateChatMessage(message: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!message || message.trim().length === 0) {
      errors.push('Message cannot be empty');
    }

    if (message.length > 1000) {
      errors.push('Message is too long (maximum 1000 characters)');
    }

    if (message.length < 3) {
      warnings.push('Very short message might not provide enough context');
    }

    const suspiciousPatterns = [
      /script/i,
      /<[^>]*>/,
      /javascript:/i
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(message))) {
      errors.push('Message contains invalid characters');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  static validateBudgetRange(min: number, max: number): ValidationResult {
    const errors: string[] = [];

    if (min < 0 || max < 0) {
      errors.push('Budget values must be positive');
    }

    if (min >= max) {
      errors.push('Minimum budget must be less than maximum budget');
    }

    if (max > 50000) {
      errors.push('Maximum budget seems unrealistic');
    }

    if (min < 100) {
      errors.push('Minimum budget is too low');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static validateEmail(email: string): ValidationResult {
    const errors: string[] = [];

    if (!email || email.trim().length === 0) {
      errors.push('Email is required');
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      errors.push('Invalid email format');
    }

    if (email.length > 254) {
      errors.push('Email is too long');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static validatePassword(password: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!password || password.length === 0) {
      errors.push('Password is required');
    }

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      warnings.push('Password should contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      warnings.push('Password should contain at least one lowercase letter');
    }

    if (!/\d/.test(password)) {
      warnings.push('Password should contain at least one number');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      warnings.push('Password should contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  static validateRestaurantSearch(query: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!query || query.trim().length === 0) {
      errors.push('Search query cannot be empty');
    }

    if (query.length > 200) {
      errors.push('Search query is too long');
    }

    if (query.length < 2) {
      warnings.push('Very short queries might not return relevant results');
    }

    const commonMisspellings = {
      'resturant': 'restaurant',
      'restarant': 'restaurant',
      'restraunt': 'restaurant'
    };

    Object.entries(commonMisspellings).forEach(([wrong, correct]) => {
      if (query.toLowerCase().includes(wrong)) {
        warnings.push(`Did you mean "${correct}" instead of "${wrong}"?`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
