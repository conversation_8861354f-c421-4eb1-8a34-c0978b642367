from fastapi import APIRouter
from app.api.v1 import auth, restaurants, recommendations, users, chat

api_router = APIRouter()

api_router.include_router(auth.router, prefix="/v1/auth", tags=["auth"])
api_router.include_router(restaurants.router, prefix="/v1/restaurants", tags=["restaurants"])
api_router.include_router(recommendations.router, prefix="/v1/recommendations", tags=["recommendations"])
api_router.include_router(users.router, prefix="/v1/users", tags=["users"])
api_router.include_router(chat.router, prefix="/v1/chat", tags=["chat"])