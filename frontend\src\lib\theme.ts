/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Authors: <AUTHORS>
 * Module: Professional theme configuration and design system
 */

// Pakistani-inspired color palette
export const COLORS = {
  // Primary colors (Pakistani flag inspired)
  primary: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e', // Main green
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  
  // Secondary colors (Orange/Saffron inspired)
  secondary: {
    50: '#fff7ed',
    100: '#ffedd5',
    200: '#fed7aa',
    300: '#fdba74',
    400: '#fb923c',
    500: '#f97316', // Main orange
    600: '#ea580c',
    700: '#c2410c',
    800: '#9a3412',
    900: '#7c2d12',
  },
  
  // Neutral colors
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
  },
  
  // Semantic colors
  success: '#22c55e',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',
  
  // Food-themed colors
  food: {
    spicy: '#dc2626',
    mild: '#fbbf24',
    sweet: '#ec4899',
    savory: '#8b5cf6',
    fresh: '#10b981',
  },
} as const;

// Typography scale
export const TYPOGRAPHY = {
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    serif: ['Playfair Display', 'serif'],
    mono: ['JetBrains Mono', 'monospace'],
    urdu: ['Noto Nastaliq Urdu', 'serif'], // For Urdu text
  },
  
  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem' }],
    sm: ['0.875rem', { lineHeight: '1.25rem' }],
    base: ['1rem', { lineHeight: '1.5rem' }],
    lg: ['1.125rem', { lineHeight: '1.75rem' }],
    xl: ['1.25rem', { lineHeight: '1.75rem' }],
    '2xl': ['1.5rem', { lineHeight: '2rem' }],
    '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
    '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
    '5xl': ['3rem', { lineHeight: '1' }],
    '6xl': ['3.75rem', { lineHeight: '1' }],
  },
  
  fontWeight: {
    thin: '100',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },
} as const;

// Spacing scale
export const SPACING = {
  px: '1px',
  0: '0',
  0.5: '0.125rem',
  1: '0.25rem',
  1.5: '0.375rem',
  2: '0.5rem',
  2.5: '0.625rem',
  3: '0.75rem',
  3.5: '0.875rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  7: '1.75rem',
  8: '2rem',
  9: '2.25rem',
  10: '2.5rem',
  11: '2.75rem',
  12: '3rem',
  14: '3.5rem',
  16: '4rem',
  20: '5rem',
  24: '6rem',
  28: '7rem',
  32: '8rem',
  36: '9rem',
  40: '10rem',
  44: '11rem',
  48: '12rem',
  52: '13rem',
  56: '14rem',
  60: '15rem',
  64: '16rem',
  72: '18rem',
  80: '20rem',
  96: '24rem',
} as const;

// Border radius scale
export const BORDER_RADIUS = {
  none: '0',
  sm: '0.125rem',
  DEFAULT: '0.25rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  '3xl': '1.5rem',
  full: '9999px',
} as const;

// Shadow scale
export const SHADOWS = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  none: 'none',
  
  // Custom shadows
  glow: '0 0 20px rgba(34, 197, 94, 0.3)',
  'glow-orange': '0 0 20px rgba(249, 115, 22, 0.3)',
  'card-hover': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
} as const;

// Breakpoints
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Z-index scale
export const Z_INDEX = {
  auto: 'auto',
  0: '0',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50',
  
  // Semantic z-index values
  dropdown: '1000',
  sticky: '1020',
  fixed: '1030',
  modal: '1040',
  popover: '1050',
  tooltip: '1060',
  toast: '1070',
} as const;

// Component variants
export const COMPONENT_VARIANTS = {
  button: {
    primary: {
      base: 'bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500',
      disabled: 'bg-neutral-300 text-neutral-500 cursor-not-allowed',
    },
    secondary: {
      base: 'bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500',
      disabled: 'bg-neutral-300 text-neutral-500 cursor-not-allowed',
    },
    outline: {
      base: 'border-2 border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500',
      disabled: 'border-neutral-300 text-neutral-500 cursor-not-allowed',
    },
    ghost: {
      base: 'text-primary-500 hover:bg-primary-50 focus:ring-primary-500',
      disabled: 'text-neutral-500 cursor-not-allowed',
    },
  },
  
  card: {
    default: 'bg-white rounded-lg shadow-md border border-neutral-200',
    elevated: 'bg-white rounded-xl shadow-lg border border-neutral-200',
    interactive: 'bg-white rounded-lg shadow-md border border-neutral-200 hover:shadow-lg transition-shadow duration-200',
  },
  
  input: {
    default: 'border border-neutral-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
    error: 'border border-error rounded-md px-3 py-2 focus:ring-2 focus:ring-error focus:border-error',
    success: 'border border-success rounded-md px-3 py-2 focus:ring-2 focus:ring-success focus:border-success',
  },
} as const;

// Animation presets
export const ANIMATION_PRESETS = {
  transition: {
    fast: 'transition-all duration-150 ease-in-out',
    normal: 'transition-all duration-300 ease-in-out',
    slow: 'transition-all duration-500 ease-in-out',
  },
  
  hover: {
    scale: 'hover:scale-105 transition-transform duration-200',
    lift: 'hover:-translate-y-1 hover:shadow-lg transition-all duration-200',
    glow: 'hover:shadow-glow transition-shadow duration-300',
  },
  
  focus: {
    ring: 'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'ring-primary': 'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
    'ring-secondary': 'focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2',
  },
} as const;

// Pakistani cultural elements
export const CULTURAL_ELEMENTS = {
  patterns: {
    geometric: 'bg-gradient-to-r from-primary-500 via-secondary-500 to-primary-500',
    paisley: 'bg-gradient-to-br from-primary-400 to-secondary-400',
    floral: 'bg-gradient-to-tr from-primary-300 to-secondary-300',
  },
  
  colors: {
    flag: {
      green: '#01411C',
      white: '#FFFFFF',
      crescent: '#FFFFFF',
      star: '#FFFFFF',
    },
    
    traditional: {
      emerald: '#50C878',
      saffron: '#F4C430',
      crimson: '#DC143C',
      gold: '#FFD700',
      ivory: '#FFFFF0',
    },
  },
  
  typography: {
    urdu: 'font-urdu text-right',
    english: 'font-sans text-left',
    mixed: 'font-sans',
  },
} as const;

// Theme configuration object
export const THEME = {
  colors: COLORS,
  typography: TYPOGRAPHY,
  spacing: SPACING,
  borderRadius: BORDER_RADIUS,
  shadows: SHADOWS,
  breakpoints: BREAKPOINTS,
  zIndex: Z_INDEX,
  components: COMPONENT_VARIANTS,
  animations: ANIMATION_PRESETS,
  cultural: CULTURAL_ELEMENTS,
} as const;

// Utility functions
export const getColor = (color: string, shade?: number) => {
  if (shade && color in COLORS) {
    const colorObj = COLORS[color as keyof typeof COLORS];
    if (typeof colorObj === 'object' && shade in colorObj) {
      return colorObj[shade as keyof typeof colorObj];
    }
  }
  return color;
};

export const getSpacing = (size: string | number) => {
  if (typeof size === 'string' && size in SPACING) {
    return SPACING[size as keyof typeof SPACING];
  }
  return size;
};

export const getShadow = (shadow: string) => {
  if (shadow in SHADOWS) {
    return SHADOWS[shadow as keyof typeof SHADOWS];
  }
  return shadow;
};

// Export default theme
export default THEME;
