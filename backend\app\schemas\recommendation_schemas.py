
from pydantic import BaseModel
from typing import List, Optional
from enum import Enum

class BudgetLevel(str, Enum):
    low = "low"
    medium = "medium"
    high = "high"

class RecommendationQuery(BaseModel):
    user_query: str
    user_id: Optional[str] = None  # Optional, if recommendations can be anonymous
    location: Optional[str] = None
    budget: Optional[BudgetLevel] = None
    dietary_restrictions: Optional[List[str]] = None

class RecommendationResponse(BaseModel):
    restaurant_id: str
    name: str
    cuisine: str
    address: str
    rating: float
    price_range: str
    ai_description: str
    menu_items_recommended: Optional[List[str]] = None

class Feedback(BaseModel):
    recommendation_id: str
    user_id: str
    helpful: bool
    comment: Optional[str] = None