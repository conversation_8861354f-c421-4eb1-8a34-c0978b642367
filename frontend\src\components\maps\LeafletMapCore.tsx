'use client';

import { useEffect, useRef, useState } from 'react';

interface Restaurant {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  rating: number;
  cuisine_types: string[];
  price_range_min: number;
  price_range_max: number;
  phone?: string;
  image_url?: string;
  distance?: number;
}

interface LeafletMapCoreProps {
  restaurants: Restaurant[];
  userLocation?: { lat: number; lng: number };
  selectedRestaurant?: Restaurant;
  onRestaurantSelect?: (restaurant: Restaurant) => void;
  height?: string;
  showUserLocation?: boolean;
  maxDistance?: number;
}

const LeafletMapCore: React.FC<LeafletMapCoreProps> = ({
  restaurants,
  userLocation,
  selectedRestaurant,
  onRestaurantSelect,
  height = '400px',
  showUserLocation = true,
  maxDistance = 15
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markersRef = useRef<any[]>([]);
  const userMarkerRef = useRef<any>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [L, setL] = useState<any>(null);

  // Islamabad center coordinates
  const islamabadCenter = { lat: 33.6844, lng: 73.0479 };

  // Load Leaflet dynamically
  useEffect(() => {
    const loadLeaflet = async () => {
      try {
        // Dynamic import of Leaflet
        const leaflet = await import('leaflet');
        
        // Import CSS
        await import('leaflet/dist/leaflet.css');

        // Fix default markers
        delete (leaflet.Icon.Default.prototype as any)._getIconUrl;
        leaflet.Icon.Default.mergeOptions({
          iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
          iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
          shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
        });

        setL(leaflet);
        setIsLoaded(true);
      } catch (error) {
        console.error('Failed to load Leaflet:', error);
      }
    };

    loadLeaflet();
  }, []);

  // Initialize map
  useEffect(() => {
    if (!isLoaded || !L || !mapRef.current) return;

    // Create map
    const map = L.map(mapRef.current, {
      zoomControl: true,
      scrollWheelZoom: true,
      doubleClickZoom: true,
      touchZoom: true,
    }).setView([islamabadCenter.lat, islamabadCenter.lng], 12);

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors',
      maxZoom: 19,
    }).addTo(map);

    mapInstanceRef.current = map;

    // Add user location marker if available
    if (userLocation && showUserLocation) {
      const userIcon = L.divIcon({
        html: `
          <div style="
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
          ">
            <div style="
              width: 8px;
              height: 8px;
              background: white;
              border-radius: 50%;
            "></div>
          </div>
        `,
        className: 'user-location-marker',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
      });

      userMarkerRef.current = L.marker([userLocation.lat, userLocation.lng], { icon: userIcon })
        .addTo(map)
        .bindPopup(`
          <div style="text-align: center; font-family: Inter, sans-serif;">
            <div style="font-weight: bold; color: #1f2937; margin-bottom: 4px;">📍 Your Location</div>
            <div style="font-size: 12px; color: #6b7280;">Islamabad, Pakistan</div>
          </div>
        `);

      // Center map on user location
      map.setView([userLocation.lat, userLocation.lng], 13);
    }

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
      markersRef.current = [];
      userMarkerRef.current = null;
    };
  }, [isLoaded, L, userLocation, showUserLocation]);

  // Add restaurant markers
  useEffect(() => {
    if (!mapInstanceRef.current || !L || !restaurants.length) return;

    // Clear existing markers
    markersRef.current.forEach(marker => {
      mapInstanceRef.current.removeLayer(marker);
    });
    markersRef.current = [];

    // Filter restaurants by distance if user location is available
    let filteredRestaurants = restaurants;
    if (userLocation && maxDistance) {
      filteredRestaurants = restaurants.filter(restaurant => {
        const distance = calculateDistance(
          userLocation.lat, userLocation.lng,
          restaurant.latitude, restaurant.longitude
        );
        return distance <= maxDistance;
      });
    }

    // Sort by distance if user location available
    if (userLocation) {
      filteredRestaurants = filteredRestaurants
        .map(restaurant => ({
          ...restaurant,
          distance: calculateDistance(
            userLocation.lat, userLocation.lng,
            restaurant.latitude, restaurant.longitude
          )
        }))
        .sort((a, b) => (a.distance || 0) - (b.distance || 0));
    }

    // Create restaurant markers
    filteredRestaurants.forEach((restaurant, index) => {
      const isSelected = selectedRestaurant?.id === restaurant.id;
      const isClosest = index === 0 && userLocation;
      
      // Custom restaurant icon
      const restaurantIcon = L.divIcon({
        html: `
          <div style="
            background: ${isSelected ? 'linear-gradient(135deg, #ef4444, #dc2626)' : 
                        isClosest ? 'linear-gradient(135deg, #10b981, #059669)' : 
                        'linear-gradient(135deg, #f97316, #ea580c)'};
            width: ${isSelected ? '36px' : '30px'};
            height: ${isSelected ? '36px' : '30px'};
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: ${isSelected ? '18px' : '16px'};
            color: white;
            font-weight: bold;
            position: relative;
            transform: ${isSelected ? 'scale(1.1)' : 'scale(1)'};
            transition: all 0.3s ease;
          ">
            🍽️
            ${isClosest ? '<div style="position: absolute; top: -8px; right: -8px; background: #10b981; color: white; border-radius: 50%; width: 16px; height: 16px; display: flex; align-items: center; justify-content: center; font-size: 10px; border: 2px solid white;">1</div>' : ''}
          </div>
        `,
        className: 'restaurant-marker',
        iconSize: [isSelected ? 36 : 30, isSelected ? 36 : 30],
        iconAnchor: [isSelected ? 18 : 15, isSelected ? 18 : 15]
      });

      const marker = L.marker([restaurant.latitude, restaurant.longitude], { icon: restaurantIcon })
        .addTo(mapInstanceRef.current);

      // Create detailed popup
      const popupContent = `
        <div style="min-width: 280px; font-family: Inter, sans-serif; max-width: 320px;">
          <div style="margin-bottom: 12px;">
            <h3 style="margin: 0 0 8px 0; font-size: 18px; font-weight: 700; color: #1f2937; line-height: 1.2;">
              ${restaurant.name}
            </h3>
            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
              <span style="color: #f59e0b; font-size: 14px; font-weight: 600;">
                ${'⭐'.repeat(Math.floor(restaurant.rating))} ${restaurant.rating}/5
              </span>
              ${restaurant.distance ? `<span style="color: #10b981; font-size: 14px; font-weight: 600;">📍 ${restaurant.distance.toFixed(1)} km away</span>` : ''}
            </div>
          </div>
          
          <div style="margin-bottom: 12px;">
            <p style="margin: 0 0 6px 0; font-size: 14px; color: #4b5563; display: flex; align-items: center; gap: 6px;">
              <span>📍</span> ${restaurant.address}
            </p>
            <p style="margin: 0 0 6px 0; font-size: 14px; color: #4b5563; display: flex; align-items: center; gap: 6px;">
              <span>🍽️</span> ${restaurant.cuisine_types.slice(0, 2).join(', ')}
            </p>
            <p style="margin: 0 0 12px 0; font-size: 14px; color: #4b5563; display: flex; align-items: center; gap: 6px;">
              <span>💰</span> Rs ${restaurant.price_range_min} - Rs ${restaurant.price_range_max}
            </p>
          </div>

          <div style="display: flex; gap: 8px; margin-top: 12px;">
            <button 
              onclick="window.openRestaurantDetails('${restaurant.id}')"
              style="
                background: linear-gradient(135deg, #f97316, #ea580c);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-size: 12px;
                font-weight: 600;
                cursor: pointer;
                flex: 1;
                transition: all 0.2s;
              "
              onmouseover="this.style.transform='scale(1.05)'"
              onmouseout="this.style.transform='scale(1)'"
            >
              📋 View Menu
            </button>
            <button 
              onclick="window.getDirections(${restaurant.latitude}, ${restaurant.longitude})"
              style="
                background: linear-gradient(135deg, #3b82f6, #2563eb);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-size: 12px;
                font-weight: 600;
                cursor: pointer;
                flex: 1;
                transition: all 0.2s;
              "
              onmouseover="this.style.transform='scale(1.05)'"
              onmouseout="this.style.transform='scale(1)'"
            >
              🧭 Directions
            </button>
          </div>
          
          ${restaurant.phone ? `
            <button 
              onclick="window.callRestaurant('${restaurant.phone}')"
              style="
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-size: 12px;
                font-weight: 600;
                cursor: pointer;
                width: 100%;
                margin-top: 8px;
                transition: all 0.2s;
              "
              onmouseover="this.style.transform='scale(1.05)'"
              onmouseout="this.style.transform='scale(1)'"
            >
              📞 Call ${restaurant.phone}
            </button>
          ` : ''}
        </div>
      `;

      marker.bindPopup(popupContent, {
        maxWidth: 320,
        className: 'custom-popup'
      });

      // Handle marker click
      marker.on('click', () => {
        if (onRestaurantSelect) {
          onRestaurantSelect(restaurant);
        }
      });

      markersRef.current.push(marker);
    });

    // Fit map to show all markers
    if (filteredRestaurants.length > 0) {
      const group = L.featureGroup(markersRef.current);
      if (userMarkerRef.current) {
        group.addLayer(userMarkerRef.current);
      }
      mapInstanceRef.current.fitBounds(group.getBounds().pad(0.1));
    }

  }, [restaurants, selectedRestaurant, userLocation, maxDistance, L, onRestaurantSelect]);

  // Global functions for popup buttons
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).openRestaurantDetails = (restaurantId: string) => {
        const restaurant = restaurants.find(r => r.id === restaurantId);
        if (restaurant && onRestaurantSelect) {
          onRestaurantSelect(restaurant);
        }
      };

      (window as any).getDirections = (lat: number, lng: number) => {
        const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
        window.open(url, '_blank');
      };

      (window as any).callRestaurant = (phone: string) => {
        window.open(`tel:${phone}`, '_self');
      };
    }
  }, [restaurants, onRestaurantSelect]);

  // Calculate distance between two points
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  if (!isLoaded) {
    return (
      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-green-50 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading interactive map...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      <div ref={mapRef} className="w-full h-full rounded-lg overflow-hidden shadow-lg" />
      
      {/* Map Controls */}
      <div className="absolute top-4 right-4 z-[1000] flex flex-col gap-2">
        <button
          onClick={() => {
            if (mapInstanceRef.current) {
              const center = userLocation || islamabadCenter;
              mapInstanceRef.current.setView([center.lat, center.lng], userLocation ? 13 : 12);
            }
          }}
          className="bg-white hover:bg-gray-50 p-3 rounded-lg shadow-lg border border-gray-200 transition-all duration-200 hover:scale-105"
          title="Reset View"
        >
          <span className="text-lg">🏠</span>
        </button>
        
        {userLocation && (
          <button
            onClick={() => {
              if (mapInstanceRef.current && userLocation) {
                mapInstanceRef.current.setView([userLocation.lat, userLocation.lng], 15);
                if (userMarkerRef.current) {
                  userMarkerRef.current.openPopup();
                }
              }
            }}
            className="bg-white hover:bg-gray-50 p-3 rounded-lg shadow-lg border border-gray-200 transition-all duration-200 hover:scale-105"
            title="My Location"
          >
            <span className="text-lg">📍</span>
          </button>
        )}
      </div>

      {/* Distance Legend */}
      {userLocation && (
        <div className="absolute bottom-4 left-4 z-[1000] bg-white rounded-lg shadow-lg border border-gray-200 p-3">
          <div className="text-sm font-medium text-gray-900 mb-2">Distance Legend</div>
          <div className="space-y-1 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-gradient-to-r from-green-500 to-green-600"></div>
              <span>Closest Restaurant</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-gradient-to-r from-orange-500 to-orange-600"></div>
              <span>Other Restaurants</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-gradient-to-r from-blue-500 to-blue-600"></div>
              <span>Your Location</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeafletMapCore;
