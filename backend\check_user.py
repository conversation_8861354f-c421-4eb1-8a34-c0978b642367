#!/usr/bin/env python3
"""
Script to check and fix test user for RotiShoti
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.user_service import user_service
from app.core.jwt import get_password_hash, verify_password

async def check_and_fix_user():
    """Check the test user and fix password if needed"""
    try:
        email = "<EMAIL>"
        password = "pakistan123"
        
        print(f"Checking user: {email}")
        user = await user_service.get_user_by_email(email)
        
        if not user:
            print("❌ User not found!")
            return
            
        print(f"✅ User found!")
        print(f"   Name: {user.name}")
        print(f"   Email: {user.email}")
        print(f"   ID: {user.id}")
        
        # Test password verification
        print(f"\nTesting password verification...")
        is_valid = verify_password(password, user.password)
        print(f"Password valid: {is_valid}")
        
        if not is_valid:
            print("❌ Password doesn't match. Updating password...")
            
            # Update password
            new_password_hash = get_password_hash(password)
            
            # Update user password in database
            from app.db.supabase import create_supabase_client
            supabase = create_supabase_client()
            
            result = supabase.from_("users").update({
                "password": new_password_hash
            }).eq("email", email).execute()
            
            if result.data:
                print("✅ Password updated successfully!")
                
                # Test again
                updated_user = await user_service.get_user_by_email(email)
                is_valid_now = verify_password(password, updated_user.password)
                print(f"Password valid now: {is_valid_now}")
            else:
                print("❌ Failed to update password")
        else:
            print("✅ Password is correct!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(check_and_fix_user())
