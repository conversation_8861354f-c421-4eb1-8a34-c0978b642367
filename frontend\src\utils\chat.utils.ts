/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * 
 * @fileoverview Chat utility functions and helpers
 * @version 1.0.0
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 RotiShoti Technologies
 */

import { IChatMessage, IRestaurantCard } from '@/types/chat.types';
import { CHAT_CONFIG, CHAT_TEMPLATES } from '@/constants/chat.constants';

/**
 * Generates a unique message ID
 * @returns Unique string identifier
 */
export const generateMessageId = (): string => {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 9);
  return `msg_${timestamp}_${randomStr}`;
};

/**
 * Formats timestamp for display
 * @param timestamp - Date object or timestamp
 * @returns Formatted time string
 */
export const formatMessageTime = (timestamp: Date | number): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * Validates message content
 * @param content - Message content to validate
 * @returns Validation result
 */
export const validateMessageContent = (content: string): {
  isValid: boolean;
  error?: string;
} => {
  if (!content || content.trim().length === 0) {
    return { isValid: false, error: 'Message cannot be empty' };
  }
  
  if (content.length > CHAT_CONFIG.MAX_MESSAGE_LENGTH) {
    return { 
      isValid: false, 
      error: `Message too long (max ${CHAT_CONFIG.MAX_MESSAGE_LENGTH} characters)` 
    };
  }
  
  // Check for potentially harmful content
  const suspiciousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
  ];
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(content)) {
      return { isValid: false, error: 'Invalid content detected' };
    }
  }
  
  return { isValid: true };
};

/**
 * Sanitizes message content for display
 * @param content - Raw message content
 * @returns Sanitized content
 */
export const sanitizeMessageContent = (content: string): string => {
  return content
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

/**
 * Extracts restaurant data from message
 * @param message - Chat message
 * @returns Array of restaurant cards
 */
export const extractRestaurantData = (message: IChatMessage): IRestaurantCard[] => {
  return message.restaurants || [];
};

/**
 * Formats price for display
 * @param price - Price as string or number
 * @returns Formatted price string
 */
export const formatPrice = (price: string | number): string => {
  const numPrice = typeof price === 'string' ? parseInt(price, 10) : price;
  return `₨${numPrice.toLocaleString('en-PK')}`;
};

/**
 * Generates Google Maps direction URL
 * @param address - Restaurant address
 * @returns Google Maps direction URL
 */
export const generateDirectionsUrl = (address: string): string => {
  const encodedAddress = encodeURIComponent(address);
  return `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`;
};

/**
 * Converts map embed URL to regular Google Maps URL
 * @param embedUrl - Google Maps embed URL
 * @returns Regular Google Maps URL
 */
export const convertEmbedToMapsUrl = (embedUrl: string): string => {
  return embedUrl.replace('/embed', '');
};

/**
 * Debounces function calls
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttles function calls
 * @param func - Function to throttle
 * @param limit - Time limit in milliseconds
 * @returns Throttled function
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

/**
 * Gets random welcome message
 * @returns Random welcome message
 */
export const getRandomWelcomeMessage = (): string => {
  const messages = CHAT_TEMPLATES.WELCOME_MESSAGES;
  return messages[Math.floor(Math.random() * messages.length)];
};

/**
 * Gets random loading message
 * @returns Random loading message
 */
export const getRandomLoadingMessage = (): string => {
  const messages = CHAT_TEMPLATES.LOADING_MESSAGES;
  return messages[Math.floor(Math.random() * messages.length)];
};

/**
 * Checks if user is on mobile device
 * @returns Boolean indicating mobile device
 */
export const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth <= 768;
};

/**
 * Checks if user prefers reduced motion
 * @returns Boolean indicating reduced motion preference
 */
export const prefersReducedMotion = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Copies text to clipboard
 * @param text - Text to copy
 * @returns Promise resolving to success status
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const success = document.execCommand('copy');
      textArea.remove();
      return success;
    }
  } catch (error) {
    console.error('Failed to copy text:', error);
    return false;
  }
};

/**
 * Scrolls element to bottom smoothly
 * @param element - Element to scroll
 * @param behavior - Scroll behavior
 */
export const scrollToBottom = (
  element: HTMLElement,
  behavior: ScrollBehavior = 'smooth'
): void => {
  element.scrollTo({
    top: element.scrollHeight,
    behavior,
  });
};

/**
 * Checks if element is scrolled to bottom
 * @param element - Element to check
 * @param threshold - Threshold in pixels
 * @returns Boolean indicating if at bottom
 */
export const isScrolledToBottom = (
  element: HTMLElement,
  threshold: number = 10
): boolean => {
  return (
    element.scrollHeight - element.scrollTop - element.clientHeight <= threshold
  );
};
