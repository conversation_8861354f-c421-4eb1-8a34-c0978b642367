/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * 
 * @fileoverview Professional Food-Themed Chat Panel Component
 * @version 2.0.0
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>> * 
 */

'use client';

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Trash2, 
  MapPin, 
  Star, 
  Clock, 
  ChefHat,
  Utensils,
  Coffee,
  Heart,
  ExternalLink,
  Navigation
} from 'lucide-react';

// Internal imports
import { useUserStore } from '@/store/userStore';
import { useChatStore } from '@/store/chatStore';
import { IChatPanelProps, IChatMessage, IRestaurantCard } from '@/types/chat.types';
import { FOOD_COLORS, TYPOGRAPHY, ANIMATIONS, CHAT_CONFIG } from '@/constants/chat.constants';
import { 
  generateMessageId, 
  formatMessageTime, 
  validateMessageContent,
  formatPrice,
  generateDirectionsUrl,
  convertEmbedToMapsUrl,
  debounce,
  scrollToBottom,
  getRandomLoadingMessage
} from '@/utils/chat.utils';

/**
 * Professional Chat Panel Component with Food-Themed Design
 * 
 * Features:
 * - Enterprise-grade architecture
 * - Food-inspired visual design
 * - Smooth animations and micro-interactions
 * - Accessibility compliant
 * - Mobile-responsive
 * - Performance optimized
 */
const ChatPanel: React.FC<IChatPanelProps> = ({
  isOpen = true,
  onClose,
  isEmbedded = false,
  className = '',
  theme = 'food',
  config = {}
}) => {
  // ==================== STATE MANAGEMENT ====================
  const [inputMessage, setInputMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingMessage, setLoadingMessage] = useState<string>('');
  const [isTyping, setIsTyping] = useState<boolean>(false);
  
  // Store hooks
  const { getCurrentUserMessages, addMessage, clearCurrentUserChat, setCurrentUser } = useChatStore();
  const { currentUser } = useUserStore();
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  
  // ==================== COMPUTED VALUES ====================
  const chatMessages = useMemo(() => getCurrentUserMessages(), [getCurrentUserMessages]);
  
  const mergedConfig = useMemo(() => ({
    maxMessages: CHAT_CONFIG.MAX_MESSAGES,
    animations: true,
    autoScroll: true,
    ...config
  }), [config]);
  
  // ==================== EFFECTS ====================
  
  /**
   * Set current user when component mounts or user changes
   */
  useEffect(() => {
    if (currentUser?.id) {
      setCurrentUser(currentUser.id);
    }
  }, [currentUser?.id, setCurrentUser]);
  
  /**
   * Auto-scroll to bottom when new messages arrive
   */
  useEffect(() => {
    if (mergedConfig.autoScroll && messagesContainerRef.current) {
      const timer = setTimeout(() => {
        scrollToBottom(messagesContainerRef.current!);
      }, CHAT_CONFIG.AUTO_SCROLL_DELAY);
      
      return () => clearTimeout(timer);
    }
  }, [chatMessages, mergedConfig.autoScroll]);
  
  /**
   * Focus input when component mounts
   */
  useEffect(() => {
    if (inputRef.current && isOpen) {
      inputRef.current.focus();
    }
  }, [isOpen]);
  
  // ==================== EVENT HANDLERS ====================
  
  /**
   * Handles input change with debounced typing indicator
   */
  const handleInputChange = useCallback(
    debounce((value: string) => {
      setInputMessage(value);
      setIsTyping(value.length > 0);
    }, 150),
    []
  );
  
  /**
   * Handles form submission
   */
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    const trimmedMessage = inputMessage.trim();
    const validation = validateMessageContent(trimmedMessage);
    
    if (!validation.isValid || isLoading) {
      return;
    }
    
    // Add user message
    addMessage({
      type: 'user',
      content: trimmedMessage
    });
    
    // Clear input and set loading state
    setInputMessage('');
    setIsLoading(true);
    setLoadingMessage(getRandomLoadingMessage());
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000'}/api/v1/chat/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_query: trimmedMessage,
          location: 'Islamabad',
          budget: currentUser?.preferences?.budgetRange ?
            (currentUser.preferences.budgetRange[1] > 2000 ? 'high' :
             currentUser.preferences.budgetRange[1] > 1000 ? 'medium' : 'low') : 'medium',
          user_profile: currentUser ? {
            name: currentUser.name || 'User',
            food_personality: currentUser.foodPersonality || 'adventurous',
            favorite_cuisines: currentUser.preferences?.favoriteCuisines || ['Pakistani'],
            spice_level: currentUser.preferences?.spiceLevel || 'medium',
            dining_style: currentUser.preferences?.diningStyle || ['casual'],
            taste_profile: currentUser.tasteProfile || {},
            budget_range: currentUser.preferences?.budgetRange || [500, 2500]
          } : null,
          conversation_history: chatMessages.slice(-5).map((msg: IChatMessage) => ({
            role: msg.type === 'user' ? 'user' : 'assistant',
            content: msg.content
          }))
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      const aiText = result?.response || result?.answer || "I'm still learning to respond.";
      
      // Add AI response
      addMessage({
        type: 'ai',
        content: aiText,
        restaurants: result?.restaurants || []
      });
      
    } catch (error) {
      console.error('Chat error:', error);
      addMessage({
        type: 'ai',
        content: 'Sorry, I encountered an error. Please try again.'
      });
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
    }
  }, [inputMessage, isLoading, addMessage, currentUser, chatMessages]);
  
  /**
   * Handles clear chat action
   */
  const handleClearChat = useCallback(() => {
    clearCurrentUserChat();
  }, [clearCurrentUserChat]);
  
  // ==================== RENDER HELPERS ====================
  
  /**
   * Renders restaurant card with enhanced design
   */
  const renderRestaurantCard = useCallback((restaurant: IRestaurantCard, index: number) => (
    <motion.div
      key={`restaurant-${index}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.3 }}
      className="bg-white rounded-2xl shadow-lg border border-orange-100 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
    >
      {/* Restaurant Header */}
      <div className="bg-gradient-to-r from-orange-50 via-red-50 to-yellow-50 p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <ChefHat className="w-4 h-4 text-orange-600" />
              <h4 className="font-bold text-gray-900 text-lg">{restaurant.name}</h4>
            </div>
            <div className="flex items-center space-x-1 text-orange-600 text-sm">
              <MapPin className="w-3 h-3" />
              <span className="font-medium">{restaurant.area}</span>
            </div>
          </div>
          <div className="flex items-center space-x-1 bg-yellow-100 px-3 py-1 rounded-full">
            <Star className="w-4 h-4 text-yellow-500 fill-current" />
            <span className="text-sm font-bold text-gray-700">{restaurant.rating}</span>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-start space-x-2">
            <MapPin className="w-4 h-4 text-orange-500 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-gray-700 leading-relaxed">{restaurant.address}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Utensils className="w-4 h-4 text-green-600" />
            <p className="text-sm font-semibold text-gray-900">
              {restaurant.item_name}: 
              <span className="text-green-600 ml-1">{formatPrice(restaurant.price)}</span>
            </p>
          </div>
        </div>
      </div>

      {/* Compact Map */}
      <div className="h-24 w-full bg-gray-100">
        <iframe
          src={restaurant.map_embed_url}
          width="100%"
          height="100%"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          className="w-full h-full"
          title={`${restaurant.name} Location`}
        />
      </div>

      {/* Action Buttons */}
      <div className="p-3 bg-gray-50 flex space-x-2">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => window.open(convertEmbedToMapsUrl(restaurant.map_embed_url), '_blank')}
          className="flex-1 bg-gradient-to-r from-orange-500 to-red-500 text-white py-2 px-3 rounded-xl text-sm font-semibold hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2"
        >
          <ExternalLink className="w-4 h-4" />
          <span>View on Maps</span>
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => window.open(generateDirectionsUrl(restaurant.address), '_blank')}
          className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-2 px-3 rounded-xl text-sm font-semibold hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2"
        >
          <Navigation className="w-4 h-4" />
          <span>Directions</span>
        </motion.button>
      </div>
    </motion.div>
  ), []);
  
  /**
   * Renders message content with restaurant cards
   */
  const renderMessageContent = useCallback((message: IChatMessage) => {
    const text = typeof message === 'string' ? message : message.content;

    if (message.restaurants && message.restaurants.length > 0) {
      return (
        <div className="space-y-4">
          {/* AI Text Response */}
          <div className="text-gray-800 leading-relaxed">{text}</div>

          {/* Restaurant Cards */}
          <div className="space-y-4">
            {message.restaurants.map((restaurant, index) =>
              renderRestaurantCard(restaurant, index)
            )}
          </div>
        </div>
      );
    }

    return <div className="text-gray-800 leading-relaxed">{text}</div>;
  }, [renderRestaurantCard]);

  /**
   * Renders loading indicator
   */
  const renderLoadingIndicator = useCallback(() => (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="flex justify-start"
    >
      <div className="bg-white rounded-2xl p-4 shadow-sm border border-orange-100 max-w-xs">
        <div className="flex items-center space-x-3">
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.2,
                }}
                className="w-2 h-2 bg-orange-400 rounded-full"
              />
            ))}
          </div>
          <span className="text-sm text-gray-600">{loadingMessage}</span>
        </div>
      </div>
    </motion.div>
  ), [loadingMessage]);

  /**
   * Renders welcome message
   */
  const renderWelcomeMessage = useCallback(() => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center py-12"
    >
      <motion.div
        animate={{
          rotate: [0, 10, -10, 0],
          scale: [1, 1.1, 1]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          repeatDelay: 3
        }}
        className="w-20 h-20 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"
      >
        <Coffee className="w-10 h-10 text-white" />
      </motion.div>
      <h4 className="font-bold text-gray-900 mb-3 text-xl">Welcome to RotiShoti AI!</h4>
      <p className="text-gray-600 max-w-sm mx-auto leading-relaxed">
        Your personal food discovery assistant. Ask me about restaurants, cuisines, or any food-related questions!
      </p>
      <div className="flex justify-center space-x-2 mt-4">
        {['🍽️', '🥘', '🍕', '🍔', '🍜'].map((emoji, index) => (
          <motion.span
            key={index}
            animate={{
              y: [0, -5, 0],
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              delay: index * 0.2
            }}
            className="text-2xl"
          >
            {emoji}
          </motion.span>
        ))}
      </div>
    </motion.div>
  ), []);

  // ==================== MAIN RENDER ====================

  if (!isOpen && !isEmbedded) return null;

  return (
    <div className={`h-full flex flex-col bg-gradient-to-br from-orange-50 via-white to-red-50 ${className}`}>
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-orange-500 via-red-500 to-yellow-500 text-white p-4 shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"
            >
              <ChefHat className="w-6 h-6 text-white" />
            </motion.div>
            <div>
              <h3 className="font-bold text-lg">RotiShoti AI</h3>
              <p className="text-xs opacity-90 flex items-center space-x-1">
                <Heart className="w-3 h-3 fill-current" />
                <span>Your food discovery companion</span>
              </p>
            </div>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleClearChat}
            className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-full transition-all duration-300 flex items-center space-x-2 text-sm font-medium backdrop-blur-sm"
          >
            <Trash2 className="w-4 h-4" />
            <span>Clear Chat</span>
          </motion.button>
        </div>
      </div>

      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 p-4 overflow-y-auto space-y-4 scrollbar-thin scrollbar-thumb-orange-300 scrollbar-track-orange-100"
      >
        <AnimatePresence>
          {chatMessages.length === 0 ? (
            renderWelcomeMessage()
          ) : (
            chatMessages.map((message, index) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.05 }}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-[85%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                  {/* Message Bubble */}
                  <div
                    className={`p-4 rounded-2xl shadow-sm ${
                      message.type === 'user'
                        ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white ml-4'
                        : 'bg-white border border-orange-100 mr-4'
                    }`}
                  >
                    <div className="text-sm leading-relaxed">
                      {message.type === 'user' ? message.content : renderMessageContent(message)}
                    </div>
                  </div>

                  {/* Timestamp */}
                  <div className={`text-xs text-gray-500 mt-1 px-2 ${
                    message.type === 'user' ? 'text-right' : 'text-left'
                  }`}>
                    <Clock className="w-3 h-3 inline mr-1" />
                    {formatMessageTime(message.timestamp)}
                  </div>
                </div>
              </motion.div>
            ))
          )}

          {/* Loading Indicator */}
          {isLoading && renderLoadingIndicator()}
        </AnimatePresence>

        <div ref={messagesEndRef} />
      </div>

      {/* Enhanced Input Form */}
      <div className="p-4 bg-white border-t border-orange-100 shadow-lg">
        <form onSubmit={handleSubmit} className="flex space-x-3">
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={inputMessage}
              onChange={(e) => handleInputChange(e.target.value)}
              placeholder="Ask about restaurants, cuisines, or food recommendations..."
              className="w-full px-4 py-3 border-2 border-orange-200 rounded-2xl focus:outline-none focus:border-orange-400 focus:ring-4 focus:ring-orange-100 transition-all duration-300 text-gray-800 placeholder-gray-500"
              disabled={isLoading}
              maxLength={CHAT_CONFIG.MAX_MESSAGE_LENGTH}
            />
            {isTyping && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="absolute right-3 top-1/2 transform -translate-y-1/2"
              >
                <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" />
              </motion.div>
            )}
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            type="submit"
            disabled={isLoading || !inputMessage.trim()}
            className="px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-2xl hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 font-semibold"
          >
            {isLoading ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Coffee className="w-5 h-5" />
              </motion.div>
            ) : (
              <Send className="w-5 h-5" />
            )}
            <span className="hidden sm:inline">
              {isLoading ? 'Cooking...' : 'Send'}
            </span>
          </motion.button>
        </form>
      </div>
    </div>
  );
};

export default ChatPanel;
