/* RotiShoti Design System - Professional Foodie Theme */

:root {
  /* Primary Colors - Warm & Appetizing */
  --primary-50: #fef7ed;
  --primary-100: #fdedd3;
  --primary-200: #fbd7a5;
  --primary-300: #f8bc6d;
  --primary-400: #f59e42;
  --primary-500: #f97316; /* Main Orange */
  --primary-600: #ea580c;
  --primary-700: #c2410c;
  --primary-800: #9a3412;
  --primary-900: #7c2d12;

  /* Secondary Colors - Earthy & Natural */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  /* Accent Colors - Fresh & Vibrant */
  --accent-green: #10b981;
  --accent-green-light: #d1fae5;
  --accent-red: #ef4444;
  --accent-red-light: #fee2e2;
  --accent-yellow: #f59e0b;
  --accent-yellow-light: #fef3c7;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  --gradient-warm: linear-gradient(135deg, #fef7ed 0%, #fed7aa 50%, #fdba74 100%);
  --gradient-hero: linear-gradient(135deg, #fef7ed 0%, #f3f4f6 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-warm: 0 10px 25px -5px rgb(249 115 22 / 0.1), 0 8px 10px -6px rgb(249 115 22 / 0.1);

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-display: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  color: var(--gray-800);
  background-color: var(--gray-50);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Classes */
.text-display-xl {
  font-family: var(--font-display);
  font-size: 3.75rem;
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.025em;
}

.text-display-lg {
  font-family: var(--font-display);
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.text-display-md {
  font-family: var(--font-display);
  font-size: 2.25rem;
  font-weight: 600;
  line-height: 1.3;
}

.text-display-sm {
  font-family: var(--font-display);
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
}

.text-body-lg {
  font-size: 1.125rem;
  line-height: 1.7;
}

.text-body {
  font-size: 1rem;
  line-height: 1.6;
}

.text-body-sm {
  font-size: 0.875rem;
  line-height: 1.5;
}

.text-caption {
  font-size: 0.75rem;
  line-height: 1.4;
  font-weight: 500;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.5;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  white-space: nowrap;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--white);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.btn-ghost {
  background: transparent;
  color: var(--gray-600);
  border: 1px solid transparent;
}

.btn-ghost:hover {
  background: var(--gray-100);
  color: var(--gray-800);
}

.btn-lg {
  padding: var(--space-md) var(--space-xl);
  font-size: 1rem;
  border-radius: var(--radius-xl);
}

.btn-sm {
  padding: var(--space-xs) var(--space-md);
  font-size: 0.75rem;
  border-radius: var(--radius-md);
}

/* Card Styles */
.card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-warm {
  background: var(--gradient-warm);
  border: 1px solid var(--primary-200);
  box-shadow: var(--shadow-warm);
}

/* Utility Classes */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-center {
  text-align: center;
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Hover Effects */
.hover-lift {
  transition: transform var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Focus States */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
  transition: all var(--transition-fast);
}

.focus-ring:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-color: var(--primary-500);
}

/* Chat-specific Styles */
.chat-message-enter {
  animation: slideInUp 0.3s ease-out;
}

.chat-typing {
  animation: typing 1.5s infinite;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Scrollbar Styling */
.chat-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-full);
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-full);
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Responsive Design */
@media (max-width: 768px) {
  .text-display-xl {
    font-size: 2.5rem;
  }

  .text-display-lg {
    font-size: 2rem;
  }

  .container {
    padding: 0 var(--space-sm);
  }

  .btn {
    padding: var(--space-sm) var(--space-md);
    font-size: 0.8rem;
  }

  .btn-lg {
    padding: var(--space-md) var(--space-lg);
    font-size: 0.9rem;
  }
}

/* Chat Modal Styles */
.chat-modal-backdrop {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.chat-modal-enter {
  animation: chatModalEnter 0.3s ease-out;
}

.chat-modal-exit {
  animation: chatModalExit 0.2s ease-in;
}

@keyframes chatModalEnter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes chatModalExit {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
}

/* Mobile Chat Modal */
@media (max-width: 768px) {
  .chat-modal-mobile {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
  }

  .chat-modal-mobile.open {
    transform: translateY(0);
  }
}

/* Floating Chat Button Styles */
.chat-button-pulse {
  animation: chatButtonPulse 2s infinite;
}

@keyframes chatButtonPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 0, 0.7);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(255, 107, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 0, 0);
  }
}

/* Chat Message Animations */
.chat-message-slide-in {
  animation: chatMessageSlideIn 0.4s ease-out;
}

@keyframes chatMessageSlideIn {
  from {
    opacity: 0;
    transform: translateY(15px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Suggestion Chips */
.suggestion-chip {
  transition: all 0.2s ease;
  transform: translateY(0);
}

.suggestion-chip:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 0, 0.2);
}
