'use client';

import { useState, useEffect, useCallback } from 'react';
// import { DynamicMap } from '../maps/DynamicMap';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  is_vegetarian?: boolean;
  spice_level?: string;
}

interface Branch {
  branch_id: string;
  name: string;
  area: string;
  address: string;
  phone?: string;
  latitude: number;
  longitude: number;
  is_main_branch: boolean;
  features: string[];
}

interface Restaurant {
  id: string;
  restaurant_id: string;
  name: string;
  city: string;
  image_url?: string;
  cuisine_types: string[];
  area: string;
  address: string;
  phone?: string;
  latitude: number;
  longitude: number;
  price_range_min: number;
  price_range_max: number;
  price_range_average: number;
  rating_overall: number;
  rating_food?: number;
  rating_service?: number;
  rating_value?: number;
  ambiance_type: string;
  noise_level?: string;
  features: string[];
  branches?: Branch[];
  menu_items?: MenuItem[];
}

interface RestaurantProfileProps {
  restaurant: Restaurant;
  onClose: () => void;
  demoMenuItems?: MenuItem[];
  demoBranches?: Branch[];
}

export const RestaurantProfile: React.FC<RestaurantProfileProps> = ({
  restaurant,
  onClose,
  demoMenuItems,
  demoBranches
}) => {
  const [activeTab, setActiveTab] = useState<'menu' | 'info' | 'location'>('menu');
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [isVisible, setIsVisible] = useState(false);

  // Stable close handler
  const handleClose = useCallback((e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    setIsVisible(false);
    setTimeout(() => onClose(), 150); // Small delay for smooth animation
  }, [onClose]);

  // Show modal after mount with slight delay to prevent flickering
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Prevent body scroll when modal is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  // Fetch restaurant details
  useEffect(() => {
    const fetchRestaurantDetails = async () => {
      try {
        setLoading(true);

        // Use demo data if provided, otherwise fetch from API
        if (demoMenuItems && demoBranches) {
          setMenuItems(demoMenuItems);
          setBranches(demoBranches);
          setLoading(false);
          return;
        }

        // Fetch menu items
        const menuResponse = await fetch(`http://localhost:8000/api/v1/restaurants/${restaurant.id}/menu`);
        if (menuResponse.ok) {
          const menuData = await menuResponse.json();
          setMenuItems(menuData);
        } else {
          // Fallback demo menu
          setMenuItems([
            { id: '1', name: 'Sample Item', description: 'Demo menu item', price: 500, category: 'Main Course' }
          ]);
        }

        // Fetch branches
        const branchResponse = await fetch(`http://localhost:8000/api/v1/restaurants/${restaurant.id}/branches`);
        if (branchResponse.ok) {
          const branchData = await branchResponse.json();
          setBranches(branchData);
        } else {
          // Fallback demo branch
          setBranches([
            {
              branch_id: '1',
              name: restaurant.name,
              area: restaurant.area,
              address: restaurant.address,
              latitude: restaurant.latitude,
              longitude: restaurant.longitude,
              is_main_branch: true,
              features: restaurant.features || []
            }
          ]);
        }

      } catch (error) {
        console.error('Error fetching restaurant details:', error);
        // Set fallback data
        setMenuItems([
          { id: '1', name: 'Sample Item', description: 'Demo menu item', price: 500, category: 'Main Course' }
        ]);
        setBranches([
          {
            branch_id: '1',
            name: restaurant.name,
            area: restaurant.area,
            address: restaurant.address,
            latitude: restaurant.latitude,
            longitude: restaurant.longitude,
            is_main_branch: true,
            features: restaurant.features || []
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchRestaurantDetails();
  }, [restaurant.id, demoMenuItems, demoBranches]);

  // Group menu items by category
  const groupedMenuItems = menuItems.reduce((acc, item) => {
    const category = item.category || 'Other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(item);
    return acc;
  }, {} as Record<string, MenuItem[]>);

  const mainBranch = branches.find(b => b.is_main_branch) || branches[0];

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-[9999] flex items-center justify-center p-4"
      onClick={handleClose}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999
      }}
    >
      <div
        className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
        onClick={(e) => e.stopPropagation()}
        style={{
          minHeight: '500px',
          maxHeight: '90vh',
          position: 'relative'
        }}
      >
        {/* Header */}
        <div className="relative">
          {restaurant.image_url && (
            <img 
              src={restaurant.image_url} 
              alt={restaurant.name}
              className="w-full h-48 object-cover"
            />
          )}
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 z-20 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 transition-all shadow-lg"
            style={{
              position: 'absolute',
              top: '16px',
              right: '16px',
              zIndex: 20
            }}
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
            <h1 className="text-3xl font-bold text-white mb-2">{restaurant.name}</h1>
            <div className="flex items-center text-white text-sm">
              <span className="mr-4">📍 {restaurant.area}</span>
              <span className="mr-4">⭐ {restaurant.rating_overall}/5</span>
              <span>💰 Rs {restaurant.price_range_min} - Rs {restaurant.price_range_max}</span>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('menu')}
            className={`flex-1 py-3 px-4 text-center font-medium transition-colors ${
              activeTab === 'menu' 
                ? 'border-b-2 border-red-500 text-red-500' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            📋 Menu
          </button>
          <button
            onClick={() => setActiveTab('info')}
            className={`flex-1 py-3 px-4 text-center font-medium transition-colors ${
              activeTab === 'info' 
                ? 'border-b-2 border-red-500 text-red-500' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            ℹ️ Info
          </button>
          <button
            onClick={() => setActiveTab('location')}
            className={`flex-1 py-3 px-4 text-center font-medium transition-colors ${
              activeTab === 'location' 
                ? 'border-b-2 border-red-500 text-red-500' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            🗺️ Location
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading restaurant details...</p>
              </div>
            </div>
          ) : (
            <>
              {/* Menu Tab */}
              {activeTab === 'menu' && (
                <div>
                  {Object.keys(groupedMenuItems).length === 0 ? (
                    <p className="text-center text-gray-600 py-8">No menu items available</p>
                  ) : (
                    Object.entries(groupedMenuItems).map(([category, items]) => (
                      <div key={category} className="mb-8">
                        <h3 className="text-xl font-bold text-gray-800 mb-4 border-b pb-2">
                          {category}
                        </h3>
                        <div className="grid gap-4">
                          {items.map((item) => (
                            <div key={item.id} className="flex justify-between items-start p-4 bg-gray-50 rounded-lg">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="font-semibold text-gray-800">{item.name}</h4>
                                  {item.is_vegetarian && <span className="text-green-600 text-sm">🌱</span>}
                                  {item.spice_level && (
                                    <span className="text-red-500 text-sm">
                                      {'🌶️'.repeat(item.spice_level === 'mild' ? 1 : item.spice_level === 'medium' ? 2 : 3)}
                                    </span>
                                  )}
                                </div>
                                {item.description && (
                                  <p className="text-gray-600 text-sm">{item.description}</p>
                                )}
                              </div>
                              <div className="text-right ml-4">
                                <span className="text-lg font-bold text-red-600">Rs {item.price}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}

              {/* Info Tab */}
              {activeTab === 'info' && (
                <div className="space-y-6">
                  {/* Basic Info */}
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-4">Restaurant Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-gray-700 mb-2">Cuisine Types</h4>
                        <div className="flex flex-wrap gap-2">
                          {restaurant.cuisine_types.map((cuisine, index) => (
                            <span key={index} className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm">
                              {cuisine}
                            </span>
                          ))}
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-gray-700 mb-2">Price Range</h4>
                        <p className="text-gray-600">Rs {restaurant.price_range_min} - Rs {restaurant.price_range_max}</p>
                        <p className="text-sm text-gray-500">Average: Rs {restaurant.price_range_average}</p>
                      </div>

                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-gray-700 mb-2">Ratings</h4>
                        <div className="space-y-1">
                          <div className="flex justify-between">
                            <span>Overall:</span>
                            <span className="font-semibold">⭐ {restaurant.rating_overall}/5</span>
                          </div>
                          {restaurant.rating_food && (
                            <div className="flex justify-between">
                              <span>Food:</span>
                              <span>⭐ {restaurant.rating_food}/5</span>
                            </div>
                          )}
                          {restaurant.rating_service && (
                            <div className="flex justify-between">
                              <span>Service:</span>
                              <span>⭐ {restaurant.rating_service}/5</span>
                            </div>
                          )}
                          {restaurant.rating_value && (
                            <div className="flex justify-between">
                              <span>Value:</span>
                              <span>⭐ {restaurant.rating_value}/5</span>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-gray-700 mb-2">Ambiance</h4>
                        <p className="text-gray-600">{restaurant.ambiance_type}</p>
                        {restaurant.noise_level && (
                          <p className="text-sm text-gray-500">Noise Level: {restaurant.noise_level}</p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Features */}
                  {restaurant.features.length > 0 && (
                    <div>
                      <h3 className="text-xl font-bold text-gray-800 mb-4">Features</h3>
                      <div className="flex flex-wrap gap-2">
                        {restaurant.features.map((feature, index) => (
                          <span key={index} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Branches */}
                  {branches.length > 0 && (
                    <div>
                      <h3 className="text-xl font-bold text-gray-800 mb-4">All Branches</h3>
                      <div className="space-y-3">
                        {branches.map((branch) => (
                          <div key={branch.branch_id} className="bg-gray-50 p-4 rounded-lg">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-semibold text-gray-800">
                                  {branch.name}
                                  {branch.is_main_branch && (
                                    <span className="ml-2 bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
                                      Main Branch
                                    </span>
                                  )}
                                </h4>
                                <p className="text-gray-600">📍 {branch.area}</p>
                                <p className="text-sm text-gray-500">{branch.address}</p>
                                {branch.phone && (
                                  <p className="text-sm text-gray-500">📞 {branch.phone}</p>
                                )}
                                {branch.features.length > 0 && (
                                  <div className="mt-2 flex flex-wrap gap-1">
                                    {branch.features.map((feature, index) => (
                                      <span key={index} className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                                        {feature}
                                      </span>
                                    ))}
                                  </div>
                                )}
                              </div>
                              <button
                                onClick={() => {
                                  const url = `https://www.openstreetmap.org/search?query=${branch.latitude},${branch.longitude}`;
                                  window.open(url, '_blank');
                                }}
                                className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors"
                              >
                                Directions
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Location Tab */}
              {activeTab === 'location' && (
                <div>
                  <h3 className="text-xl font-bold text-gray-800 mb-4">Restaurant Locations</h3>
                  {/* Simple Map Placeholder - Will be enhanced later */}
                  <div style={{
                    height: '400px',
                    background: '#f5f5f5',
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '2px dashed #ddd'
                  }}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '48px', marginBottom: '16px' }}>🗺️</div>
                      <h3 style={{ margin: '0 0 8px 0', color: '#333' }}>Interactive Map</h3>
                      <p style={{ margin: '0 0 16px 0', color: '#666' }}>
                        {restaurant.name} - {restaurant.area}
                      </p>
                      <button
                        onClick={() => {
                          const query = encodeURIComponent(`${restaurant.name} ${restaurant.area} ${restaurant.address}`);
                          window.open(`https://www.openstreetmap.org/search?query=${query}`, '_blank');
                        }}
                        style={{
                          background: '#4285f4',
                          color: 'white',
                          border: 'none',
                          padding: '12px 24px',
                          borderRadius: '8px',
                          cursor: 'pointer',
                          fontSize: '16px'
                        }}
                      >
                        🗺️ Open in Maps
                      </button>
                    </div>
                  </div>
                  
                  {mainBranch && (
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-semibold text-gray-800 mb-2">Main Branch Details</h4>
                      <p className="text-gray-600">📍 {mainBranch.address}</p>
                      {mainBranch.phone && (
                        <p className="text-gray-600">📞 {mainBranch.phone}</p>
                      )}
                      <div className="mt-2 flex gap-2">
                        <button
                          onClick={() => {
                            const url = `https://www.openstreetmap.org/search?query=${mainBranch.latitude},${mainBranch.longitude}`;
                            window.open(url, '_blank');
                          }}
                          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                        >
                          🗺️ Open in Maps
                        </button>
                        <button
                          onClick={() => {
                            navigator.clipboard.writeText(mainBranch.address);
                            alert('Address copied to clipboard!');
                          }}
                          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors"
                        >
                          📋 Copy Address
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
