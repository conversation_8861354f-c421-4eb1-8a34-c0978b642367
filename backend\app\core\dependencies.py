from fastapi import HTT<PERSON><PERSON>x<PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from jose import jwt, JWTError
from app.core.config import settings
from app.db.supabase import create_supabase_client
from app.core.enums import UserRole;

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")

def get_current_user(token: str = Depends(oauth2_scheme)) -> dict:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id: str = payload.get("sub")
        email: str = payload.get("email")
        name: str = payload.get("name")
        role: str = payload.get("role")

        if not user_id or not email:
            raise credentials_exception

        return {
            "id": user_id,
            "email": email,
            "name": name,
            "role": role
        }
    except JWTError:
        raise credentials_exception
    
def require_authenticated_user(current_user: dict = Depends(get_current_user)):
    return current_user

def require_role(required_role: str):
    def role_guard(current_user = Depends(get_current_user)):
        if current_user["role"] != required_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    return role_guard

def require_authenticated_admin(current_user: dict = Depends(get_current_user)):
    if current_user["role"] != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access only",
        )
    return current_user

def get_supabase_client():
    return create_supabase_client()