"""
RotiShoti - AI-Powered Food Discovery Platform
Author: <PERSON><PERSON><PERSON> Faroo<PERSON>
Main FastAPI application entry point
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.api.api import api_router

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    openapi_url=f"/openapi.json",
    description="AI-powered food discovery platform for Pakistani cuisine",
    contact={
        "name": "Masab Farooque",
        "url": "https://github.com/Masab12/RotiShoti",
    },
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix="/api")

@app.get("/", tags=["healthcheck"])
async def root():
    return {
        "message": "RotiShoti API is running!",
        "author": "Masab Farooque",
        "version": settings.PROJECT_VERSION
    }