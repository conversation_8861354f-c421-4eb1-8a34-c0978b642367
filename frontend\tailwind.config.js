/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // "Spices & Cream" Color Palette
      colors: {
        // Primary Background - Warm creamy off-white
        cream: '#FFF8F0',
        // Main Text - Rich dark brown/charcoal
        chocolate: '#3D2C21',
        // Primary Accent - Vibrant saffron/paprika orange
        saffron: '#FF6B00',
        paprika: '#E67E22',
        // Secondary Accents
        coriander: '#27AE60', // Fresh green for success/verified
        chili: '#C0392B', // Hot red for trending/deals
        // Additional food-inspired colors
        turmeric: '#F39C12',
        cardamom: '#8E44AD',
        cinnamon: '#D35400',
      },
      fontFamily: {
        // Friendly headings
        'heading': ['Poppins', 'sans-serif'],
        // Clean body text
        'body': ['Inter', 'sans-serif'],
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'spice-gradient': 'linear-gradient(135deg, #FF6B00 0%, #E67E22 50%, #D35400 100%)',
        'cream-gradient': 'linear-gradient(135deg, #FFF8F0 0%, #FDF2E9 100%)',
      },
      animation: {
        'bounce-gentle': 'bounce 2s infinite',
        'pulse-slow': 'pulse 3s infinite',
        'wiggle': 'wiggle 1s ease-in-out infinite',
        'slide-in': 'slideIn 0.5s ease-out',
        'fade-in': 'fadeIn 0.3s ease-in',
      },
      keyframes: {
        wiggle: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' },
        },
        slideIn: {
          '0%': { transform: 'translateX(100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        }
      }
    },
  },
  plugins: [],
}
