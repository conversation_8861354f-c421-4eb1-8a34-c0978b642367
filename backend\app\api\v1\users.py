from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any
from app.schemas.user_schemas import UserPreferencesUpdate, UserActivityLog
from app.services.user_service import user_service
from app.core.dependencies import get_current_user

router = APIRouter()

@router.get("/users/me/history", response_model=List[Dict[str, Any]])
async def get_user_history(current_user: dict = Depends(get_current_user)):
    return await user_service.get_user_history(current_user["id"])

@router.post("/users/me/like", status_code=status.HTTP_204_NO_CONTENT)
async def like_restaurant(data: Dict[str, str], current_user: dict = Depends(get_current_user)):
    restaurant_id = data.get("restaurant_id")
    if not restaurant_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="restaurant_id is required")
    await user_service.like_restaurant(current_user["id"], restaurant_id)
    return {"message": "Restaurant liked"}

@router.post("/users/me/rate", status_code=status.HTTP_204_NO_CONTENT)
async def rate_restaurant(data: Dict[str, Any], current_user: dict = Depends(get_current_user)):
    restaurant_id = data.get("restaurant_id")
    rating = data.get("rating")
    feedback = data.get("feedback")
    if not restaurant_id or rating is None:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="restaurant_id and rating are required")
    await user_service.rate_restaurant(current_user["id"], restaurant_id, rating, feedback)
    return {"message": "Rating submitted"}

@router.put("/users/me/preferences", status_code=status.HTTP_204_NO_CONTENT)
async def update_user_preferences(preferences: UserPreferencesUpdate, current_user: dict = Depends(get_current_user)):
    await user_service.update_user_preferences(current_user["id"], preferences)
    return {"message": "Preferences updated"}