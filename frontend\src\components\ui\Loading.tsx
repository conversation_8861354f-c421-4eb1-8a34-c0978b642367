/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Authors: <AUTHORS>
 * Component: Professional loading components with beautiful animations
 */

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { LOADING_ANIMATIONS } from '@/lib/animations';

export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars' | 'pakistani';
  color?: 'primary' | 'secondary' | 'white' | 'gray';
  text?: string;
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  variant = 'spinner',
  color = 'primary',
  text,
  className,
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const colorClasses = {
    primary: 'text-green-500',
    secondary: 'text-orange-500',
    white: 'text-white',
    gray: 'text-gray-500',
  };

  const containerClass = cn(
    'flex flex-col items-center justify-center space-y-2',
    className
  );

  const iconClass = cn(sizeClasses[size], colorClasses[color]);

  const renderSpinner = () => (
    <motion.div
      className={cn('border-2 border-current border-t-transparent rounded-full', iconClass)}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    />
  );

  const renderDots = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className={cn('w-2 h-2 bg-current rounded-full', colorClasses[color])}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: i * 0.2,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );

  const renderPulse = () => (
    <motion.div
      className={cn('bg-current rounded-full', iconClass)}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.5, 1, 0.5],
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    />
  );

  const renderBars = () => (
    <div className="flex space-x-1 items-end">
      {[0, 1, 2, 3].map((i) => (
        <motion.div
          key={i}
          className={cn('w-1 bg-current rounded-full', colorClasses[color])}
          style={{ height: `${12 + i * 4}px` }}
          animate={{
            scaleY: [1, 2, 1],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.1,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );

  const renderPakistani = () => (
    <div className="relative">
      {/* Crescent and Star */}
      <motion.div
        className="relative w-8 h-8"
        animate={{
          rotate: [0, 360],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'linear',
        }}
      >
        {/* Crescent */}
        <div className="absolute inset-0 border-2 border-green-500 rounded-full border-r-transparent transform rotate-45" />
        
        {/* Star */}
        <motion.div
          className="absolute top-1 right-1 w-2 h-2"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          <div className="w-full h-full bg-white transform rotate-45" style={{
            clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)'
          }} />
        </motion.div>
      </motion.div>
      
      {/* Glow effect */}
      <motion.div
        className="absolute inset-0 rounded-full bg-green-500/20"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
    </div>
  );

  const renderVariant = () => {
    switch (variant) {
      case 'spinner':
        return renderSpinner();
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      case 'bars':
        return renderBars();
      case 'pakistani':
        return renderPakistani();
      default:
        return renderSpinner();
    }
  };

  return (
    <div className={containerClass}>
      {renderVariant()}
      {text && (
        <motion.p
          className={cn('text-sm font-medium', colorClasses[color])}
          animate={{
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );
};

// Specialized loading components
export const SpinnerLoading: React.FC<Omit<LoadingProps, 'variant'>> = (props) => (
  <Loading variant="spinner" {...props} />
);

export const DotsLoading: React.FC<Omit<LoadingProps, 'variant'>> = (props) => (
  <Loading variant="dots" {...props} />
);

export const PulseLoading: React.FC<Omit<LoadingProps, 'variant'>> = (props) => (
  <Loading variant="pulse" {...props} />
);

export const BarsLoading: React.FC<Omit<LoadingProps, 'variant'>> = (props) => (
  <Loading variant="bars" {...props} />
);

export const PakistaniLoading: React.FC<Omit<LoadingProps, 'variant'>> = (props) => (
  <Loading variant="pakistani" {...props} />
);

// Full screen loading overlay
export interface LoadingOverlayProps extends LoadingProps {
  visible: boolean;
  backdrop?: boolean;
  message?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  visible,
  backdrop = true,
  message = 'Loading...',
  ...loadingProps
}) => {
  if (!visible) return null;

  return (
    <motion.div
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center',
        backdrop && 'bg-black/50 backdrop-blur-sm'
      )}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        className="bg-white rounded-lg p-6 shadow-xl"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        <Loading text={message} {...loadingProps} />
      </motion.div>
    </motion.div>
  );
};

// Skeleton loading for content
export interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  rounded?: boolean;
  animated?: boolean;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className,
  width,
  height,
  rounded = false,
  animated = true,
}) => {
  return (
    <div
      className={cn(
        'bg-gray-200',
        rounded ? 'rounded-full' : 'rounded',
        animated && 'animate-pulse',
        className
      )}
      style={{ width, height }}
    />
  );
};

// Loading states for specific components
export const ChatMessageSkeleton: React.FC = () => (
  <div className="space-y-2">
    <Skeleton width="60%" height="1rem" />
    <Skeleton width="80%" height="1rem" />
    <Skeleton width="40%" height="1rem" />
  </div>
);

export const RestaurantCardSkeleton: React.FC = () => (
  <div className="space-y-3">
    <Skeleton height="12rem" />
    <div className="space-y-2">
      <Skeleton width="70%" height="1.25rem" />
      <Skeleton width="50%" height="1rem" />
      <div className="flex justify-between">
        <Skeleton width="30%" height="1rem" />
        <Skeleton width="25%" height="1rem" />
      </div>
    </div>
  </div>
);

export default Loading;
