# RotiShoti - AI-Powered Food Discovery Platform
# Author: <PERSON><PERSON><PERSON> Farooque
# Repository: https://github.com/Masab12/RotiShoti

# ================================
# SENSITIVE DATA & SECRETS
# ================================
.env
.env.*
.env.local
.env.development.local
.env.test.local
.env.production.local
*.env
config/secrets.py
config/keys.py
*_key.py
*_secret.py
credentials.json
service-account.json
supabase_key.txt
groq_api_key.txt

# ================================
# PYTHON
# ================================
__pycache__/
*.py[cod]
*$py.class
*.so
*.egg
*.egg-info/
dist/
build/
.eggs/
*.pyo
*.pyd
*.pdb
*.sqlite3
*.db
*.log
*.pot
*.mo
.Python
develop-eggs/
downloads/
eggs/
backend/lib/
backend/lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
.installed.cfg
MANIFEST
pip-log.txt
pip-delete-this-directory.txt
.tox/
.nox/
.coverage
.pytest_cache/
cover/
*.cover
*.py,cover
.hypothesis/
nosetests.xml
coverage.xml
.venv/
venv/
env/
ENV/
env.bak/
venv.bak/
backend/venv/
backend/env/

# ================================
# NODE.JS & FRONTEND
# ================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
.npm
.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/
.nyc_output
.grunt
bower_components
.lock-wscript
.node_repl_history
*.tgz
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Next.js
.next/
out/
.vercel
.turbo

# React
build/
dist/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Package lock files (optional - uncomment if needed)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# ================================
# IDE & EDITORS
# ================================
.vscode/
.idea/
*.iml
*.ipr
*.iws
*.swp
*.swo
*~
*.sublime-project
*.sublime-workspace

# ================================
# OPERATING SYSTEM
# ================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ================================
# TESTING & COVERAGE
# ================================
coverage/
*.cover
*.coverage
*.cov
.pytest_cache/
.mypy_cache/
test-results/
junit.xml
.hypothesis/
nosetests.xml
coverage.xml

# ================================
# JUPYTER NOTEBOOK
# ================================
.ipynb_checkpoints

# ================================
# DATABASE & DATA
# ================================
backend/app/data/
*.sqlite
*.sqlite3
database.db
db.sqlite3

# ================================
# SUPABASE
# ================================
.supabase/

# ================================
# LOGS & TEMPORARY FILES
# ================================
logs/
*.log
*.bak
*.swp
*.tmp
*.temp
*.backup
*.old
tmp/
temp/

# ================================
# AI/ML MODELS (if any)
# ================================
*.pkl
*.model
*.h5
*.pb

# ================================
# DOCKER
# ================================
.dockerignore
docker-compose.override.yml

# ================================
# MISC
# ================================
.local/
local/
secrets/
private/
confidential/