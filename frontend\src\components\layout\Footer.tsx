'use client';

import React from 'react';
import Link from 'next/link';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 border-t border-orange-200 mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            {/* Brand Section */}
            <div className="md:col-span-2">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-16 h-16 rounded-3xl bg-gradient-to-br from-red-500 to-orange-500 flex items-center justify-center shadow-2xl">
                  <span className="text-white font-bold text-3xl">🍽️</span>
                </div>
                <div>
                  <h3 className="text-3xl font-bold bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
                    RotiShoti
                  </h3>
                  <p className="text-orange-600 font-medium">Your AI Food Companion</p>
                </div>
              </div>
              <p className="text-gray-700 text-lg leading-relaxed max-w-md mb-8">
                Discover the best restaurants in Islamabad with AI-powered recommendations.
                From traditional Pakistani cuisine to international flavors, we help you find your perfect meal.
              </p>
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-3 bg-white rounded-2xl px-4 py-3 shadow-lg">
                  <svg className="w-6 h-6 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-gray-800 font-semibold">Islamabad, Pakistan</span>
                  <span className="text-2xl">🇵🇰</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-xl font-bold text-gray-900 mb-6">Quick Links</h4>
              <ul className="space-y-4">
                <li>
                  <Link href="/" className="text-gray-600 hover:text-red-600 transition-colors duration-200 flex items-center gap-2">
                    <span>🏠</span> Home
                  </Link>
                </li>
                <li>
                  <Link href="/restaurants" className="text-gray-600 hover:text-red-600 transition-colors duration-200 flex items-center gap-2">
                    <span>🍽️</span> Restaurants
                  </Link>
                </li>
                <li>
                  <Link href="/chat" className="text-gray-600 hover:text-red-600 transition-colors duration-200 flex items-center gap-2">
                    <span>🤖</span> AI Chat
                  </Link>
                </li>
                <li>
                  <Link href="/profile" className="text-gray-600 hover:text-red-600 transition-colors duration-200 flex items-center gap-2">
                    <span>👤</span> Profile
                  </Link>
                </li>
              </ul>
            </div>

            {/* Food Categories */}
            <div>
              <h4 className="text-xl font-bold text-gray-900 mb-6">Popular Cuisines</h4>
              <ul className="space-y-4">
                <li>
                  <span className="text-gray-600 flex items-center gap-2">
                    <span>🍛</span> Pakistani Desi
                  </span>
                </li>
                <li>
                  <span className="text-gray-600 flex items-center gap-2">
                    <span>🍕</span> Fast Food
                  </span>
                </li>
                <li>
                  <span className="text-gray-600 flex items-center gap-2">
                    <span>🍜</span> Chinese
                  </span>
                </li>
                <li>
                  <span className="text-gray-600 flex items-center gap-2">
                    <span>🥘</span> Continental
                  </span>
                </li>
              </ul>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-orange-200 mt-12 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-6">
              <div className="flex items-center gap-8">
                <p className="text-gray-600">
                  &copy; {new Date().getFullYear()} RotiShoti. All rights reserved.
                </p>
                <div className="flex items-center gap-6 text-sm text-gray-500">
                  <Link href="/privacy" className="hover:text-red-600 transition-colors">Privacy Policy</Link>
                  <Link href="/terms" className="hover:text-red-600 transition-colors">Terms of Service</Link>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <span className="text-gray-600 font-medium">Made with ❤️ in Pakistan</span>
                <div className="flex items-center gap-2">
                  <span className="text-2xl">🇵🇰</span>
                  <span className="text-gray-600">Islamabad</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
