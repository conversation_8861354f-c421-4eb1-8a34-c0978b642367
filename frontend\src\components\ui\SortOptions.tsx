
import React from 'react';

interface SortOptionProps {
  label: string;
  value: string;
  isActive?: boolean;
  onClick?: (value: string) => void;
}

const SortOption: React.FC<SortOptionProps> = ({ label, value, isActive = false, onClick }) => {
  const activeClasses = isActive ? 'text-orange-600 font-semibold' : 'text-gray-600 hover:text-orange-600';
  return (
    <button
      type="button"
      className={`px-3 py-1 rounded-md text-sm transition-colors duration-200 ${activeClasses}`}
      onClick={() => onClick?.(value)}
    >
      {label}
    </button>
  );
};

interface SortOptionsProps {
  options: { label: string; value: string }[];
  activeOption?: string;
  onSelect?: (value: string) => void;
}

const SortOptions: React.FC<SortOptionsProps> = ({ options, activeOption, onSelect }) => {
  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm font-medium text-gray-700">Sort by:</span>
      {options.map((option) => (
        <SortOption
          key={option.value}
          label={option.label}
          value={option.value}
          isActive={activeOption === option.value}
          onClick={onSelect}
        />
      ))}
    </div>
  );
};

export default SortOptions;
