export class TextProcessingService {
  static cleanAIResponse(text: string): string {
    return text
      // Remove markdown bold formatting
      .replace(/\*\*(.*?)\*\*/g, '$1')
      // Remove markdown italic formatting
      .replace(/\*(.*?)\*/g, '$1')
      // Remove numbered lists completely when we have restaurant cards
      .replace(/\d+\.\s+[^\n]*₨[^\n]*/g, '')
      // Clean up "Recommended Options:" section
      .replace(/Recommended Options:\s*/g, '')
      // Remove bullet points
      .replace(/•\s*/g, '')
      // Clean up extra whitespace
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, ' ')
      // Trim and ensure proper spacing
      .trim();
  }

  static formatRecommendationText(text: string): string {
    return text
      // Split into sentences and add proper line breaks
      .replace(/\.\s+/g, '.\n')
      // Format numbered recommendations better
      .replace(/(\d+)\.\s*([^.]+)/g, '\n$1. $2')
      // Clean up any double line breaks
      .replace(/\n\n+/g, '\n')
      .trim();
  }

  static extractRecommendationSummary(text: string): string {
    // Clean the text but keep the main message
    const cleaned = this.cleanAIResponse(text);

    // Split into sentences
    const sentences = cleaned.split(/[.!?]+/).filter(s => s.trim().length > 0);

    // Take the first 1-2 sentences that don't contain numbered lists
    const mainSentences = sentences.filter(sentence =>
      !sentence.match(/^\s*\d+\./) && // Remove numbered lists
      sentence.trim().length > 10 // Keep meaningful sentences
    ).slice(0, 2);

    // If we have good sentences, use them, otherwise use the first part of cleaned text
    if (mainSentences.length > 0) {
      return mainSentences.join('. ').trim() + '.';
    }

    // Fallback: take first 100 characters of cleaned text
    return cleaned.substring(0, 100).trim() + (cleaned.length > 100 ? '...' : '');
  }

  static shouldShowRestaurantCards(text: string): boolean {
    // Show cards if the response mentions specific restaurants or prices
    return text.includes('₨') || 
           text.toLowerCase().includes('restaurant') ||
           text.toLowerCase().includes('option');
  }
}
