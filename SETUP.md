# RotiShoti Setup Guide

**Author:** <PERSON><PERSON><PERSON>, <PERSON><PERSON>
**Repository:** https://github.com/Masab12/RotiShoti

## Quick Start

### 1. <PERSON><PERSON> and Install
```bash
git clone https://github.com/Masab12/RotiShoti.git
cd RotiShoti
npm run setup
```

### 2. Environment Configuration
```bash
# Backend environment
cp backend/.env.example backend/.env
# Edit backend/.env with your actual values

# Frontend environment  
cp frontend/.env.example frontend/.env.local
# Edit frontend/.env.local with your actual values
```

### 3. Required API Keys

#### Supabase Setup
1. Create account at [supabase.com](https://supabase.com)
2. Create new project
3. Get your project URL and anon key from Settings > API
4. Add to `backend/.env`:
   ```
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_KEY=your_supabase_anon_key
   ```

#### Groq API Setup
1. Create account at [console.groq.com](https://console.groq.com)
2. Generate API key
3. Add to `backend/.env`:
   ```
   GROQ_API_KEY=your_groq_api_key
   ```

### 4. Database Setup
```bash
cd backend
python seed_database.py
```

### 5. Run Development Servers
```bash
# Run both frontend and backend
npm run dev

# Or run separately:
npm run dev:backend  # Backend on :8000
npm run dev:frontend # Frontend on :3000
```

## Project Structure

```
RotiShoti/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/v1/         # API endpoints
│   │   ├── core/           # Configuration
│   │   ├── db/             # Database models
│   │   ├── schemas/        # Pydantic schemas
│   │   └── services/       # Business logic
│   ├── data/               # Restaurant data
│   ├── .env.example        # Environment template
│   └── requirements.txt    # Python dependencies
├── frontend/               # Next.js frontend
│   ├── src/
│   │   ├── app/           # Next.js app directory
│   │   ├── components/    # React components
│   │   └── store/         # State management
│   ├── .env.example       # Environment template
│   └── package.json       # Node dependencies
├── .gitignore             # Git ignore rules
├── README.md              # Project documentation
├── LICENSE                # MIT license
└── package.json           # Root package configuration
```

## Features Overview

### 🤖 AI Chatbot
- Groq LLaMA integration
- Conversation memory
- User profile awareness
- Bilingual (English/Urdu) support
- Budget-aware recommendations

### 👥 User Profiles
- Masab Farooque (Spice Lover)
- Saad Ilyas (Cafe Explorer)
- Taste profile visualizations
- Activity tracking

### 🏪 Restaurant Database
- Real Islamabad restaurants
- Complete menu information
- Accurate pricing in Pakistani Rupees (₨)
- Location details and features

### 📱 Mobile-First Design
- Responsive UI
- Mobile navigation
- Optimized chatbot interface
- Professional animations

## Development Commands

```bash
# Setup
npm run setup              # Install all dependencies
npm run setup:backend      # Install Python dependencies
npm run setup:frontend     # Install Node dependencies

# Development
npm run dev               # Run both servers
npm run dev:backend       # Run backend only
npm run dev:frontend      # Run frontend only

# Production
npm run build            # Build frontend
npm start               # Start production server
npm run lint            # Lint frontend code
```

## Environment Variables

### Backend (.env)
```env
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
GROQ_API_KEY=your_groq_key
PROJECT_NAME=RotiShoti
PROJECT_VERSION=1.0.0
DEBUG=true
```

### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=RotiShoti
NEXT_PUBLIC_APP_VERSION=1.0.0
```

## Troubleshooting

### Common Issues

1. **Backend not starting**
   - Check Python version (3.11+ required)
   - Verify virtual environment is activated
   - Ensure all environment variables are set

2. **Frontend not loading**
   - Check Node version (18+ required)
   - Clear Next.js cache: `rm -rf .next`
   - Verify backend is running on port 8000

3. **Database connection issues**
   - Verify Supabase URL and key
   - Check network connectivity
   - Ensure database is properly seeded

4. **AI chat not working**
   - Verify Groq API key is valid
   - Check API rate limits
   - Ensure backend is receiving requests

### Getting Help

For issues or questions:
- Check existing GitHub issues
- Contact: <EMAIL>
- GitHub: [@Masab12](https://github.com/Masab12)

---

**Built with ❤️ by Masab Farooque**
