export interface User {
  id: string;
  name: string;
  email: string;
  // Add other user-related fields as per your Supabase schema
  phone?: string;
  city?: string;
  age_group?: string;
  dietary_restrictions?: string[];
  created_at?: string;
  updated_at?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  // Add other auth-related fields like refresh token if applicable
}

export interface UserProfileUpdate {
  name?: string;
  phone?: string;
  city?: string;
  age_group?: string;
  dietary_restrictions?: string[];
}

export interface UserPreferences {
  cuisines?: string[];
  spice_tolerance?: string;
  budget_range?: [number, number];
  dining_style?: string[];
  preferred_areas?: string[];
}