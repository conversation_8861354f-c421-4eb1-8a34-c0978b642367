'use client';

import { create } from 'zustand';

interface ChatModalState {
  isOpen: boolean;
  initialQuery: string;
  openChat: (query?: string) => void;
  closeChat: () => void;
  toggleChat: () => void;
}

export const useChatModal = create<ChatModalState>((set, get) => ({
  isOpen: false,
  initialQuery: '',
  
  openChat: (query = '') => {
    set({ isOpen: true, initialQuery: query });
  },
  
  closeChat: () => {
    set({ isOpen: false, initialQuery: '' });
  },
  
  toggleChat: () => {
    const { isOpen } = get();
    set({ isOpen: !isOpen, initialQuery: isOpen ? '' : get().initialQuery });
  }
}));
