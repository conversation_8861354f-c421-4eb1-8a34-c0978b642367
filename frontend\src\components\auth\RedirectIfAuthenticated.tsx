'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';

interface RedirectIfAuthenticatedProps {
  children: React.ReactNode;
  redirectTo?: string;
}

export default function RedirectIfAuthenticated({ 
  children, 
  redirectTo = '/' 
}: RedirectIfAuthenticatedProps) {
  const { isAuthenticated, currentUser } = useUserStore();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated && currentUser) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, currentUser, router, redirectTo]);

  // Show loading or the children while checking authentication
  if (isAuthenticated && currentUser) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
