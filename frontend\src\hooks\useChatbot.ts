
import { useAppStore } from '@/store/appStore';
import { useState, useCallback } from 'react';

export const useChatbot = () => {
  const { chatMessages, addChatMessage, clearChatMessages } = useAppStore();
  const [isTyping, setIsTyping] = useState(false);

  const sendMessage = useCallback(async (messageText: string) => {
    if (messageText.trim() === '') return;

    const userMessage = {
      id: Date.now().toString(),
      sender: 'user' as const,
      text: messageText,
      timestamp: Date.now(),
    };
    addChatMessage(userMessage);
    setIsTyping(true);

    try {
      // In a real application, you would make an API call to your backend here
      // For example: const response = await fetch('/api/recommendations/query', { ... });
      // const data = await response.json();

      // Simulate AI response
      await new Promise((resolve) => setTimeout(resolve, 1500)); // Simulate network delay
      const aiResponseText = `AI received: "${messageText}". I'm processing your request!`;
      const aiMessage = {
        id: Date.now().toString(),
        sender: 'ai' as const,
        text: aiResponseText,
        timestamp: Date.now(),
      };
      addChatMessage(aiMessage);
    } catch (error) {
      console.error('Error sending message to AI:', error);
      const errorMessage = {
        id: Date.now().toString(),
        sender: 'ai' as const,
        text: 'Oops! Something went wrong. Please try again.',
        timestamp: Date.now(),
      };
      addChatMessage(errorMessage);
    } finally {
      setIsTyping(false);
    }
  }, [addChatMessage]);

  return {
    chatMessages,
    sendMessage,
    clearChatMessages,
    isTyping,
  };
};
