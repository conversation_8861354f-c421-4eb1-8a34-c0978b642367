# app/services/recommender.py - CORRECTED VERSION

from typing import List, Dict, Any, Optional
from app.db.supabase import create_supabase_client
from app.services.ai_service import ai_service # <--- IMPORTANT: IMPORT THE *INSTANCE* 'ai_service', not the class 'AIService'
from fastapi import HTTPException # <--- IMPORTANT: ADD THIS IMPORT!
import logging

# Initialize logging
logger = logging.getLogger(__name__)

class RecommendationEngine:
    def __init__(self):
        self.db = create_supabase_client()
        # Use the already initialized global instance of AIService
        self.ai_service = ai_service 

    async def generate_recommendations(
        self,
        user_query: str,
        user_id: str,
        location: str,
        budget: Optional[str] = None, # 'low', 'medium', 'high'
        dietary_restrictions: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Generates hybrid recommendations based on user query and preferences.
        """
        logger.info(f"Generating recommendations for query: '{user_query}' with location '{location}'")

        # 1. Generate embedding for user query
        try:
            query_embedding = await self.ai_service.generate_embedding(user_query)
        except Exception as e:
            logger.error(f"Failed to generate embedding for user query: {e}")
            raise HTTPException(status_code=500, detail="Failed to process query for recommendations.")
        
        # 2. Perform content-based filtering (fallback to simple filtering for now)
        # TODO: Implement vector search with match_restaurants function
        try:
            # Fallback: Get restaurants by location and use simple text matching
            query_builder = self.db.from_('restaurants').select('*, menu_items(*)')

            if location:
                query_builder = query_builder.eq('city', location)

            # Get all restaurants and filter by simple text matching
            all_restaurants_response = query_builder.execute()
            all_restaurants = all_restaurants_response.data or []

            # Simple text-based filtering for now
            content_based_candidates = []
            query_lower = user_query.lower()
            for restaurant in all_restaurants:
                # Check if query matches restaurant name, cuisine types, or menu items
                match_score = 0

                # Check restaurant name
                if query_lower in restaurant.get('name', '').lower():
                    match_score += 3

                # Check cuisine types
                cuisine_types = restaurant.get('cuisine_types', [])
                for cuisine in cuisine_types:
                    if query_lower in cuisine.lower():
                        match_score += 2

                # Check menu items
                menu_items = restaurant.get('menu_items', [])
                for item in menu_items:
                    if query_lower in item.get('name', '').lower():
                        match_score += 1
                    if query_lower in item.get('description', '').lower():
                        match_score += 1

                if match_score > 0:
                    restaurant['match_score'] = match_score
                    content_based_candidates.append(restaurant)

            # Sort by match score
            content_based_candidates.sort(key=lambda x: x.get('match_score', 0), reverse=True)
            content_based_candidates = content_based_candidates[:10]  # Limit to top 10

            logger.info(f"Content-based candidates: {len(content_based_candidates)}")

            # Filter candidates based on location, budget, dietary restrictions
            filtered_candidates = []
            for restaurant_data in content_based_candidates:
                # Expecting full restaurant object with menu_items from RPC
                restaurant = restaurant_data 

                if location and restaurant.get('city', '').lower() != location.lower():
                    continue
                
                # Basic budget filtering (you can refine this based on your budget strings/ranges)
                if budget:
                    min_price = restaurant.get('price_range_min')
                    max_price = restaurant.get('price_range_max')
                    
                    # Example budget mapping (adjust these ranges to your data)
                    if budget == "low" and (min_price is None or min_price > 700): continue
                    if budget == "medium" and (min_price is None or min_price < 500 or max_price > 2000): continue
                    if budget == "high" and (max_price is None or max_price < 1500): continue

                # Dietary restrictions (simple check for now, can be more complex)
                if dietary_restrictions:
                    has_restriction_issue = False
                    for restriction in dietary_restrictions:
                        # Example: Check if 'Halal' is in restaurant features if restriction is 'halal'
                        if restriction.lower() == 'halal' and not any(f.lower() == 'halal' for f in restaurant.get('features', [])):
                            has_restriction_issue = True
                            break
                        # Add other restriction checks here as needed
                    if has_restriction_issue:
                        continue
                
                filtered_candidates.append(restaurant)
            
            logger.info(f"Filtered candidates after location/budget/dietary: {len(filtered_candidates)}")

        except Exception as e:
            logger.error(f"Error calling match_restaurants RPC or processing candidates: {e}")
            # Ensure HTTPException is imported to be used here
            raise HTTPException(status_code=500, detail="Error retrieving semantic matches from database.")

        # 3. Enhance with AI (Groq API) for natural language description
        final_recommendations = []
        for restaurant in filtered_candidates:
            # Prepare prompt for Groq
            # Safely get menu_items as a list before joining
            restaurant_menu_items = ', '.join([item.get('name', '') for item in restaurant.get('menu_items', [])]) if restaurant.get('menu_items') else ''

            restaurant_summary = (
                f"Name: {restaurant.get('name')}\n"
                f"Cuisine: {', '.join(restaurant.get('cuisine_types', []))}\n"
                f"Address: {restaurant.get('address')}\n"
                f"Rating: {restaurant.get('rating_overall', 'N/A')}\n"
                f"Price Range: PKR {restaurant.get('price_range_min', 'N/A')}-{restaurant.get('price_range_max', 'N/A')}\n"
                f"Menu Items: {restaurant_menu_items}\n"
                f"Features: {', '.join(restaurant.get('features', []))}\n"
            )
            
            llm_prompt = (
                f"You are RotiShoti, an expert Pakistani food recommender. "
                f"The user is looking for a restaurant with the query: '{user_query}'. "
                f"They are in '{location}' and their budget is '{budget}'. "
                f"Their dietary restrictions are: {', '.join(dietary_restrictions) if dietary_restrictions else 'None'}.\n\n"
                f"Here is a relevant restaurant:\n{restaurant_summary}\n\n"
                f"Based on the user's query and preferences, describe why this restaurant is a good recommendation for them. "
                f"Highlight key aspects like cuisine, specific menu items, price, and ambiance. "
                f"Keep it concise and appealing."
            )
            
            ai_description = await self.ai_service.get_llm_response(llm_prompt)
            
            final_recommendations.append({
                "restaurant": restaurant,
                "ai_description": ai_description
            })
        
        return final_recommendations
    
    async def popularity_based_filtering(self, city: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Retrieves popular restaurants based on overall rating.
        """
        response = self.db.from_('restaurants').select('*, menu_items(*)').eq('city', city).order('rating_overall', desc=True).limit(limit).execute()
        return response.data or []

    async def content_based_filtering(
        self,
        query_embedding: List[float], # This might be from user profile if not explicit query
        city: Optional[str] = None,
        budget: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        A simplified content-based filtering function for personalized or general content matches.
        This would typically be for user's general preferences, not a direct search query.
        """
        query_builder = self.db.from_('restaurants').select('*, menu_items(*)')
        
        if city:
            query_builder = query_builder.eq('city', city)
        
        # In a real personalized scenario, you'd use user_profile_embedding
        # to call match_restaurants again, or filter based on preferred cuisines.
        # For this example, just return highly rated restaurants in the city.
        query_builder = query_builder.order('rating_overall', desc=True).limit(limit)
        
        response = await query_builder.execute()
        return response.data or []

# Initialize the recommender service globally once
recommender_service = RecommendationEngine()