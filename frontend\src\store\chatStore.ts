/**
 * RotiShoti - AI-Powered Food Discovery Platform
 *
 * @fileoverview Professional Chat Store with Enterprise Architecture
 * @version 2.0.0
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 RotiShoti Technologies
 */

import { create } from 'zustand';
import { persist, subscribeWithSelector } from 'zustand/middleware';

// Temporary local interfaces to avoid import issues
interface IChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  restaurants?: any[];
}

interface IUserChatHistory {
  userId: string;
  messages: IChatMessage[];
  lastUpdated: Date;
  sessionMetadata?: {
    messageCount: number;
    sessionStart?: Date;
    preferences?: Record<string, any>;
  };
}

interface IChatStore {
  userChats: IUserChatHistory[];
  currentUserId: string | null;
  isLoading: boolean;
  error: string | null;

  setCurrentUser: (userId: string) => void;
  addMessage: (message: Omit<IChatMessage, 'id' | 'timestamp'>) => void;
  clearCurrentUserChat: () => void;
  clearAllUserData: () => void;
  getCurrentUserMessages: () => IChatMessage[];
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  deleteMessage: (messageId: string) => void;
  editMessage: (messageId: string, newContent: string) => void;
  exportChatHistory: (userId: string) => string;
  importChatHistory: (userId: string, data: string) => void;
}

// Utility functions
const generateMessageId = (): string => {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 9);
  return `msg_${timestamp}_${randomStr}`;
};

const CHAT_CONFIG = {
  MAX_MESSAGES: 100,
};

const STORAGE_KEYS = {
  CHAT_HISTORY: 'rotishoti-chat-storage',
};

/**
 * Enterprise-grade Chat Store Implementation
 *
 * Features:
 * - Type-safe state management
 * - Persistent storage with versioning
 * - Performance optimizations
 * - Error handling and validation
 * - Subscription-based updates
 */

export const useChatStore = create<IChatStore>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        userChats: [],
        currentUserId: null,
        isLoading: false,
        error: null,

        setCurrentUser: (userId: string) => {
          const { currentUserId } = get();

          // If switching to a different user, clear any error state
          if (currentUserId !== userId) {
            set({ error: null, isLoading: false });
          }

          set({ currentUserId: userId });

          // Create chat history for user if it doesn't exist
          const { userChats } = get();
          const userExists = userChats.some((chat: IUserChatHistory) => chat.userId === userId);

          if (!userExists) {
            set({
              userChats: [
                ...userChats,
                {
                  userId,
                  messages: [],
                  lastUpdated: new Date(),
                  sessionMetadata: {
                    messageCount: 0,
                    sessionStart: new Date(),
                    preferences: {}
                  }
                }
              ]
            });
          }
        },

        addMessage: (message: Omit<IChatMessage, 'id' | 'timestamp'>) => {
          const { currentUserId, userChats } = get();
          if (!currentUserId) return;

          const newMessage: IChatMessage = {
            ...message,
            id: generateMessageId(),
            timestamp: new Date()
          };

          const updatedChats = userChats.map((chat: IUserChatHistory) => {
            if (chat.userId === currentUserId) {
              const updatedMessages = [...chat.messages, newMessage];

              // Limit messages to prevent memory issues
              const limitedMessages = updatedMessages.slice(-CHAT_CONFIG.MAX_MESSAGES);

              return {
                ...chat,
                messages: limitedMessages,
                lastUpdated: new Date(),
                sessionMetadata: {
                  ...chat.sessionMetadata,
                  messageCount: limitedMessages.length
                }
              };
            }
            return chat;
          });

          set({ userChats: updatedChats });
        },

        clearCurrentUserChat: () => {
          const { currentUserId, userChats } = get();
          if (!currentUserId) return;

          const updatedChats = userChats.map((chat: IUserChatHistory) => {
            if (chat.userId === currentUserId) {
              return {
                ...chat,
                messages: [],
                lastUpdated: new Date(),
                sessionMetadata: {
                  messageCount: 0,
                  sessionStart: new Date(),
                  preferences: chat.sessionMetadata?.preferences || {}
                }
              };
            }
            return chat;
          });

          set({ userChats: updatedChats });
        },

        clearAllUserData: () => {
          set({
            userChats: [],
            currentUserId: null,
            isLoading: false,
            error: null
          });
        },

        getCurrentUserMessages: () => {
          const { currentUserId, userChats } = get();
          if (!currentUserId) return [];

          const userChat = userChats.find((chat: IUserChatHistory) => chat.userId === currentUserId);
          const messages = userChat?.messages || [];

          // Ensure timestamps are Date objects
          return messages.map(msg => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }));
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        setError: (error: string | null) => {
          set({ error });
        },

        deleteMessage: (messageId: string) => {
          const { currentUserId, userChats } = get();
          if (!currentUserId) return;

          const updatedChats = userChats.map((chat: IUserChatHistory) => {
            if (chat.userId === currentUserId) {
              return {
                ...chat,
                messages: chat.messages.filter(msg => msg.id !== messageId),
                lastUpdated: new Date()
              };
            }
            return chat;
          });

          set({ userChats: updatedChats });
        },

        editMessage: (messageId: string, newContent: string) => {
          const { currentUserId, userChats } = get();
          if (!currentUserId) return;

          const updatedChats = userChats.map((chat: IUserChatHistory) => {
            if (chat.userId === currentUserId) {
              return {
                ...chat,
                messages: chat.messages.map(msg =>
                  msg.id === messageId
                    ? { ...msg, content: newContent, timestamp: new Date() }
                    : msg
                ),
                lastUpdated: new Date()
              };
            }
            return chat;
          });

          set({ userChats: updatedChats });
        },

        exportChatHistory: (userId: string) => {
          const { userChats } = get();
          const userChat = userChats.find((chat: IUserChatHistory) => chat.userId === userId);
          return JSON.stringify(userChat, null, 2);
        },

        importChatHistory: (userId: string, data: string) => {
          try {
            const importedChat: IUserChatHistory = JSON.parse(data);
            const { userChats } = get();

            const updatedChats = userChats.map((chat: IUserChatHistory) =>
              chat.userId === userId ? importedChat : chat
            );

            set({ userChats: updatedChats });
          } catch (error) {
            console.error('Failed to import chat history:', error);
            set({ error: 'Failed to import chat history' });
          }
        }
      }),
      {
        name: STORAGE_KEYS.CHAT_HISTORY,
        version: 2,
        migrate: (persistedState: any, version: number) => {
          // Handle migration from older versions
          if (version < 2) {
            return {
              ...persistedState,
              isLoading: false,
              error: null
            };
          }
          return persistedState;
        }
      }
    )
  )
);
