# 🍽️ RotiShoti - AI-Powered Food Discovery Platform

**Authors: <AUTHORS>
**Repository:** https://github.com/Masab12/RotiShoti
**Version:** 2.0.0
**License:** MIT

## 📖 Overview

RotiShoti is an intelligent food discovery platform designed specifically for Pakistani food lovers. Using advanced AI technology, it provides personalized restaurant recommendations, maintains conversation context, and offers a culturally authentic experience with bilingual (English/Urdu) support.

## ✨ Key Features

### 🤖 AI-Powered Chatbot
- **Groq LLaMA Integration** for intelligent conversations
- **Conversation Memory** - maintains context across messages
- **User Profile Integration** - personalized recommendations based on preferences
- **Bilingual Support** - Natural English/Urdu conversation style
- **Budget-Aware Filtering** - strict adherence to user's price constraints

### 👥 User Profile System
- **Dummy Profiles** - Ma<PERSON><PERSON> (Spice Lover) & <PERSON><PERSON> (Cafe Explorer)
- **Taste Profile Visualization** - Interactive radar charts and progress bars
- **Activity Tracking** - Search history, visited restaurants, saved favorites
- **Preference Management** - Cuisine types, spice levels, dietary restrictions

### 🏪 Restaurant Database
- **Real Islamabad Restaurants** - Authentic data for 6+ popular venues
- **Complete Menu Information** - Items, prices, descriptions, categories
- **Location Details** - Exact addresses, areas, features
- **Vector Search Ready** - Supabase integration for semantic search

### 📱 Mobile-First Design
- **Responsive UI** - Perfect on all screen sizes
- **Mobile Navigation** - Bottom tab bar for easy access
- **Optimized Chatbot** - Expandable mobile interface
- **Professional Animations** - Smooth transitions and effects

## 🛠️ Technology Stack

### Backend
- **FastAPI** - High-performance Python web framework
- **Supabase** - PostgreSQL database with real-time features
- **Groq API** - LLaMA model for AI conversations
- **Pydantic** - Data validation and serialization
- **CORS** - Cross-origin resource sharing

### Frontend
- **Next.js 15** - React framework with Turbopack
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **Zustand** - State management
- **Custom Animations** - CSS transitions and keyframes

### Database
- **PostgreSQL** (via Supabase)
- **Vector Embeddings** - For semantic restaurant search
- **Real-time Subscriptions** - Live data updates

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Python 3.11+
- Supabase account
- Groq API key

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/Masab12/RotiShoti.git
cd RotiShoti
```

2. **Backend Setup**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **Frontend Setup**
```bash
cd frontend
npm install
```

4. **Environment Configuration**
Create `.env` files in both backend and frontend directories:

**Backend `.env`:**
```env
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key
GROQ_API_KEY=your_groq_api_key
```

**Frontend `.env.local`:**
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

5. **Database Setup**
```bash
cd backend
python seed_database.py
```

6. **Run the Application**

Backend:
```bash
cd backend
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

Frontend:
```bash
cd frontend
npm run dev
```

Visit `http://localhost:3000` to access the application.

## 📁 Project Structure

```
RotiShoti/
├── backend/
│   ├── app/
│   │   ├── api/v1/          # API endpoints
│   │   ├── core/            # Configuration
│   │   ├── db/              # Database models
│   │   ├── schemas/         # Pydantic schemas
│   │   └── services/        # Business logic
│   ├── data/                # Restaurant data
│   └── main.py              # FastAPI application
├── frontend/
│   ├── src/
│   │   ├── app/             # Next.js app directory
│   │   ├── components/      # React components
│   │   └── store/           # State management
│   └── public/              # Static assets
└── README.md
```

## 🎯 Core Features

### Restaurant Recommendations
- Personalized suggestions based on user preferences
- Budget-aware filtering with Pakistani Rupee (₨) support
- Real menu items with accurate pricing
- Location-based recommendations

### Conversation Intelligence
- Context-aware responses
- User profile integration
- Cultural authenticity with Urdu expressions
- Memory across chat sessions

### User Experience
- Professional Pakistani-inspired design (orange/green theme)
- Mobile-responsive interface
- Smooth animations and transitions
- Intuitive navigation

## 🔒 Security & Privacy

- Environment variables for sensitive data
- API key protection
- CORS configuration
- Input validation and sanitization
- No hardcoded credentials

## 🤝 Contributing

This project is authored by **Masab Farooque** and **Saad Ilyas**. For contributions or suggestions, please contact the authors.

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- **Supabase** for the excellent database platform
- **Groq** for AI model access
- **Next.js** team for the amazing framework
- **Tailwind CSS** for the utility-first approach

---

**Built with ❤️ by Masab Farooque & Saad Ilyas**
*Bringing Pakistani food culture to the digital world*
