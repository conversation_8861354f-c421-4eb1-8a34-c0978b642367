
import React from 'react';

interface SearchInputProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  onVoiceSearch?: () => void;
}

const SearchInput: React.FC<SearchInputProps> = ({ placeholder = "Search for restaurants or dishes...", onSearch, onVoiceSearch }) => {
  return (
    <div className="relative flex items-center w-full max-w-md mx-auto">
      <input
        type="text"
        placeholder={placeholder}
        className="w-full px-4 py-2 pl-10 pr-12 text-gray-900 bg-white border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
        onChange={(e) => onSearch?.(e.target.value)}
      />
      <div className="absolute inset-y-0 left-0 flex items-center pl-3">
        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>
      {onVoiceSearch && (
        <button
          type="button"
          onClick={onVoiceSearch}
          className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-orange-600 focus:outline-none"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-14 0v-1a7 7 0 0114 0v1z"></path>
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 21h4m-2-4v4m-2-4h4"></path>
          </svg>
        </button>
      )}
    </div>
  );
};

export default SearchInput;
