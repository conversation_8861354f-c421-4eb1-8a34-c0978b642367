"""
RotiShoti - AI-Powered Food Discovery Platform
Author: <PERSON><PERSON>b Farooque
Restaurant Service - Contains all restaurant-related business logic
"""

from typing import List, Dict, Any, Optional, Tuple
from app.db.supabase import create_supabase_client


class RestaurantService:
    def __init__(self):
        self.supabase = create_supabase_client()

    async def get_restaurants_by_city(self, city: str) -> List[Dict[str, Any]]:
        """
        Get all restaurants in a specific city
        """
        try:
            response = self.supabase.from_("restaurants").select("""
                id, name, city, area, address, latitude, longitude,
                cuisine_types, price_range_min, price_range_max, price_range_average,
                ambiance_type, features, phone, website,
                rating_overall, rating_food, rating_service, rating_value
            """).eq("city", city).execute()
            
            return response.data or []
        except Exception as e:
            print(f"Error fetching restaurants: {e}")
            return []

    async def get_restaurants_with_menu_items(self, city: str) -> List[Dict[str, Any]]:
        """
        Get restaurants with their menu items for a specific city
        """
        try:
            response = self.supabase.from_("restaurants").select("""
                name, cuisine_types, area, address, latitude, longitude,
                price_range_average, rating_overall, features,
                menu_items(name, price, description, category)
            """).eq("city", city).execute()
            
            return response.data or []
        except Exception as e:
            print(f"Error fetching restaurants with menu items: {e}")
            return []

    async def get_restaurant_by_id(self, restaurant_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific restaurant by ID
        """
        try:
            response = self.supabase.from_("restaurants").select("*").eq("id", restaurant_id).single().execute()
            return response.data
        except Exception as e:
            print(f"Error fetching restaurant by ID: {e}")
            return None

    async def search_restaurants_by_cuisine(self, city: str, cuisine_types: List[str]) -> List[Dict[str, Any]]:
        """
        Search restaurants by cuisine types
        """
        try:
            # Note: This is a simplified version. In practice, you might need more complex querying
            # for array contains operations depending on your Supabase setup
            response = self.supabase.from_("restaurants").select("*").eq("city", city).execute()
            
            restaurants = response.data or []
            
            # Filter by cuisine types (client-side filtering for now)
            filtered_restaurants = []
            for restaurant in restaurants:
                restaurant_cuisines = restaurant.get('cuisine_types', [])
                if any(cuisine.lower() in [c.lower() for c in restaurant_cuisines] for cuisine in cuisine_types):
                    filtered_restaurants.append(restaurant)
            
            return filtered_restaurants
        except Exception as e:
            print(f"Error searching restaurants by cuisine: {e}")
            return []

    async def get_restaurants_by_price_range(self, city: str, min_price: int, max_price: int) -> List[Dict[str, Any]]:
        """
        Get restaurants within a specific price range
        """
        try:
            response = self.supabase.from_("restaurants").select("*").eq("city", city).execute()
            
            restaurants = response.data or []
            
            # Filter by price range
            filtered_restaurants = []
            for restaurant in restaurants:
                avg_price = restaurant.get('price_range_average', 0)
                if min_price <= avg_price <= max_price:
                    filtered_restaurants.append(restaurant)
            
            return filtered_restaurants
        except Exception as e:
            print(f"Error fetching restaurants by price range: {e}")
            return []

    async def get_menu_items_by_restaurant(self, restaurant_id: str) -> List[Dict[str, Any]]:
        """
        Get all menu items for a specific restaurant
        """
        try:
            response = self.supabase.from_("menu_items").select("*").eq("restaurant_id", restaurant_id).execute()
            return response.data or []
        except Exception as e:
            print(f"Error fetching menu items: {e}")
            return []

    async def search_menu_items_by_name(self, city: str, item_name: str) -> List[Dict[str, Any]]:
        """
        Search menu items by name across all restaurants in a city
        """
        try:
            # Get restaurants with menu items
            restaurants = await self.get_restaurants_with_menu_items(city)
            
            matching_items = []
            for restaurant in restaurants:
                menu_items = restaurant.get('menu_items', [])
                for item in menu_items:
                    if item_name.lower() in item.get('name', '').lower():
                        item['restaurant_name'] = restaurant['name']
                        item['restaurant_area'] = restaurant.get('area', '')
                        matching_items.append(item)
            
            return matching_items
        except Exception as e:
            print(f"Error searching menu items: {e}")
            return []

    def filter_restaurants_by_budget(self, restaurants: List[Dict[str, Any]], 
                                   budget_range: Optional[Tuple[int, int]] = None) -> List[Dict[str, Any]]:
        """
        Filter restaurants based on budget range
        """
        if not budget_range:
            return restaurants
        
        min_budget, max_budget = budget_range
        filtered_restaurants = []
        
        for restaurant in restaurants:
            menu_items = restaurant.get('menu_items', [])
            # Filter menu items by budget range
            filtered_items = [
                item for item in menu_items 
                if min_budget <= item.get('price', 0) <= max_budget
            ]
            
            # Only include restaurant if it has items in budget range
            if filtered_items:
                restaurant_copy = restaurant.copy()
                restaurant_copy['menu_items'] = filtered_items
                filtered_restaurants.append(restaurant_copy)
        
        return filtered_restaurants

    def format_restaurant_for_display(self, restaurant: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format restaurant data for frontend display
        """
        return {
            "id": restaurant.get("id"),
            "name": restaurant.get("name"),
            "area": restaurant.get("area"),
            "address": restaurant.get("address"),
            "cuisine_types": restaurant.get("cuisine_types", []),
            "price_range_average": restaurant.get("price_range_average"),
            "rating_overall": restaurant.get("rating_overall"),
            "features": restaurant.get("features", []),
            "coordinates": {
                "latitude": restaurant.get("latitude"),
                "longitude": restaurant.get("longitude")
            }
        }

    async def get_restaurant_recommendations(self, city: str, user_preferences: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get restaurant recommendations based on user preferences
        """
        try:
            restaurants = await self.get_restaurants_with_menu_items(city)
            
            # Apply filters based on user preferences
            if user_preferences.get("cuisine_types"):
                restaurants = await self.search_restaurants_by_cuisine(
                    city, user_preferences["cuisine_types"]
                )
            
            if user_preferences.get("budget_range"):
                min_budget, max_budget = user_preferences["budget_range"]
                restaurants = self.filter_restaurants_by_budget(
                    restaurants, (min_budget, max_budget)
                )
            
            # Sort by rating (highest first)
            restaurants.sort(key=lambda x: x.get('rating_overall', 0), reverse=True)
            
            return restaurants[:10]  # Return top 10 recommendations
        except Exception as e:
            print(f"Error getting restaurant recommendations: {e}")
            return []


# Create global instance
restaurant_service = RestaurantService()
