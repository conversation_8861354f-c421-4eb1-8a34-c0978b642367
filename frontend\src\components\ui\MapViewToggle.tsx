
import React from 'react';

interface MapViewToggleProps {
  isMapView: boolean;
  onToggle: (isMapView: boolean) => void;
}

const MapViewToggle: React.FC<MapViewToggleProps> = ({ isMapView, onToggle }) => {
  return (
    <div className="flex items-center space-x-2">
      <button
        className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${!isMapView ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
        onClick={() => onToggle(false)}
      >
        List View
      </button>
      <button
        className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${isMapView ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
        onClick={() => onToggle(true)}
      >
        Map View
      </button>
    </div>
  );
};

export default MapViewToggle;
