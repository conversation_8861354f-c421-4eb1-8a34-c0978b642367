{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "AI-powered food discovery platform for Pakistani cuisine", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Masab12"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Masab12/RotiShoti.git"}, "homepage": "https://github.com/Masab12/RotiShoti#readme", "bugs": {"url": "https://github.com/Masab12/RotiShoti/issues"}, "keywords": ["food", "restaurants", "ai", "pakistan", "islamabad", "recommendations", "chatbot", "nextjs", "<PERSON><PERSON><PERSON>", "supabase"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000", "dev:frontend": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start": "cd frontend && npm start", "lint": "cd frontend && npm run lint", "setup": "npm run setup:backend && npm run setup:frontend", "setup:backend": "cd backend && pip install -r requirements.txt", "setup:frontend": "cd frontend && npm install"}, "dependencies": {"@supabase/supabase-js": "^2.50.3", "@types/leaflet": "^1.9.20", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "leaflet": "^1.9.4", "lucide-react": "^0.400.0", "react-leaflet": "^5.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "python": ">=3.11.0"}}