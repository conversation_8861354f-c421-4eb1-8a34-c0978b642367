
'use client';

import React, { useState } from 'react';
import Header from '@/components/layout/Header';
import SearchInput from '@/components/ui/SearchInput';
import FilterChips from '@/components/ui/FilterChips';
import SortOptions from '@/components/ui/SortOptions';
import MapViewToggle from '@/components/ui/MapViewToggle';
import RestaurantCard from '@/components/ui/RestaurantCard';
import SkeletonLoader from '@/components/ui/SkeletonLoader';
import MapView from '@/components/ui/MapView'; // Assuming you have this component

export default function SearchPage() {
  const [isMapView, setIsMapView] = useState(false);
  const [activeCuisines, setActiveCuisines] = useState<string[]>([]);
  const [activeSort, setActiveSort] = useState('popularity');
  const [loading, setLoading] = useState(false); // Simulate loading

  const handleSearch = (query: string) => {
    console.log('Search query on search page:', query);
    // Implement search logic
  };

  const handleVoiceSearch = () => {
    console.log('Voice search initiated on search page');
    // Implement voice search logic
  };

  const handleCuisineClick = (label: string) => {
    setActiveCuisines((prev) =>
      prev.includes(label) ? prev.filter((c) => c !== label) : [...prev, label]
    );
  };

  const handleSortSelect = (value: string) => {
    setActiveSort(value);
  };

  const toggleMapView = (view: boolean) => {
    setIsMapView(view);
  };

  // Dummy data for demonstration
  const restaurants = [
    {
      id: '1',
      name: 'The Gourmet Grill',
      cuisine: 'Steakhouse',
      rating: 4.5,
      priceRange: '$$',
      imageUrl: '/images/restaurant1.jpg', // Placeholder image
      address: '123 Main St, Lahore',
    },
    {
      id: '2',
      name: 'Biryani House',
      cuisine: 'Pakistani',
      rating: 4.2,
      priceRange: '$',
      imageUrl: '/images/restaurant2.jpg', // Placeholder image
      address: '456 Oak Ave, Lahore',
    },
    {
      id: '3',
      name: 'Pasta Paradise',
      cuisine: 'Italian',
      rating: 4.7,
      priceRange: '$$$',
      imageUrl: '/images/restaurant3.jpg', // Placeholder image
      address: '789 Pine Ln, Lahore',
    },
  ];

  const cuisineFilters = ['Pakistani', 'Chinese', 'Continental', 'Italian', 'Steakhouse', 'Fast Food', 'BBQ', 'Desserts'];
  const sortOptions = [
    { label: 'Popularity', value: 'popularity' },
    { label: 'Distance', value: 'distance' },
    { label: 'Price', value: 'price' },
    { label: 'Rating', value: 'rating' },
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      <Header />

      <main className="container mx-auto p-4">
        <div className="mb-6">
          <SearchInput onSearch={handleSearch} onVoiceSearch={handleVoiceSearch} />
        </div>

        {/* Filter and Sort Bar */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-6 space-y-4 md:space-y-0">
          <div className="flex flex-wrap gap-2">
            <FilterChips chips={cuisineFilters} activeChips={activeCuisines} onChipClick={handleCuisineClick} />
          </div>
          <div className="flex items-center space-x-4">
            <SortOptions options={sortOptions} activeOption={activeSort} onSelect={handleSortSelect} />
            <MapViewToggle isMapView={isMapView} onToggle={toggleMapView} />
          </div>
        </div>

        {/* Search Results Display */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <SkeletonLoader /><SkeletonLoader /><SkeletonLoader />
          </div>
        ) : isMapView ? (
          <div className="h-[600px] w-full rounded-lg shadow-md overflow-hidden">
            <MapView /> {/* This component needs to be implemented */}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {restaurants.map((restaurant) => (
              <RestaurantCard key={restaurant.id} {...restaurant} />
            ))}
          </div>
        )}
      </main>
    </div>
  );
}
