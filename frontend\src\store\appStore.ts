
/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Author: <PERSON><PERSON><PERSON>
 * Application state management for chat and global state
 */

import { create } from 'zustand';

interface User {
  id: string;
  name: string;
  email: string;
}

interface UserPreferences {
  dietaryRestrictions: string[];
  spiceLevel?: string;
  budget?: 'low' | 'medium' | 'high';
  favoriteCuisines?: string[];
  location?: string;
}

interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  text: string;
  timestamp: number;
}

interface AppState {
  currentUser: User | null;
  currentLocation: { lat: number; lng: number; address: string } | null;
  userPreferences: UserPreferences | null;
  chatMessages: ChatMessage[];
  
  // Actions
  setCurrentUser: (user: User | null) => void;
  setCurrentLocation: (location: { lat: number; lng: number; address: string } | null) => void;
  setUserPreferences: (preferences: UserPreferences | null) => void;
  addChatMessage: (message: ChatMessage) => void;
  clearChatMessages: () => void;
}

export const useAppStore = create<AppState>((set) => ({
  currentUser: null,
  currentLocation: null,
  userPreferences: null,
  chatMessages: [],

  setCurrentUser: (user) => set({ currentUser: user }),
  setCurrentLocation: (location) => set({ currentLocation: location }),
  setUserPreferences: (preferences) => set({ userPreferences: preferences }),
  addChatMessage: (message) =>
    set((state) => ({
      chatMessages: [...state.chatMessages, message],
    })),
  clearChatMessages: () => set({ chatMessages: [] }),
}));
