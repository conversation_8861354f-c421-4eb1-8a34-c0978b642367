
import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://127.0.0.1:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include the auth token
api.interceptors.request.use(
  (config) => {
    // You would typically get the token from a state management solution (e.g., Zustand) or localStorage
    // const token = useAppStore.getState().currentUser?.token;
    const token = localStorage.getItem('access_token'); // Get the access token from localStorage
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export const authApi = {
  register: (userData: any) => api.post('/api/v1/auth/register', userData),
  login: (credentials: any) => api.post('/api/v1/auth/login', credentials),
  getProfile: () => api.get('/api/v1/auth/profile'),
  updateProfile: (profileData: any) => api.put('/api/v1/auth/profile', profileData),
};

export const restaurantApi = {
  getAll: (params?: any) => api.get('/api/v1/restaurants', { params }),
  getById: (id: string) => api.get(`/api/v1/restaurants/${id}`),
  search: (params?: any) => api.get('/api/v1/restaurants/search', { params }),
  getNearMe: (params?: any) => api.get('/api/v1/restaurants/near_me', { params }),
};

export const recommendationApi = {
  query: (data: any) => api.post('/api/v1/recommendations/query', data),
  getPopular: () => api.get('/api/v1/recommendations/popular'),
  getPersonalized: () => api.get('/api/v1/recommendations/personalized'),
  sendFeedback: (data: any) => api.post('/api/v1/recommendations/feedback', data),
};

export const userApi = {
  getHistory: () => api.get('/api/v1/users/me/history'),
  likeRestaurant: (data: { restaurant_id: string }) => api.post('/api/v1/users/me/like', data),
  rateRestaurant: (data: { restaurant_id: string; rating: number; review: string }) => api.post('/api/v1/users/me/rate', data),
  updatePreferences: (data: any) => api.put('/api/v1/users/me/preferences', data),
};

export default api;
