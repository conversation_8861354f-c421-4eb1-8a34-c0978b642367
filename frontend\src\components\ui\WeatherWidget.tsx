'use client';

import React, { useState, useEffect } from 'react';

interface WeatherData {
  temperature: number;
  description: string;
  humidity: number;
  windSpeed: number;
  icon: string;
  feelsLike: number;
}

export const WeatherWidget: React.FC = () => {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [mounted, setMounted] = useState(false);

  const fetchWeather = async () => {
    try {
      // Use mock data for now (weather API requires valid key)
      setWeather({
        temperature: 28,
        description: 'Partly Cloudy',
        humidity: 65,
        windSpeed: 12,
        icon: '02d',
        feelsLike: 31
      });
    } catch (error) {
      console.error('Weather fetch error:', error);
      // Fallback to mock data
      setWeather({
        temperature: 28,
        description: 'Partly Cloudy',
        humidity: 65,
        windSpeed: 12,
        icon: '02d',
        feelsLike: 31
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setMounted(true);
    fetchWeather();
  }, []);

  // Prevent SSR issues
  if (!mounted) {
    return (
      <div className="bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
        <div className="animate-pulse">
          <div className="h-4 bg-white bg-opacity-30 rounded mb-2"></div>
          <div className="h-8 bg-white bg-opacity-30 rounded mb-2"></div>
          <div className="h-4 bg-white bg-opacity-30 rounded"></div>
        </div>
      </div>
    );
  }

  const getWeatherIcon = (iconCode: string) => {
    const iconMap: { [key: string]: string } = {
      '01d': '☀️', '01n': '🌙',
      '02d': '⛅', '02n': '☁️',
      '03d': '☁️', '03n': '☁️',
      '04d': '☁️', '04n': '☁️',
      '09d': '🌧️', '09n': '🌧️',
      '10d': '🌦️', '10n': '🌧️',
      '11d': '⛈️', '11n': '⛈️',
      '13d': '❄️', '13n': '❄️',
      '50d': '🌫️', '50n': '🌫️'
    };
    return iconMap[iconCode] || '🌤️';
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const getFoodSuggestion = () => {
    if (!weather) return 'Perfect weather for food!';
    
    if (weather.temperature > 30) {
      return 'Hot day! Try some cold drinks and ice cream 🍦';
    } else if (weather.temperature < 15) {
      return 'Chilly weather! Perfect for hot soup and tea ☕';
    } else if (weather.description.includes('rain')) {
      return 'Rainy day! Great for pakoras and chai 🌧️';
    } else {
      return 'Beautiful weather! Perfect for outdoor dining 🍽️';
    }
  };

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
        <div className="animate-pulse">
          <div className="h-4 bg-white bg-opacity-30 rounded mb-2"></div>
          <div className="h-8 bg-white bg-opacity-30 rounded mb-2"></div>
          <div className="h-4 bg-white bg-opacity-30 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !weather) {
    return (
      <div className="bg-gradient-to-br from-gray-400 to-gray-600 rounded-2xl p-6 text-white shadow-lg">
        <div className="text-center">
          <div className="text-3xl mb-2">🌤️</div>
          <h3 className="font-bold">Weather Unavailable</h3>
          <p className="text-sm opacity-90">Islamabad, Pakistan</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-300">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-bold">{getGreeting()}!</h3>
          <p className="text-sm opacity-90">📍 Islamabad, Pakistan</p>
        </div>
        <div className="text-4xl">
          {getWeatherIcon(weather.icon)}
        </div>
      </div>

      <div className="flex items-center justify-between mb-4">
        <div>
          <div className="text-3xl font-bold">{weather.temperature}°C</div>
          <div className="text-sm opacity-90 capitalize">{weather.description}</div>
          <div className="text-xs opacity-75">Feels like {weather.feelsLike}°C</div>
        </div>
        <div className="text-right text-sm space-y-1">
          <div className="flex items-center gap-2">
            <span>💧</span>
            <span>{weather.humidity}%</span>
          </div>
          <div className="flex items-center gap-2">
            <span>💨</span>
            <span>{weather.windSpeed} km/h</span>
          </div>
        </div>
      </div>

      <div className="bg-black bg-opacity-30 rounded-lg p-3 backdrop-blur-sm border border-white border-opacity-50">
        <div className="text-sm font-bold mb-1 text-white drop-shadow-lg">🍽️ Food Suggestion</div>
        <div className="text-xs text-white font-medium drop-shadow-lg">{getFoodSuggestion()}</div>
      </div>

      <div className="mt-3 text-xs opacity-75 text-center">
        Last updated: {new Date().toLocaleTimeString()}
      </div>
    </div>
  );
};
