"use client";

import { useState, useEffect } from 'react';

interface SimpleCaptchaProps {
  onVerify: (isValid: boolean) => void;
  className?: string;
}

export default function SimpleCaptcha({ onVerify, className = "" }: SimpleCaptchaProps) {
  const [captchaText, setCaptchaText] = useState('');
  const [userInput, setUserInput] = useState('');
  const [isVerified, setIsVerified] = useState(false);

  // Generate random captcha text
  const generateCaptcha = () => {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setCaptchaText(result);
    setUserInput('');
    setIsVerified(false);
    onVerify(false);
  };

  useEffect(() => {
    generateCaptcha();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setUserInput(value);
    
    const isValid = value === captchaText;
    setIsVerified(isValid);
    onVerify(isValid);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        Captcha Verification
      </label>
      
      {/* Captcha Display */}
      <div className="flex items-center space-x-3">
        <div 
          className="bg-gray-100 border-2 border-gray-300 px-4 py-2 rounded font-mono text-lg tracking-wider select-none"
          style={{
            background: 'linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)',
            backgroundSize: '20px 20px',
            backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
          }}
        >
          {captchaText}
        </div>
        
        <button
          type="button"
          onClick={generateCaptcha}
          className="px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          🔄
        </button>
      </div>

      {/* Input Field */}
      <div>
        <input
          type="text"
          value={userInput}
          onChange={handleInputChange}
          placeholder="Enter the captcha text"
          className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            userInput ? (isVerified ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50') : 'border-gray-300'
          }`}
        />
        
        {userInput && (
          <p className={`text-sm mt-1 ${isVerified ? 'text-green-600' : 'text-red-600'}`}>
            {isVerified ? '✓ Captcha verified' : '✗ Captcha does not match'}
          </p>
        )}
      </div>
    </div>
  );
}
