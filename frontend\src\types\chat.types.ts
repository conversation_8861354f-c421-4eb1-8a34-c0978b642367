/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * 
 * @fileoverview Chat-related TypeScript type definitions
 * @version 1.0.0
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 RotiShoti Technologies
 */

/**
 * Represents a single chat message in the conversation
 */
export interface IChatMessage {
  /** Unique identifier for the message */
  id: string;
  
  /** Type of message sender */
  type: 'user' | 'ai';
  
  /** Text content of the message */
  content: string;
  
  /** Timestamp when message was created */
  timestamp: Date;
  
  /** Optional restaurant data for AI responses */
  restaurants?: IRestaurantCard[];
  
  /** Optional metadata for message */
  metadata?: {
    /** Processing time for AI responses */
    processingTime?: number;
    /** Confidence score for AI responses */
    confidence?: number;
    /** User location when message was sent */
    location?: string;
  };
}

/**
 * Restaurant card data structure for chat display
 */
export interface IRestaurantCard {
  /** Restaurant name */
  name: string;
  
  /** Area/location of restaurant */
  area: string;
  
  /** Full address */
  address: string;
  
  /** Rating display string (e.g., "4.5/5") */
  rating: string;
  
  /** Recommended menu item name */
  item_name: string;
  
  /** Price as string (e.g., "3499") */
  price: string;
  
  /** Google Maps embed URL */
  map_embed_url: string;
  
  /** Optional additional data */
  cuisine_types?: string[];
  phone?: string;
  website?: string;
}

/**
 * User chat history structure
 */
export interface IUserChatHistory {
  /** User ID */
  userId: string;
  
  /** Array of chat messages */
  messages: IChatMessage[];
  
  /** Last update timestamp */
  lastUpdated: Date;
  
  /** Optional session metadata */
  sessionMetadata?: {
    /** Total messages in session */
    messageCount: number;
    /** Session start time */
    sessionStart: Date;
    /** User preferences during session */
    preferences?: Record<string, any>;
  };
}

/**
 * Chat API request structure
 */
export interface IChatRequest {
  /** User's query text */
  user_query: string;
  
  /** User's location */
  location?: string;
  
  /** Budget preference */
  budget?: string;
  
  /** User profile data */
  user_profile?: {
    name: string;
    food_personality: string;
    favorite_cuisines: string[];
    spice_level: string;
    dining_style: string[];
    taste_profile: Record<string, any>;
    budget_range?: number[];
  };
  
  /** Conversation history for context */
  conversation_history?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}

/**
 * Chat API response structure
 */
export interface IChatResponse {
  /** AI response text */
  response: string;
  
  /** Array of restaurant recommendations */
  restaurants?: IRestaurantCard[];
  
  /** Optional metadata */
  metadata?: {
    /** Processing time */
    processing_time?: number;
    /** Number of restaurants found */
    restaurants_found?: number;
    /** Search parameters used */
    search_params?: Record<string, any>;
  };
}

/**
 * Chat component props interface
 */
export interface IChatPanelProps {
  /** Whether chat is open */
  isOpen?: boolean;
  
  /** Close handler */
  onClose?: () => void;
  
  /** Whether chat is embedded in page */
  isEmbedded?: boolean;
  
  /** Custom styling classes */
  className?: string;
  
  /** Theme variant */
  theme?: 'light' | 'dark' | 'food';
  
  /** Custom configuration */
  config?: {
    /** Maximum messages to display */
    maxMessages?: number;
    /** Enable/disable animations */
    animations?: boolean;
    /** Auto-scroll behavior */
    autoScroll?: boolean;
  };
}

/**
 * Chat store state interface
 */
export interface IChatStore {
  /** All user chat histories */
  userChats: IUserChatHistory[];
  
  /** Currently active user ID */
  currentUserId: string | null;
  
  /** Loading state */
  isLoading: boolean;
  
  /** Error state */
  error: string | null;
  
  // Actions
  setCurrentUser: (userId: string) => void;
  addMessage: (message: Omit<IChatMessage, 'id' | 'timestamp'>) => void;
  clearCurrentUserChat: () => void;
  getCurrentUserMessages: () => IChatMessage[];
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Advanced actions
  deleteMessage: (messageId: string) => void;
  editMessage: (messageId: string, newContent: string) => void;
  exportChatHistory: (userId: string) => string;
  importChatHistory: (userId: string, data: string) => void;
}
