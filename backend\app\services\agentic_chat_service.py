"""
RotiShoti - AI-Powered Food Discovery Platform
Author: <PERSON><PERSON><PERSON>
Agentic Chat Service - Multi-LLM coordination with fact-checking
"""

import asyncio
import json
import logging
import math
from typing import Dict, List, Any, Optional
from groq import AsyncGroq
from app.core.config import settings
from app.services.restaurant_service import restaurant_service
from app.services.chat_service import chat_service

logger = logging.getLogger(__name__)

class AgenticChatService:
    def __init__(self):
        # Primary LLM for conversation and recommendations
        self.primary_client = AsyncGroq(api_key=settings.GROQ_API_KEY)

        # Secondary LLM for fact-checking and validation (separate API key)
        fact_checker_key = settings.GROQ_FACT_CHECKER_API_KEY or settings.GROQ_API_KEY
        self.fact_checker_client = AsyncGroq(api_key=fact_checker_key)

        self.primary_model = "llama-3.3-70b-versatile"  # Best for conversation & recommendations
        self.fact_checker_model = "llama-3.1-8b-instant"  # Fast and efficient for fact-checking

        # Enhanced personality and context awareness
        self.personality_prompts = {
            'friendly': "You are RotiShoti AI, a warm and friendly food guide who loves helping people discover amazing Pakistani cuisine. Use emojis naturally and be conversational.",
            'professional': "You are RotiShoti AI, a professional dining consultant providing expert recommendations with detailed analysis and precise information.",
            'enthusiastic': "You are RotiShoti AI, an incredibly excited and passionate foodie who LOVES discovering amazing restaurants! Use lots of enthusiasm and energy in your responses!"
        }

        # Use existing chat service instance

    def _analyze_query_intent(self, query_lower: str) -> str:
        """Analyze user query to understand intent for better responses"""
        if any(word in query_lower for word in ['recommend', 'suggest', 'best', 'good', 'top']):
            return "seeking_recommendations"
        elif any(word in query_lower for word in ['cheap', 'budget', 'affordable', 'under']):
            return "budget_conscious"
        elif any(word in query_lower for word in ['near', 'close', 'nearby', 'location']):
            return "location_focused"
        elif any(word in query_lower for word in ['menu', 'dishes', 'items', 'food']):
            return "menu_inquiry"
        elif any(word in query_lower for word in ['compare', 'vs', 'versus', 'difference']):
            return "comparison_request"
        elif any(word in query_lower for word in ['spicy', 'mild', 'hot', 'sweet']):
            return "taste_preference"
        elif any(word in query_lower for word in ['vegetarian', 'vegan', 'halal', 'diet']):
            return "dietary_requirement"
        else:
            return "general_inquiry"

    def _get_search_parameters(self, query_intent: str, query: str) -> dict:
        """
        Get optimized search parameters based on query intent
        """
        base_params = {
            "limit": 30,
            "similarity_threshold": 0.4
        }

        # Adjust parameters based on intent
        if query_intent == "seeking_recommendations":
            base_params.update({
                "limit": 40,  # More options for recommendations
                "similarity_threshold": 0.35  # Cast wider net
            })
        elif query_intent == "budget_conscious":
            base_params.update({
                "limit": 50,  # More options to find budget items
                "similarity_threshold": 0.3  # Lower threshold for budget searches
            })
        elif query_intent == "location_focused":
            base_params.update({
                "limit": 25,  # Fewer but more location-relevant
                "similarity_threshold": 0.45  # Higher precision for location
            })
        elif query_intent == "menu_inquiry":
            base_params.update({
                "limit": 20,  # Focus on specific items
                "similarity_threshold": 0.5  # Higher precision for menu items
            })
        elif query_intent == "comparison_request":
            base_params.update({
                "limit": 15,  # Fewer but more comparable options
                "similarity_threshold": 0.4
            })
        elif query_intent == "dietary_requirement":
            base_params.update({
                "limit": 35,  # More options for dietary needs
                "similarity_threshold": 0.35
            })

        return base_params

    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """
        Calculate distance between two points using Haversine formula
        Returns distance in kilometers
        """
        R = 6371  # Earth's radius in kilometers

        # Convert latitude and longitude from degrees to radians
        lat1_rad = math.radians(lat1)
        lng1_rad = math.radians(lng1)
        lat2_rad = math.radians(lat2)
        lng2_rad = math.radians(lng2)

        # Haversine formula
        dlat = lat2_rad - lat1_rad
        dlng = lng2_rad - lng1_rad

        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlng/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))

        distance = R * c
        return round(distance, 2)

    def add_distance_to_restaurants(self, restaurants: List[Dict], user_location: Optional[Dict] = None) -> List[Dict]:
        """
        Add distance information to restaurant data
        """
        if not user_location or not user_location.get('latitude') or not user_location.get('longitude'):
            return restaurants

        user_lat = user_location['latitude']
        user_lng = user_location['longitude']

        for restaurant in restaurants:
            if restaurant.get('latitude') and restaurant.get('longitude'):
                distance = self.calculate_distance(
                    user_lat, user_lng,
                    restaurant['latitude'], restaurant['longitude']
                )
                restaurant['distance_km'] = distance
                restaurant['distance_text'] = f"{distance} km away"

        # Sort by distance (closest first)
        restaurants.sort(key=lambda x: x.get('distance_km', float('inf')))
        return restaurants

    def _handle_location_question(self, query: str, user_location: dict = None) -> str:
        """Handle simple location questions like 'Where is De Flambe?'"""
        query_lower = query.lower()

        # Restaurant location mappings with verified Islamabad coordinates
        restaurant_data = {
            'de flambe': {'location': 'Blue Area, Islamabad', 'lat': 33.7023, 'lng': 73.0598},
            'wild wings': {'location': 'F-8 Markaz, Islamabad', 'lat': 33.7012, 'lng': 73.0634},
            'savour foods': {'location': 'G-7 Blue Area, Islamabad', 'lat': 33.7089, 'lng': 73.0571},
            'chaaye khana': {'location': 'F-6 Markaz, Islamabad', 'lat': 33.7267, 'lng': 73.0801},
            'khoka khola': {'location': 'F-6/1 Blue Area, Islamabad', 'lat': 33.7065, 'lng': 73.0548},
            'bundu khan': {'location': 'I-8 Markaz, Islamabad', 'lat': 33.6654, 'lng': 73.0789},
            'ranchers': {'location': 'F-11 Markaz, Islamabad', 'lat': 33.7156, 'lng': 73.0498},
            'cheezious': {'location': 'F-10 Markaz, Islamabad', 'lat': 33.6998, 'lng': 73.0621},
            'the burger co': {'location': 'F-11 Markaz, Islamabad', 'lat': 33.7156, 'lng': 73.0498},
            'rewayat': {'location': 'F-7 Markaz, Islamabad', 'lat': 33.7294, 'lng': 73.0654},
            'haleem ghar': {'location': 'Blue Area, Islamabad', 'lat': 33.7081, 'lng': 73.0559},
            'salt\'n pepper': {'location': 'F-6 Blue Area, Islamabad', 'lat': 33.7077, 'lng': 73.0563},
            'saltn pepper': {'location': 'F-6 Blue Area, Islamabad', 'lat': 33.7077, 'lng': 73.0563},  # Alternative spelling
            'the carnivore': {'location': 'F-7, Islamabad', 'lat': 33.7245, 'lng': 73.0612},
            'zamana restaurant': {'location': 'Blue Area, Islamabad', 'lat': 33.6987, 'lng': 73.0543},
            'el momento': {'location': 'F-7, Islamabad', 'lat': 33.7134, 'lng': 73.0456},
            'howdy': {'location': 'F-7, Islamabad', 'lat': 33.7201, 'lng': 73.0512}
        }

        # Handle location questions - comprehensive pattern matching
        location_patterns = [
            'where is', 'where are', 'location of', 'where the', 'where\'s',
            'where can i find', 'address of', 'located at', 'find location',
            'where', 'location'  # More general patterns
        ]

        # Check if this is a location question
        is_location_question = any(pattern in query_lower for pattern in location_patterns)

        if is_location_question:
            found_restaurants = []

            # Check each restaurant name in the query
            for restaurant, data in restaurant_data.items():
                # More flexible matching - check for partial names too
                restaurant_variations = [
                    restaurant,
                    restaurant.replace("'", ""),  # Handle apostrophes
                    restaurant.replace(" ", ""),   # Handle spaces
                    restaurant.replace("'n", " n"), # Handle "salt'n pepper" vs "salt n pepper"
                    restaurant.replace("the ", ""), # Handle "the burger co" vs "burger co"
                ]

                # Also check if the restaurant name appears in the query (more flexible)
                restaurant_found = any(var in query_lower for var in restaurant_variations)

                # Additional check for partial matches
                if not restaurant_found:
                    # Check if key words from restaurant name appear
                    restaurant_words = restaurant.split()
                    if len(restaurant_words) > 1:
                        # For multi-word names, check if most words appear
                        word_matches = sum(1 for word in restaurant_words if word in query_lower)
                        if word_matches >= len(restaurant_words) - 1:  # Allow missing one word
                            restaurant_found = True

                if restaurant_found:
                    found_restaurants.append(f"{restaurant.title()} is located in {data['location']}")

            # If we found any restaurants and this looks like a pure location question, return the locations
            if found_restaurants:
                # Check if it's a pure location question (no food-related words)
                food_words = ['burger', 'food', 'eat', 'suggest', 'recommend', 'menu', 'dish', 'meal']
                is_pure_location = not any(food_word in query_lower for food_word in food_words)

                if is_pure_location:
                    if len(found_restaurants) == 1:
                        return found_restaurants[0] + "."
                    else:
                        return " ".join(found_restaurants) + "."

        # Handle distance questions
        distance_patterns = ['how far', 'distance', 'how close', 'how many km', 'miles away']

        if any(pattern in query_lower for pattern in distance_patterns):
            found_restaurants = []
            for restaurant, data in restaurant_data.items():
                if restaurant in query_lower:
                    if user_location and 'latitude' in user_location and 'longitude' in user_location:
                        # Check if user is reasonably close to Islamabad (center: 33.6844, 73.0479)
                        islamabad_center_lat, islamabad_center_lng = 33.6844, 73.0479
                        distance_from_islamabad = self._calculate_distance(
                            user_location['latitude'], user_location['longitude'],
                            islamabad_center_lat, islamabad_center_lng
                        )

                        # If user is more than 100km from Islamabad, use approximate distances
                        if distance_from_islamabad > 100:
                            # Provide approximate distances within Islamabad
                            approximate_distances = {
                                'de flambe': 5, 'wild wings': 8, 'savour foods': 4, 'chaaye khana': 6,
                                'khoka khola': 4, 'bundu khan': 12, 'ranchers': 10, 'cheezious': 9,
                                'the burger co': 10, 'rewayat': 7, 'haleem ghar': 4, 'salt\'n pepper': 4,
                                'saltn pepper': 4, 'the carnivore': 7, 'zamana restaurant': 5, 'el momento': 8, 'howdy': 7
                            }
                            approx_dist = approximate_distances.get(restaurant, 6)
                            found_restaurants.append(f"{restaurant.title()} is approximately {approx_dist} km away (estimated within Islamabad). It's located in {data['location']}")
                        else:
                            # Calculate actual distance
                            distance = self._calculate_distance(
                                user_location['latitude'], user_location['longitude'],
                                data['lat'], data['lng']
                            )
                            found_restaurants.append(f"{restaurant.title()} is approximately {distance:.1f} km away from your location. It's located in {data['location']}")
                    else:
                        found_restaurants.append(f"I need your location to calculate the distance to {restaurant.title()}. {restaurant.title()} is located in {data['location']}")

            if found_restaurants:
                if len(found_restaurants) == 1:
                    return found_restaurants[0] + ". You can use the map feature to get directions and see the exact distance from your current location."
                else:
                    return " ".join(found_restaurants) + ". You can use the map feature for precise directions."

        return None

    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two points using Haversine formula"""
        import math

        # Convert latitude and longitude from degrees to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # Radius of earth in kilometers
        r = 6371

        return c * r

    def _handle_general_question(self, query: str) -> str:
        """Handle general questions about RotiShoti and casual conversation"""
        query_lower = query.lower()

        # RotiShoti specific questions
        if 'who made rotishoti' in query_lower or 'who created rotishoti' in query_lower or 'founder of rotishoti' in query_lower:
            return "RotiShoti was created by Masab Farooque, a passionate food enthusiast who wanted to help people discover amazing food in Pakistan using AI technology."

        if 'what is rotishoti' in query_lower or 'about rotishoti' in query_lower:
            return "RotiShoti is an AI-powered food discovery platform that helps you find the best restaurants and dishes in Pakistan. It uses advanced AI to provide personalized recommendations based on your taste preferences, budget, and location."

        if 'how does rotishoti work' in query_lower:
            return "RotiShoti uses AI to analyze your preferences, budget, and location to recommend the perfect restaurants and dishes for you. Just tell me what you're craving, your budget, or ask about specific restaurants!"

        # Casual conversation patterns that shouldn't trigger food recommendations
        casual_patterns = [
            'hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening',
            'how are you', 'what\'s up', 'thanks', 'thank you', 'bye', 'goodbye',
            'ok', 'okay', 'alright', 'cool', 'nice', 'great', 'awesome'
        ]

        if any(pattern in query_lower for pattern in casual_patterns) and len(query.split()) <= 3:
            return "Hello! I'm here to help you discover amazing food in Islamabad. You can ask me about restaurants, specific dishes, locations, or your budget preferences. What would you like to know?"

        return None

    async def process_agentic_chat(self, request) -> Dict[str, Any]:
        """
        Enhanced Agentic chat processing with intelligent query understanding
        """
        try:
            # Intelligent query preprocessing
            query_intent = self._analyze_query_intent(request.user_query.lower())
            logger.info(f"Query intent detected: {query_intent}")

            # Check for simple location questions first
            user_location = getattr(request, 'user_location', None)
            location_response = self._handle_location_question(request.user_query, user_location)
            if location_response:
                return {
                    "response": location_response,
                    "restaurants": [],
                    "confidence": 95,
                    "fact_checked": True,
                    "accuracy_score": 95,
                    "primary_model": "location-handler",
                    "query_intent": query_intent
                }

            # Check for general RotiShoti questions
            general_response = self._handle_general_question(request.user_query)
            if general_response:
                return {
                    "response": general_response,
                    "restaurants": [],
                    "confidence": 90,
                    "fact_checked": True,
                    "accuracy_score": 90,
                    "primary_model": "general-handler",
                    "query_intent": query_intent
                }

            # Step 1: Extract budget and get restaurant data
            budget_info = chat_service._extract_budget_from_query_and_profile(
                request.user_query,
                request.user_profile
            )

            # Intelligent vector search based on query intent
            try:
                # Adjust search parameters based on intent for optimal results
                search_params = self._get_search_parameters(query_intent, request.user_query)
                logger.info(f"Using search parameters: {search_params}")

                restaurants_data = await chat_service._get_vector_search_results(
                    query=request.user_query,
                    location=request.location or "Islamabad",
                    budget_range=budget_info.get('budget_range')
                )

                # If vector search returns no results, fallback to regular search
                if not restaurants_data or len(restaurants_data) == 0:
                    print("DEBUG: Vector search returned no results, falling back to regular search")
                    restaurants_data = await chat_service._get_filtered_restaurants(
                        location=request.location or "Islamabad",
                        budget_range=budget_info.get('budget_range')
                    )
                else:
                    print(f"DEBUG: Vector search successful - found {len(restaurants_data)} restaurants")
            except Exception as e:
                print(f"DEBUG: Vector search failed: {str(e)}, using regular search")
                restaurants_data = await chat_service._get_filtered_restaurants(
                    location=request.location or "Islamabad",
                    budget_range=budget_info.get('budget_range')
                )

            # Add distance information if user location is provided
            if hasattr(request, 'user_location') and request.user_location:
                restaurants_data = self.add_distance_to_restaurants(restaurants_data, request.user_location)

            # Step 2: Extract restaurant cards first to ensure consistency
            restaurant_cards = chat_service._extract_restaurant_cards(
                restaurants_data,
                budget_info.get("budget_range"),
                request.user_query,
                request.conversation_history
            )

            # Step 3: Convert cards to consistent format for AI
            card_based_data = []
            for card in restaurant_cards:
                restaurant_info = {
                    "name": card.get("name"),
                    "area": card.get("area"),
                    "menu_items": [{
                        "name": card.get("item_name"),
                        "price": card.get("price")
                    }]
                }
                card_based_data.append(restaurant_info)

            # Step 4: Generate primary response using card data
            primary_response = await self._get_primary_response(
                request, budget_info, card_based_data
            )

            # Step 5: Fact-check the response
            fact_check_result = await self._fact_check_response(
                primary_response, card_based_data, request.user_query
            )

            # Step 6: Orchestrate final response
            final_response = await self._orchestrate_final_response(
                primary_response, fact_check_result, card_based_data, budget_info, request, restaurant_cards
            )

            # Return with enhanced metadata
            final_response["restaurants"] = restaurant_cards
            final_response["query_intent"] = query_intent
            final_response["processing_metadata"] = {
                "vector_search_results": len(restaurants_data) if restaurants_data else 0,
                "restaurant_cards_generated": len(restaurant_cards),
                "budget_info": budget_info,
                "user_personality": getattr(request.user_profile, 'food_personality', 'friendly') if request.user_profile else 'friendly'
            }
            return final_response

        except Exception as e:
            logger.error(f"Agentic chat processing failed: {str(e)}")
            return {
                "response": "I'm having trouble processing your request right now. Please try again!",
                "restaurants": [],
                "confidence": 0.0,
                "fact_checked": False
            }

    async def _get_primary_response(self, request, budget_info: Dict, restaurants_data: Dict) -> Dict[str, Any]:
        """
        Enhanced Primary LLM generates intelligent, context-aware responses
        """
        # Build conversation context (optimized for token efficiency)
        conversation_context = ""
        if request.conversation_history:
            for msg in request.conversation_history[-4:]:  # Last 4 messages for better context
                role = "User" if msg.role == "user" else "Assistant"
                # Keep full context for better understanding
                conversation_context += f"{role}: {msg.content}\n"

        # Determine user personality from profile or conversation tone
        user_personality = 'friendly'  # default
        if request.user_profile and hasattr(request.user_profile, 'food_personality'):
            user_personality = request.user_profile.food_personality
        elif conversation_context:
            # Analyze conversation tone
            if any(word in conversation_context.lower() for word in ['amazing', 'awesome', 'love', 'excited']):
                user_personality = 'enthusiastic'
            elif any(word in conversation_context.lower() for word in ['professional', 'business', 'meeting']):
                user_personality = 'professional'

        # Get current time for meal context
        from datetime import datetime
        current_time = datetime.now()
        current_hour = current_time.hour

        # Determine meal time
        if 6 <= current_hour < 11:
            meal_time = "BREAKFAST"
        elif 11 <= current_hour < 16:
            meal_time = "LUNCH"
        elif 16 <= current_hour < 23:
            meal_time = "DINNER"
        else:
            meal_time = "LATE NIGHT"

        # Get personality-specific prompt prefix
        personality_prompt = self.personality_prompts.get(user_personality, self.personality_prompts['friendly'])

        # Analyze query intent for better responses
        query_lower = request.user_query.lower()
        query_intent = self._analyze_query_intent(query_lower)

        # Create intelligent, context-aware prompt
        prompt = f"""{personality_prompt}

USER PROFILE:
- Name: {request.user_profile.name if request.user_profile else 'Food Lover'}
- Budget: {budget_info.get('effective_budget', 'medium')} ({budget_info.get('budget_range', 'flexible budget')})
- Location: {request.location or 'Islamabad'}
- Preferences: {', '.join(request.user_profile.favorite_cuisines) if request.user_profile and request.user_profile.favorite_cuisines else 'Pakistani cuisine'}
- Spice Level: {request.user_profile.spice_level if request.user_profile else 'medium'}
- Dining Style: {', '.join(request.user_profile.dining_style) if request.user_profile and request.user_profile.dining_style else 'casual'}

INTELLIGENT CONTEXT:
- Current Time: {current_time.strftime('%I:%M %p')} ({meal_time})
- Query Intent: {query_intent}
- Conversation Context: {"Continuing conversation" if conversation_context else "New conversation"}

CONVERSATION HISTORY:
{conversation_context}

CURRENT QUERY: "{request.user_query}"

RESTAURANT DATA (Top matches from vector search):
{json.dumps(restaurants_data[:4] if restaurants_data else [], indent=2)}

MEAL TIME AWARENESS:
- BREAKFAST (6 AM - 11 AM): Suggest paratha, halwa puri, nihari, chai, lassi, omelets, breakfast items
- LUNCH (11 AM - 4 PM): Suggest biryani, pulao, karahi, handi, rice dishes, main courses
- DINNER (4 PM - 11 PM): Suggest karahi, BBQ items, grilled food, main courses, family meals
- LATE NIGHT (11 PM - 6 AM): Suggest light items, chai, snacks, fast food
- NEVER suggest breakfast items (French toast, pancakes, omelets) for dinner
- NEVER suggest desserts or snacks as main meal recommendations
- Match food suggestions to appropriate meal times

FOOD CATEGORY UNDERSTANDING:
- "Desi dishes" (Urdu/Hindi) means traditional Pakistani dishes like karahi, handi, biryani, pulao, nehari, haleem, BBQ items
- "Desi chicken" means traditional chicken dishes like chicken karahi, chicken handi, chicken malai boti, chicken biryani
- "BBQ" means grilled items like malai boti, seekh kabab, tikka, reshmi kabab (NOT burgers or steaks)
- "Karahi" specifically means karahi dishes (chicken karahi, mutton karahi)
- "Handi" means traditional handi dishes
- "BBQ" or "grilled" means Pakistani BBQ like malai boti, seekh kabab, tikka
- NEVER suggest Western dishes (steak, burgers, pasta, sandwiches) when user asks for desi/traditional/BBQ food
- When user asks in Urdu/Hindi, focus ONLY on traditional Pakistani dishes
- Prioritize exact category matches from the JSON data over generic suggestions

INTELLIGENT RESPONSE GUIDELINES:
1. CONTEXT AWARENESS: Reference previous conversation naturally if continuing a discussion
2. PERSONALIZATION: Use user's name, preferences, and dietary restrictions in recommendations
3. LOCATION PRECISION: Always mention specific areas (F-7, Blue Area, etc.) and distances when relevant
4. BUDGET INTELLIGENCE: Prioritize options within user's budget, mention if going slightly over with justification
5. MEAL TIME MATCHING: Suggest appropriate foods for current time (breakfast/lunch/dinner)
6. INTENT-BASED RESPONSES (Current Intent: {query_intent}):
   - seeking_recommendations: Provide 3-4 top picks with detailed reasons why they're great
   - budget_conscious: Lead with prices, highlight value, mention "great deal" or "affordable"
   - location_focused: Emphasize proximity, mention travel time, give directions context
   - menu_inquiry: Focus on specific dishes, describe variety, mention popular items
   - comparison_request: Highlight key differences, pros/cons, help user decide
   - taste_preference: Match spice levels, describe flavors, mention heat levels
   - dietary_requirement: Ensure ALL suggestions meet restrictions, mention compliance
   - general_inquiry: Provide balanced overview with variety of options
7. CONVERSATION FLOW: Build on previous messages, acknowledge user's journey
8. ENTHUSIASM MATCHING: Match user's energy level (excited users get excited responses)

GENERAL KNOWLEDGE RESPONSES:
- If asked "Where is [restaurant]?" - Provide ONLY the location/area, be direct and brief
- If asked "Who made RotiShoti?" - "RotiShoti was created by Masab Farooque, a passionate food enthusiast who wanted to help people discover amazing food in Pakistan using AI technology."
- If asked about RotiShoti features - Explain AI-powered recommendations, location-based suggestions, budget-aware filtering
- If asked about team/company - "RotiShoti is developed by Masab Farooque with a focus on Pakistani food culture and AI-powered discovery"
- For general food questions without specific restaurant context - Provide helpful food advice but always try to connect to available restaurants

🚨 CRITICAL ANTI-HALLUCINATION RULES - ZERO TOLERANCE:
1. ONLY mention restaurants that appear in the AVAILABLE RESTAURANTS data above - ABSOLUTELY NO EXCEPTIONS
2. ONLY mention menu items that exist in the provided restaurant data - ABSOLUTELY NO EXCEPTIONS
3. Use EXACT restaurant names, dish names, and prices from the provided data - NO VARIATIONS
4. NEVER mention: Tuscany Courtyard, KFC, McDonald's, Pizza Hut, Domino's, Subway, Burger King, CGC, or ANY restaurant not in the data
5. NEVER make up prices - only use prices shown in the restaurant data
6. NEVER create menu items that don't exist in the data
7. If you can't find what they're looking for, be honest: "I don't have [item] in my current database"
8. Double-check EVERY restaurant name and menu item against the provided data before mentioning it
9. If unsure about ANY restaurant or item, DO NOT mention it - better to say nothing than hallucinate
10. VERIFY each recommendation exists in the provided data before including it in your response

RESPONSE STYLE:
- Quick casual greeting with their name
- PRIORITIZE exact matches for their request from the provided data
- List 2-3 options briefly with exact prices from the data
- One simple closing line
- NO repetition, NO excessive enthusiasm, NO long explanations

CRITICAL: Only use restaurants and items from the provided data above!

Example good response: "Hey Masab! Found some great burgers for you. The Bacon Cheese Burger at The Burger Co is Rs 1290, and there's a Smash Burger at De Flambe for Rs 1490. Both are excellent choices!"

Example for wings: "Hey Masab! Found wings for you! Wild Wings has Traditional Wings (6 Pcs) for Rs 749. Perfect for your craving!"

Example when item exists but outside budget: "Hey Masab! I found Mutton Karahi at Zamana Restaurant for Rs 3390, but it's above your budget. Want to adjust your budget or try something else?"

Example for desi chicken dishes: "Hey Masab! Found some great desi chicken dishes! Rewayat has Famous Chicken Karahi (Half) for Rs 1499, and Haleem Ghar has White Chicken Karahi (Half) for Rs 1100. Perfect traditional flavors!"

Example for BBQ: "Hey Masab! Found some amazing BBQ for you! Salt'n Pepper has Boneless Chicken Malai Boti for Rs 1925, and Rewayat has Royal Malai Boti (6 Pcs) for Rs 1599. Grilled to perfection!"

Example for Urdu desi query: "Hey Masab! Yahan desi chicken dishes hain! Rewayat mein Famous Chicken Karahi (Half) Rs 1499 mein, aur Haleem Ghar mein White Chicken Karahi (Half) Rs 1100 mein. Bilkul traditional taste!"

🔒 FINAL VALIDATION CHECKPOINT - MANDATORY:
Before responding, verify EVERY restaurant and menu item mentioned exists in the AVAILABLE RESTAURANTS data above.
If you cannot find exact matches in the provided data, DO NOT mention them.
NEVER mention: Tuscany Courtyard, KFC, McDonald's, Pizza Hut, or any restaurant not in the data above.
"""

        try:
            # Log token usage for monitoring
            prompt_tokens = len(prompt.split()) * 1.3  # Rough estimate
            logger.info(f"Primary LLM - Estimated input tokens: {int(prompt_tokens)}")

            response = await self.primary_client.chat.completions.create(
                model=self.primary_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=150  # Short, concise responses
            )
            
            return {
                "content": response.choices[0].message.content,
                "model": self.primary_model,
                "confidence": 0.8
            }
        except Exception as e:
            logger.error(f"Primary LLM failed: {str(e)}")
            return {
                "content": "I'm having trouble generating recommendations right now.",
                "model": self.primary_model,
                "confidence": 0.0
            }

    async def _fact_check_response(self, primary_response: Dict, restaurants_data: Dict, user_query: str) -> Dict[str, Any]:
        """
        Fact-checker LLM validates the primary response
        """
        # Build fact-check prompt safely
        fact_check_prompt = "You are a strict fact-checking AI for restaurant recommendations. Your job is to verify accuracy and prevent hallucinations.\n\n"
        fact_check_prompt += f"PRIMARY RESPONSE TO VERIFY:\n{primary_response.get('content', '')}\n\n"
        fact_check_prompt += f"ACTUAL RESTAURANT DATA (REFERENCE):\n{json.dumps(restaurants_data[:3] if restaurants_data else [], indent=2)}\n\n"
        fact_check_prompt += f"USER QUERY: {user_query}\n\n"
        fact_check_prompt += "ENHANCED INTELLIGENT FACT-CHECK GUIDELINES:\n"
        fact_check_prompt += "1. RESTAURANT VERIFICATION: Names must exist in data (case-insensitive, allow minor variations)\n"
        fact_check_prompt += "2. MENU ITEM VERIFICATION: Dish names must exist in restaurant's menu_items (fuzzy matching allowed)\n"
        fact_check_prompt += "3. PRICE ACCURACY: Prices must match exactly (±Rs 10 tolerance for rounding)\n"
        fact_check_prompt += "4. LOCATION ACCURACY: Areas must match restaurant data (F-7, Blue Area, etc.)\n"
        fact_check_prompt += "5. CHAIN RESTAURANT POLICY: Only approve KFC/McDonald's/Pizza Hut if explicitly in database\n"
        fact_check_prompt += "6. CONTEXT INTELLIGENCE: Allow reasonable inferences (beef items for 'steak' queries)\n"
        fact_check_prompt += "7. CONVERSATION FLOW: Approve natural greetings, personality, and contextual references\n"
        fact_check_prompt += "8. BUDGET INTELLIGENCE: Allow 10-15% budget flexibility with clear justification\n"
        fact_check_prompt += "9. CULTURAL CONTEXT: Approve halal mentions, Pakistani food culture references\n"
        fact_check_prompt += "10. MEAL TIME LOGIC: Verify suggestions match appropriate meal times\n"
        fact_check_prompt += "11. USER PERSONALIZATION: Allow references to user name, preferences, conversation history\n"
        fact_check_prompt += "12. SMART CATEGORIZATION: Accept semantic matches (biryani for 'rice dishes')\n"
        fact_check_prompt += "13. AVAILABILITY LOGIC: Check if restaurants/items are marked as available\n\n"
        fact_check_prompt += "INTELLIGENT SCORING GUIDELINES:\n"
        fact_check_prompt += "- 0.95-1.0: Perfect accuracy + excellent personalization + natural conversation flow\n"
        fact_check_prompt += "- 0.85-0.94: All core facts correct + good context awareness + minor style issues\n"
        fact_check_prompt += "- 0.75-0.84: Facts mostly correct + some personalization + acceptable conversation\n"
        fact_check_prompt += "- 0.60-0.74: Some factual issues OR poor context matching but recoverable\n"
        fact_check_prompt += "- 0.40-0.59: Multiple factual errors OR significant hallucinations - needs revision\n"
        fact_check_prompt += "- 0.0-0.39: Major hallucinations, fake restaurants, completely wrong data - REJECT\n\n"
        fact_check_prompt += "BONUS POINTS FOR:\n"
        fact_check_prompt += "- Using user's name naturally\n"
        fact_check_prompt += "- Referencing conversation history appropriately\n"
        fact_check_prompt += "- Matching user's personality/energy level\n"
        fact_check_prompt += "- Providing meal-time appropriate suggestions\n"
        fact_check_prompt += "- Smart budget recommendations with justification\n\n"
        fact_check_prompt += "Respond with JSON format:\n"
        fact_check_prompt += "{\n"
        fact_check_prompt += '    "accuracy_score": 0.0-1.0,\n'
        fact_check_prompt += '    "issues_found": ["specific factual issues only"],\n'
        fact_check_prompt += '    "corrections": ["corrections for factual errors"],\n'
        fact_check_prompt += '    "verified_facts": ["confirmed accurate facts"],\n'
        fact_check_prompt += '    "recommendation": "approve/revise/reject"\n'
        fact_check_prompt += "}"

        try:
            # Log token usage for monitoring
            fact_check_tokens = len(fact_check_prompt.split()) * 1.3
            logger.info(f"Fact-checker LLM - Estimated input tokens: {int(fact_check_tokens)}")

            response = await self.fact_checker_client.chat.completions.create(
                model=self.fact_checker_model,
                messages=[{"role": "user", "content": fact_check_prompt}],
                temperature=0.1,  # Low temperature for factual accuracy
                max_tokens=200  # Sufficient for detailed fact-checking
            )
            
            # Parse JSON response
            fact_check_content = response.choices[0].message.content
            try:
                fact_check_result = json.loads(fact_check_content)
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                fact_check_result = {
                    "accuracy_score": 0.7,
                    "issues_found": [],
                    "corrections": [],
                    "verified_facts": ["Response generated"],
                    "recommendation": "approve"
                }
            
            return fact_check_result
            
        except Exception as e:
            logger.error(f"Fact-checker LLM failed: {str(e)}")
            return {
                "accuracy_score": 0.5,
                "issues_found": ["Fact-checking unavailable"],
                "corrections": [],
                "verified_facts": [],
                "recommendation": "approve"
            }

    async def _orchestrate_final_response(self, primary_response: Dict, fact_check: Dict, restaurants_data: Dict, budget_info: Dict, request, restaurant_cards: list) -> Dict[str, Any]:
        """
        PERFECT orchestrator - ensures 100% accuracy with zero tolerance for errors
        """
        accuracy_score = fact_check.get('accuracy_score', 0.0)
        recommendation = fact_check.get('recommendation', 'reject')
        issues_found = fact_check.get('issues_found', [])
        corrections = fact_check.get('corrections', [])

        # IMPROVED QUALITY CONTROL - More reasonable confidence scoring
        if recommendation == 'approve' and accuracy_score >= 0.7:
            # Good response - use as is
            final_response = primary_response.get('content', '')
            confidence = max(0.90, accuracy_score)  # High confidence for approved responses
            fact_checked = True

        elif recommendation == 'approve' and accuracy_score >= 0.5:
            # Approved but lower accuracy - still good
            final_response = primary_response.get('content', '')
            confidence = max(0.80, accuracy_score)  # Good confidence for approved responses
            fact_checked = True

        elif recommendation == 'revise' and accuracy_score >= 0.4:
            # Decent response but could be better - still use it
            final_response = primary_response.get('content', '')
            confidence = max(0.70, accuracy_score)  # Reasonable confidence
            fact_checked = True

        elif accuracy_score >= 0.2:
            # Low accuracy but might still be useful - use with moderate confidence
            final_response = primary_response.get('content', '')
            confidence = max(0.60, accuracy_score)
            fact_checked = True

        else:
            # Very low confidence or major issues - generate safe response
            logger.warning(f"Response rejected: accuracy={accuracy_score}, recommendation={recommendation}")
            logger.warning(f"Issues found: {issues_found}")

            final_response = self._generate_safe_fallback_response(restaurants_data, request.user_query)
            confidence = 0.85  # Good confidence in safe response
            fact_checked = True

        return {
            "response": final_response,
            "restaurants": restaurant_cards,  # Use the cards passed in
            "confidence": confidence,
            "fact_checked": fact_checked,
            "accuracy_score": accuracy_score,
            "primary_model": primary_response.get('model'),
            "fact_checker_used": True
        }

    def _generate_safe_response(self, restaurants_data: list, request, budget_info: Dict) -> str:
        """
        Generate a safe, data-only response when primary response fails fact-check
        """
        user_name = request.user_profile.name if request.user_profile else 'Food Lover'

        if not restaurants_data:
            return f"Hi {user_name}! I don't have any restaurants in my database that match your criteria right now. Please try a different search or check back later."

        # Create response using only verified data
        response = f"Hi {user_name}! Based on your query and budget, here are the verified options I found:\n\n"

        for i, restaurant in enumerate(restaurants_data[:2], 1):
            name = restaurant.get('name', 'Restaurant')
            area = restaurant.get('area', 'Location')
            menu_items = restaurant.get('menu_items', [])

            if menu_items:
                item = menu_items[0]  # First item
                item_name = item.get('name', 'Item')
                price = item.get('price', 'Price')

                response += f"{i}. **{name}** in {area}\n"
                response += f"   • {item_name} - Rs {price}\n\n"

        response += "All options are halal and within your specified criteria. Enjoy your meal! 🍽️"
        return response

    def _generate_safe_fallback_response(self, restaurants_data: List[Dict], user_query: str) -> str:
        """
        Generate a completely safe response using only verified data
        """
        if not restaurants_data:
            return "Sorry, I couldn't find any restaurants matching your request. Please try a different search."

        # Extract first few restaurants with their actual menu items
        safe_items = []
        for restaurant in restaurants_data[:3]:
            name = restaurant.get('name', 'Restaurant')
            area = restaurant.get('area', 'Islamabad')
            menu_items = restaurant.get('menu_items', [])

            if menu_items:
                item = menu_items[0]  # Take first menu item
                item_name = item.get('name', 'Item')
                price = item.get('price', 0)
                safe_items.append(f"{item_name} at {name} for Rs {price}")

        if safe_items:
            if len(safe_items) == 1:
                return f"Hey! I found {safe_items[0]}. Check it out!"
            else:
                return f"Hey! Found some options: {', '.join(safe_items)}. All verified from our database!"

        return "Sorry, I couldn't find specific items matching your request. Please try a different search."

# Global instance
agentic_chat_service = AgenticChatService()
