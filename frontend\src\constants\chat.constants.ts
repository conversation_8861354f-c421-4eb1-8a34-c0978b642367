/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * 
 * @fileoverview Chat-related constants and configuration
 * @version 1.0.0
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 RotiShoti Technologies
 */

/**
 * API Configuration
 */
export const CHAT_API = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000',
  ENDPOINTS: {
    CHAT: '/api/v1/chat/chat',
    AUTH: '/api/v1/auth',
  },
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
} as const;

/**
 * Chat UI Configuration
 */
export const CHAT_CONFIG = {
  MAX_MESSAGES: 100,
  MAX_MESSAGE_LENGTH: 1000,
  TYPING_DELAY: 1000,
  AUTO_SCROLL_DELAY: 100,
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
} as const;

/**
 * Food-themed color palette
 */
export const FOOD_COLORS = {
  // Primary brand colors
  PRIMARY: {
    orange: '#FF6B35',
    red: '#E63946',
    yellow: '#FFB627',
    warmOrange: '#FF8500',
  },
  
  // Food-inspired colors
  FOOD: {
    spice: '#D2691E',
    curry: '#CC9900',
    mint: '#98FB98',
    tomato: '#FF6347',
    olive: '#808000',
    cream: '#FFFDD0',
    chocolate: '#7B3F00',
    coffee: '#6F4E37',
  },
  
  // UI colors
  UI: {
    background: '#FAFAFA',
    surface: '#FFFFFF',
    border: '#E5E7EB',
    text: {
      primary: '#1F2937',
      secondary: '#6B7280',
      muted: '#9CA3AF',
    },
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
  },
  
  // Gradients
  GRADIENTS: {
    primary: 'linear-gradient(135deg, #FF6B35 0%, #E63946 100%)',
    warm: 'linear-gradient(135deg, #FFB627 0%, #FF8500 100%)',
    food: 'linear-gradient(135deg, #FF6347 0%, #D2691E 100%)',
    subtle: 'linear-gradient(135deg, #FFFDD0 0%, #F7FAFC 100%)',
  },
} as const;

/**
 * Typography configuration
 */
export const TYPOGRAPHY = {
  FONTS: {
    primary: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    heading: '"Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    mono: '"JetBrains Mono", "Fira Code", Consolas, monospace',
  },
  
  SIZES: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
  },
  
  WEIGHTS: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
} as const;

/**
 * Animation configurations
 */
export const ANIMATIONS = {
  EASING: {
    ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  
  DURATION: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },
  
  KEYFRAMES: {
    fadeIn: {
      from: { opacity: 0 },
      to: { opacity: 1 },
    },
    slideUp: {
      from: { transform: 'translateY(10px)', opacity: 0 },
      to: { transform: 'translateY(0)', opacity: 1 },
    },
    bounce: {
      '0%, 20%, 53%, 80%, 100%': { transform: 'translate3d(0,0,0)' },
      '40%, 43%': { transform: 'translate3d(0, -30px, 0)' },
      '70%': { transform: 'translate3d(0, -15px, 0)' },
      '90%': { transform: 'translate3d(0, -4px, 0)' },
    },
  },
} as const;

/**
 * Chat message templates and responses
 */
export const CHAT_TEMPLATES = {
  WELCOME_MESSAGES: [
    "Welcome to RotiShoti! I'm here to help you discover amazing Pakistani cuisine. What are you craving today?",
    "Assalam-o-Alaikum! Ready to explore some delicious food options? Tell me what you're in the mood for!",
    "Hello! I'm your personal food discovery assistant. What kind of culinary adventure shall we embark on today?",
  ],
  
  ERROR_MESSAGES: {
    NETWORK: "I'm having trouble connecting right now. Please check your internet and try again.",
    SERVER: "Our kitchen is a bit busy right now. Please try again in a moment.",
    VALIDATION: "I didn't quite understand that. Could you please rephrase your request?",
    TIMEOUT: "That's taking longer than expected. Let me try again for you.",
  },
  
  LOADING_MESSAGES: [
    "Searching for delicious options...",
    "Exploring the best restaurants...",
    "Finding perfect matches for you...",
    "Cooking up some recommendations...",
  ],
} as const;

/**
 * Feature flags for progressive enhancement
 */
export const FEATURE_FLAGS = {
  ENABLE_VOICE_INPUT: false,
  ENABLE_IMAGE_UPLOAD: false,
  ENABLE_LOCATION_DETECTION: true,
  ENABLE_PUSH_NOTIFICATIONS: false,
  ENABLE_OFFLINE_MODE: false,
  ENABLE_ANALYTICS: true,
  ENABLE_A_B_TESTING: false,
} as const;

/**
 * Performance and optimization settings
 */
export const PERFORMANCE = {
  VIRTUAL_SCROLLING_THRESHOLD: 50,
  IMAGE_LAZY_LOADING: true,
  DEBOUNCE_SEARCH: 300,
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  MAX_CONCURRENT_REQUESTS: 3,
} as const;

/**
 * Accessibility configuration
 */
export const A11Y = {
  FOCUS_VISIBLE_OUTLINE: '2px solid #FF6B35',
  HIGH_CONTRAST_MODE: false,
  REDUCED_MOTION_SUPPORT: true,
  SCREEN_READER_ANNOUNCEMENTS: true,
  KEYBOARD_NAVIGATION: true,
} as const;

/**
 * Local storage keys
 */
export const STORAGE_KEYS = {
  CHAT_HISTORY: 'rotishoti-chat-storage',
  USER_PREFERENCES: 'rotishoti-user-preferences',
  THEME_PREFERENCE: 'rotishoti-theme',
  LANGUAGE_PREFERENCE: 'rotishoti-language',
} as const;
