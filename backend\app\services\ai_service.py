import os
from typing import List
from groq import Groq
from transformers import AutoTokenizer, AutoModel
import torch
import logging

logger = logging.getLogger(__name__)

class AIService:
    def __init__(self):
        self.groq_client = Groq(api_key=os.getenv("GROQ_API_KEY"))
        self.embedding_model = None
        self.tokenizer = None
        # This method is called automatically when an AIService object is created
        self._load_embedding_model() 

    def _load_embedding_model(self):
        """Loads the SentenceTransformers model."""
        try:
            model_name = "sentence-transformers/all-MiniLM-L6-v2"
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.embedding_model = AutoModel.from_pretrained(model_name)
            logger.info(f"Loaded embedding model: {model_name}")
        except Exception as e:
            logger.error(f"Error loading embedding model: {e}")
            self.embedding_model = None # Ensure it's None if loading fails
            # Optionally raise an error here if the model is critical
            # raise RuntimeError(f"Failed to load embedding model: {e}")

    def _mean_pooling(self, model_output, attention_mask):
        token_embeddings = model_output[0] # First element of model_output contains all token embeddings
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
        return torch.sum(token_embeddings * input_mask_expanded, 1) / torch.clamp(input_mask_expanded.sum(1), min=1e-9)

    async def generate_embedding(self, text: str) -> List[float]:
        """Generates an embedding vector for the given text."""
        if not self.embedding_model or not self.tokenizer:
            logger.error("Embedding model not loaded. Cannot generate embedding.")
            # Depending on severity, you might want to raise an error or return a dummy
            raise RuntimeError("Embedding model not loaded or initialized correctly. Cannot generate embedding.")

        try:
            # Ensure input is a string
            if not isinstance(text, str):
                text = str(text)

            encoded_input = self.tokenizer(text, padding=True, truncation=True, return_tensors='pt')
            with torch.no_grad():
                model_output = self.embedding_model(**encoded_input)
            
            # Perform pooling. In this case, mean pooling.
            sentence_embeddings = self._mean_pooling(model_output, encoded_input['attention_mask'])
            
            # Normalize embeddings
            sentence_embeddings = torch.nn.functional.normalize(sentence_embeddings, p=2, dim=1)
            
            return sentence_embeddings[0].tolist()
        except Exception as e:
            logger.error(f"Error generating embedding for text '{text[:50]}...': {e}")
            raise # Re-raise to propagate the error

    async def get_llm_response(self, prompt: str) -> str:
        """Gets a response from the Groq API."""
        try:
            chat_completion = self.groq_client.chat.completions.create(
                messages=[
                    {
                        "role": "user",
                        "content": prompt,
                    }
                ],
                model="llama-3.1-8b-instant", # Or your preferred LLaMA model
                temperature=0.3,  # Lower temperature for more consistent responses
                max_tokens=1500   # Increased for complete restaurant card responses
            )
            return chat_completion.choices[0].message.content
        except Exception as e:
            logger.error(f"Error calling Groq API with prompt: '{prompt[:100]}...': {e}")
            return "Sorry, I am unable to generate a recommendation at this moment."

# Create a global instance of AIService that can be imported by other modules
ai_service = AIService()