/**
 * RotiShoti - AI-Powered Food Discovery Platform
 * Authors: <AUTHORS>
 * Component: Professional Button component with animations and variants
 */

import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { COMPONENT_ANIMATIONS } from '@/lib/animations';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  rounded?: boolean;
  gradient?: boolean;
  glow?: boolean;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = 'primary',
      size = 'md',
      loading = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      rounded = false,
      gradient = false,
      glow = false,
      disabled,
      children,
      ...props
    },
    ref
  ) => {
    const baseClasses = [
      // Base styles
      'inline-flex items-center justify-center font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
      
      // Size variants
      size === 'sm' && 'px-3 py-1.5 text-sm gap-1.5',
      size === 'md' && 'px-4 py-2 text-sm gap-2',
      size === 'lg' && 'px-6 py-3 text-base gap-2',
      size === 'xl' && 'px-8 py-4 text-lg gap-3',
      
      // Width
      fullWidth && 'w-full',
      
      // Border radius
      rounded ? 'rounded-full' : 'rounded-lg',
    ];

    const variantClasses = {
      primary: [
        gradient
          ? 'bg-gradient-to-r from-green-500 to-green-600 text-white'
          : 'bg-green-500 text-white',
        'hover:bg-green-600 hover:shadow-lg',
        'focus:ring-green-500',
        'active:bg-green-700',
        glow && 'hover:shadow-green-500/25',
      ],
      secondary: [
        gradient
          ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white'
          : 'bg-orange-500 text-white',
        'hover:bg-orange-600 hover:shadow-lg',
        'focus:ring-orange-500',
        'active:bg-orange-700',
        glow && 'hover:shadow-orange-500/25',
      ],
      outline: [
        'border-2 border-green-500 text-green-500 bg-transparent',
        'hover:bg-green-50 hover:border-green-600',
        'focus:ring-green-500',
        'active:bg-green-100',
      ],
      ghost: [
        'text-green-600 bg-transparent',
        'hover:bg-green-50 hover:text-green-700',
        'focus:ring-green-500',
        'active:bg-green-100',
      ],
      danger: [
        'bg-red-500 text-white',
        'hover:bg-red-600 hover:shadow-lg',
        'focus:ring-red-500',
        'active:bg-red-700',
        glow && 'hover:shadow-red-500/25',
      ],
    };

    const classes = cn(
      baseClasses,
      variantClasses[variant],
      glow && 'hover:shadow-xl',
      className
    );

    return (
      <button
        ref={ref}
        className={classes}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        
        {!loading && leftIcon && (
          <span className="flex-shrink-0">{leftIcon}</span>
        )}
        
        <span className="flex-1">{children}</span>
        
        {!loading && rightIcon && (
          <span className="flex-shrink-0">{rightIcon}</span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

// Button variants for specific use cases
export const PrimaryButton = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'variant'>>(
  (props, ref) => <Button ref={ref} variant="primary" {...props} />
);

export const SecondaryButton = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'variant'>>(
  (props, ref) => <Button ref={ref} variant="secondary" {...props} />
);

export const OutlineButton = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'variant'>>(
  (props, ref) => <Button ref={ref} variant="outline" {...props} />
);

export const GhostButton = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'variant'>>(
  (props, ref) => <Button ref={ref} variant="ghost" {...props} />
);

export const DangerButton = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'variant'>>(
  (props, ref) => <Button ref={ref} variant="danger" {...props} />
);

// Animated button with Pakistani flair
export const PakistaniButton = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'variant' | 'gradient' | 'glow'>>(
  (props, ref) => (
    <Button
      ref={ref}
      variant="primary"
      gradient
      glow
      className="bg-gradient-to-r from-green-600 via-green-500 to-green-600 hover:from-green-700 hover:via-green-600 hover:to-green-700 transform hover:scale-105 transition-all duration-300"
      {...props}
    />
  )
);

// Icon button
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, className, size = 'md', ...props }, ref) => {
    const sizeClasses = {
      sm: 'p-1.5',
      md: 'p-2',
      lg: 'p-3',
      xl: 'p-4',
    };

    return (
      <Button
        ref={ref}
        size={size}
        className={cn('aspect-square', sizeClasses[size], className)}
        {...props}
      >
        {icon}
      </Button>
    );
  }
);

// Floating Action Button
export const FloatingActionButton = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'rounded' | 'size'>>(
  (props, ref) => (
    <Button
      ref={ref}
      size="lg"
      rounded
      glow
      className="fixed bottom-6 right-6 z-50 shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300"
      {...props}
    />
  )
);

PrimaryButton.displayName = 'PrimaryButton';
SecondaryButton.displayName = 'SecondaryButton';
OutlineButton.displayName = 'OutlineButton';
GhostButton.displayName = 'GhostButton';
DangerButton.displayName = 'DangerButton';
PakistaniButton.displayName = 'PakistaniButton';
IconButton.displayName = 'IconButton';
FloatingActionButton.displayName = 'FloatingActionButton';

export default Button;
