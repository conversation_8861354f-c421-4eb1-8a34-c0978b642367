import { IChatMessage, IRestaurantCard, IChatRequest, IChatResponse } from '@/types/chat.types';
import { TextProcessingService } from './textProcessingService';

export class ChatService {
  private static readonly API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
  private static readonly CHAT_ENDPOINT = '/api/v1/chat/chat';

  static async sendMessage(request: IChatRequest): Promise<IChatResponse> {
    const response = await fetch(`${this.API_BASE_URL}${this.CHAT_ENDPOINT}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  static validateMessage(content: string): { isValid: boolean; error?: string } {
    if (!content || content.trim().length === 0) {
      return { isValid: false, error: 'Message cannot be empty' };
    }
    if (content.length > 1000) {
      return { isValid: false, error: 'Message too long' };
    }
    return { isValid: true };
  }

  static generateMessageId(): string {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 9);
    return `msg_${timestamp}_${randomStr}`;
  }

  static formatMessageTime(timestamp: Date | string | number): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  static buildChatRequest(
    userQuery: string,
    userProfile: any,
    conversationHistory: IChatMessage[]
  ): IChatRequest {
    return {
      user_query: userQuery,
      location: 'Islamabad',
      budget: this.determineBudgetCategory(userProfile),
      user_profile: {
        name: userProfile?.name || 'User',
        food_personality: userProfile?.foodPersonality || 'adventurous',
        favorite_cuisines: userProfile?.preferences?.favoriteCuisines || ['Pakistani'],
        spice_level: userProfile?.preferences?.spiceLevel || 'medium',
        dining_style: userProfile?.preferences?.diningStyle || ['casual'],
        taste_profile: userProfile?.tasteProfile || {},
        budget_range: userProfile?.preferences?.budgetRange || [500, 2500]
      },
      conversation_history: conversationHistory.slice(-5).map(msg => ({
        role: msg.type === 'user' ? 'user' : 'assistant',
        content: msg.content
      }))
    };
  }

  static processAIResponse(response: string): string {
    return TextProcessingService.cleanAIResponse(response);
  }

  static extractRecommendationSummary(response: string): string {
    return TextProcessingService.extractRecommendationSummary(response);
  }

  private static determineBudgetCategory(userProfile: any): string {
    const budgetRange = userProfile?.preferences?.budgetRange;
    if (!budgetRange) return 'medium';

    const maxBudget = budgetRange[1];
    if (maxBudget > 2000) return 'high';
    if (maxBudget > 1000) return 'medium';
    return 'low';
  }
}
