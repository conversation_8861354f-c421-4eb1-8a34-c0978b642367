{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.50.3", "@types/leaflet": "^1.9.20", "axios": "^1.10.0", "clsx": "^2.1.1", "framer-motion": "^11.0.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.400.0", "next": "15.3.5", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "react-simple-captcha": "^9.3.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}