'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { RestaurantProfile } from '@/components/restaurant/RestaurantProfile';
import { UniversalMap } from '@/components/maps/UniversalMap';

interface Restaurant {
  id: string;
  name: string;
  cuisine_types: string[];
  area: string;
  address: string;
  rating_overall: number;
  price_range_min: number;
  price_range_max: number;
  image_url?: string;
  features: string[];
  latitude: number;
  longitude: number;
}

const RestaurantsPage: React.FC = () => {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRestaurant, setSelectedRestaurant] = useState<Restaurant | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredRestaurants, setFilteredRestaurants] = useState<Restaurant[]>([]);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get search query from URL parameters
    const urlQuery = searchParams.get('q');
    if (urlQuery) {
      setSearchQuery(urlQuery);
      fetchRestaurants(urlQuery);
    } else {
      fetchRestaurants();
    }
  }, [searchParams]);

  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = restaurants.filter(restaurant =>
        restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        restaurant.cuisine_types.some(cuisine => 
          cuisine.toLowerCase().includes(searchQuery.toLowerCase())
        ) ||
        restaurant.area.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredRestaurants(filtered);
    } else {
      setFilteredRestaurants(restaurants);
    }
  }, [searchQuery, restaurants]);

  const fetchRestaurants = async (query?: string) => {
    try {
      let url = 'http://localhost:8000/api/v1/restaurants';
      if (query) {
        url = `http://localhost:8000/api/v1/restaurants/search?q=${encodeURIComponent(query)}`;
      }

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setRestaurants(data);
        setFilteredRestaurants(data);
      }
    } catch (error) {
      console.error('Error fetching restaurants:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRestaurantImage = (name: string) => {
    const imageIds = [
      'UxRhrU8fPHQ', 'ZuIDLSz3XLg', 'N_Y88TWmGwA', 'jpkfc5_d-DI',
      'lP5MCM6nZ5A', 'MQUqbmszGGM', 'dphM2U1xq0U', 'IGfIGP5ONV0'
    ];
    const randomId = imageIds[Math.abs(name.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % imageIds.length];
    return `https://images.unsplash.com/${randomId}?w=400&h=250&fit=crop`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600 text-lg">Loading delicious restaurants...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent mb-4">
            Discover Amazing Restaurants
          </h1>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Explore the best dining experiences in Islamabad. From traditional Pakistani cuisine to international flavors.
          </p>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search restaurants, cuisines, or areas..."
              className="w-full h-14 pl-14 pr-6 rounded-2xl border-2 border-orange-200 focus:border-orange-400 focus:outline-none bg-white text-gray-800 placeholder-gray-500 text-lg shadow-lg"
            />
            <div className="absolute left-5 top-1/2 transform -translate-y-1/2">
              <svg className="w-6 h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-8">
          <p className="text-gray-600 text-lg">
            Found <span className="font-semibold text-orange-600">{filteredRestaurants.length}</span> restaurants
          </p>
        </div>

        {/* Restaurant Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredRestaurants.map((restaurant) => (
            <div
              key={restaurant.id}
              className="bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 hover:scale-105 cursor-pointer"
              onClick={() => setSelectedRestaurant(restaurant)}
            >
              <div className="relative h-48">
                <img
                  src={restaurant.image_url || getRestaurantImage(restaurant.name)}
                  alt={restaurant.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-4 right-4 bg-white bg-opacity-90 rounded-full px-3 py-1">
                  <div className="flex items-center gap-1">
                    <span className="text-yellow-500">⭐</span>
                    <span className="font-semibold text-gray-800">{restaurant.rating_overall}/5</span>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">{restaurant.name}</h3>
                <p className="text-gray-600 mb-3">📍 {restaurant.area}</p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {restaurant.cuisine_types.slice(0, 2).map((cuisine, index) => (
                    <span
                      key={index}
                      className="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm font-medium"
                    >
                      {cuisine}
                    </span>
                  ))}
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-gray-700">
                    <span className="font-semibold">Rs {restaurant.price_range_min} - Rs {restaurant.price_range_max}</span>
                  </div>
                  <button className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-xl hover:from-red-600 hover:to-orange-600 transition-all duration-200 font-medium">
                    View Details
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredRestaurants.length === 0 && !loading && (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">No restaurants found</h3>
            <p className="text-gray-600">Try adjusting your search criteria</p>
          </div>
        )}
      </main>

      <Footer />

      {/* Restaurant Profile Modal */}
      {selectedRestaurant && (
        <RestaurantProfile
          restaurant={selectedRestaurant}
          onClose={() => setSelectedRestaurant(null)}
        />
      )}
    </div>
  );
};

export default RestaurantsPage;
