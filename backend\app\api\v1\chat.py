"""
RotiShoti - AI-Powered Food Discovery Platform
Author: <PERSON><PERSON><PERSON> endpoint - Thin controller layer for routing only
"""

from fastapi import APIRouter, HTTPException
from app.schemas.chat_schemas import ChatRequest, ChatResponse
from app.services.chat_service import chat_service
from app.services.agentic_chat_service import agentic_chat_service

router = APIRouter()

@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai(request: ChatRequest) -> ChatResponse:
    """
    Agentic chat endpoint - uses dual LLM system with fact-checking, fallback to simple chat
    """
    try:
        result = await agentic_chat_service.process_agentic_chat(request)
        return ChatResponse(**result)
    except Exception as e:
        print(f"Agentic chat failed, falling back to simple chat: {str(e)}")
        # Fallback to simple chat service
        try:
            result = await chat_service.process_chat_request(request)
            return ChatResponse(**result)
        except Exception as fallback_error:
            raise HTTPException(status_code=500, detail=f"Both agentic and simple chat failed: {str(fallback_error)}")

@router.post("/chat/simple", response_model=ChatResponse)
async def simple_chat(request: ChatRequest) -> ChatResponse:
    """
    Simple chat endpoint - single LLM (fallback)
    """
    try:
        result = await chat_service.process_chat_request(request)
        return ChatResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Simple chat processing failed: {str(e)}")
