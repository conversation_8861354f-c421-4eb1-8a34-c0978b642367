# app/api/v1/auth.py
from typing import Any, Dict
from fastapi import APIRouter, Depends, Body
from fastapi.security import OAuth2PasswordRequestForm
from app.schemas.auth_schemas import UserRegister, Token, UserProfile
from app.core.dependencies import get_current_user
from app.services.auth_service import auth_service

router = APIRouter()

@router.post("/register", response_model=UserProfile)
async def register_user(user_in: UserRegister):
    return await auth_service.register_user(user_in)

@router.post("/login", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    return await auth_service.login(form_data)

@router.get("/profile", response_model=UserProfile)
async def read_users_me(current_user: dict = Depends(get_current_user)):
    return await auth_service.get_profile(current_user["id"])

@router.put("/profile", response_model=UserProfile)
async def update_users_me(
    profile_data: UserProfile,
    current_user: dict = Depends(get_current_user)
):
    return await auth_service.update_profile(
        current_user["id"],
        profile_data.model_dump(exclude_unset=True)
    )

# ✅ New route: Handle login from Google client
@router.post("/google-login")
async def google_login(google_user: Dict[str, Any] = Body(...)):
    return await auth_service.handle_google_login(google_user)

# ✅ New route: Create full profile after Google login
@router.post("/complete-profile", response_model=Token)
async def complete_profile(profile_data: Dict[str, Any] = Body(...)):
    return await auth_service.create_profile_after_google(profile_data)

# ✅ New route: Update user profile
@router.put("/profile")
async def update_profile(
    profile_data: Dict[str, Any] = Body(...),
    current_user: dict = Depends(get_current_user)
):
    return await auth_service.update_profile(current_user["user_id"], profile_data)

# ✅ New route: Save user preferences
@router.post("/preferences")
async def save_preferences(
    preferences: Dict[str, Any] = Body(...),
    current_user: dict = Depends(get_current_user)
):
    return await auth_service.save_preferences(current_user["id"], preferences)