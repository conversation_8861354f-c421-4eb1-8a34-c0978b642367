"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useUserStore } from "@/store/userStore";
import { useChatStore } from "@/store/chatStore";
import { Loader2 } from "lucide-react";

export default function AfterLogin() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const setUser = useUserStore((state) => state.setUser);
  const setCurrentUser = useChatStore((state) => state.setCurrentUser);

  useEffect(() => {
    const checkUser = async () => {
      if (status === "authenticated") {
        try {
          const res = await fetch(
            `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/v1/auth/google-login`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ email: session.user?.email }),
            }
          );

          if (res.status === 200) {
            const data = await res.json();

            // Save JWT cookie
            document.cookie = `token=${data.access_token}; path=/; Secure; SameSite=Lax`;

            // Fetch user profile
            const profileRes = await fetch(
              `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/v1/auth/profile`,
              {
                headers: {
                  Authorization: `Bearer ${data.access_token}`,
                },
              }
            );

            const user = await profileRes.json();

            // Add profile picture from localStorage
            const storedProfilePicture = localStorage.getItem('user_profile_picture') ||
                                        localStorage.getItem('google_profile_picture');
            if (storedProfilePicture) {
              user.profile_picture = storedProfilePicture;
            }

            // Set Zustand store
            setUser({ currentUser: user, isAuthenticated: true });

            // Set chat store current user to ensure proper isolation
            setCurrentUser(user.id);

            // Simple and reliable onboarding check
            const hasCompletedOnboarding = user.phone && user.city && user.age_group;

            // Check if preferences have been completed (using flag)
            const preferencesCompleted = localStorage.getItem('preferences_completed') === 'true';

            // Also check if user has preferences in localStorage (backup check)
            let hasExistingPreferences = false;
            try {
              const userStoreData = localStorage.getItem('rotishoti-user-storage');
              if (userStoreData) {
                const parsedData = JSON.parse(userStoreData);
                const storedUser = parsedData.state?.currentUser;
                if (storedUser?.email === user.email && storedUser?.preferences?.favoriteCuisines?.length > 0) {
                  hasExistingPreferences = true;
                  // Restore preferences to current user
                  setUser({
                    currentUser: { ...user, preferences: storedUser.preferences },
                    isAuthenticated: true
                  });
                }
              }
            } catch (e) {
              console.log('Error checking localStorage preferences:', e);
            }

            const shouldSkipPreferences = preferencesCompleted || hasExistingPreferences;

            console.log('Onboarding check:', {
              hasCompletedOnboarding,
              preferencesCompleted,
              hasExistingPreferences,
              shouldSkipPreferences,
              userEmail: user.email
            });

            if (!hasCompletedOnboarding) {
              // New user - needs to complete profile first
              router.replace("/complete-profile");
            } else if (!shouldSkipPreferences) {
              // User has profile but hasn't completed preferences
              router.replace("/setup-preferences");
            } else {
              // User has completed everything - go to homepage
              router.replace("/");
            }
          } else if (res.status === 404) {
            router.replace("/complete-profile");
          }
        } catch (err) {
          console.error("Google login check failed", err);
        }
      }
    };

    checkUser();
  }, [status]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-yellow-50 to-orange-50 px-4">
      <div className="flex flex-col items-center justify-center text-center p-6 bg-white rounded-xl shadow-lg max-w-md w-full">
        <Loader2 className="w-8 h-8 text-orange-500 animate-spin mb-4" />
        <h2 className="text-xl font-semibold text-gray-800 mb-2">
          Finalizing Login...
        </h2>
        <p className="text-gray-600">
          Please wait while we check your profile and sign you in.
        </p>
      </div>
    </div>
  );
}
