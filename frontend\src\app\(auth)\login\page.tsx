"use client";

import { signIn } from "next-auth/react";
import { useState } from "react";
import { Loader2, Utensils, Star, MapPin, Clock } from "lucide-react";

export default function SignIn() {
  const [loading, setLoading] = useState(false);

  const handleGoogleLogin = async () => {
    setLoading(true);
    await signIn("google", { callbackUrl: "/after-login" });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-yellow-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 text-6xl">🍕</div>
        <div className="absolute top-32 right-20 text-4xl">🍔</div>
        <div className="absolute bottom-20 left-20 text-5xl">🍜</div>
        <div className="absolute bottom-32 right-10 text-4xl">🥘</div>
        <div className="absolute top-1/2 left-1/3 text-3xl">🍛</div>
        <div className="absolute top-1/3 right-1/3 text-4xl">🌮</div>
      </div>

      <div className="min-h-screen flex items-center justify-center p-6 relative z-10">
        <div className="max-w-4xl w-full grid md:grid-cols-2 gap-8 items-center">

          {/* Left Side - Branding & Features */}
          <div className="text-center md:text-left space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-center md:justify-start gap-3">
                <div className="bg-gradient-to-r from-orange-500 to-red-500 p-3 rounded-full">
                  <Utensils className="w-8 h-8 text-white" />
                </div>
                <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                  RotiShoti
                </h1>
              </div>
              <p className="text-xl text-gray-600 font-medium">
                AI-Powered Food Discovery Platform
              </p>
              <p className="text-gray-500 text-lg">
                Discover amazing Pakistani cuisine with intelligent recommendations
              </p>
            </div>

            {/* Features */}
            <div className="space-y-4 pt-4">
              <div className="flex items-center gap-3 text-gray-700">
                <div className="bg-orange-100 p-2 rounded-full">
                  <Star className="w-5 h-5 text-orange-600" />
                </div>
                <span className="font-medium">Smart AI Recommendations</span>
              </div>
              <div className="flex items-center gap-3 text-gray-700">
                <div className="bg-red-100 p-2 rounded-full">
                  <MapPin className="w-5 h-5 text-red-600" />
                </div>
                <span className="font-medium">Location-Based Search</span>
              </div>
              <div className="flex items-center gap-3 text-gray-700">
                <div className="bg-yellow-100 p-2 rounded-full">
                  <Clock className="w-5 h-5 text-yellow-600" />
                </div>
                <span className="font-medium">Real-Time Availability</span>
              </div>
            </div>
          </div>

          {/* Right Side - Sign In Form */}
          <div className="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-2xl border border-white/20">
            <div className="text-center space-y-6">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold text-gray-800">Welcome Back!</h2>
                <p className="text-gray-600">Sign in to discover your next favorite meal</p>
              </div>

              <div className="space-y-4">
                <button
                  onClick={handleGoogleLogin}
                  disabled={loading}
                  className={`w-full py-4 px-6 bg-white border-2 border-gray-200 rounded-xl font-semibold text-gray-700
                    hover:border-orange-300 hover:bg-orange-50 transition-all duration-300 transform hover:scale-105
                    shadow-lg hover:shadow-xl flex items-center justify-center gap-3
                    ${loading ? "opacity-50 cursor-not-allowed transform-none" : ""}
                  `}
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin text-orange-600" />
                      <span>Signing you in...</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                      </svg>
                      <span>Continue with Google</span>
                    </>
                  )}
                </button>

                <div className="text-xs text-gray-500 leading-relaxed">
                  By signing in, you agree to our Terms of Service and Privacy Policy.
                  We use your data to provide personalized food recommendations.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Stats */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 text-center text-gray-500 text-sm">
        <div className="flex items-center gap-6">
          <span>🏪 16+ Restaurants</span>
          <span>🍽️ 96+ Menu Items</span>
          <span>🤖 AI-Powered</span>
          <span>📍 Islamabad</span>
        </div>
      </div>
    </div>
  );
}
