"""
RotiShoti - AI-Powered Food Discovery Platform
Authors: <AUTHORS>
Module: Recommendations API - Thin controller layer for recommendation endpoints
"""

from fastapi import APIRouter, HTTPException, status
from typing import List
from app.schemas.recommendation_schemas import RecommendationQuery, RecommendationResponse, Feedback
from app.services.recommendation_service import recommendation_service
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(tags=["recommendations"])

@router.post("/query", response_model=List[RecommendationResponse])
async def get_ai_recommendations(query: RecommendationQuery) -> List[RecommendationResponse]:
    """
    Get AI-powered restaurant recommendations based on user query.

    Args:
        query: User's recommendation query with preferences

    Returns:
        List of restaurant recommendations with AI descriptions
    """
    try:
        logger.info(f"Processing recommendation query: {query.user_query}")

        # Use dummy user for development
        dummy_user_id = "00000000-0000-0000-0000-000000000000"

        recommendations = await recommendation_service.generate_recommendations(
            user_query=query.user_query,
            user_id=dummy_user_id,
            location=query.location or "Islamabad",
            budget=query.budget.value if query.budget else "medium",
            dietary_restrictions=query.dietary_restrictions or ["none"]
        )

        # Format the recommendations to match the RecommendationResponse schema
        formatted_recommendations = []
        for rec in recommendations:
            restaurant = rec["restaurant"]
            formatted_recommendations.append(RecommendationResponse(
                restaurant_id=str(restaurant.get("id", "unknown")),
                name=restaurant.get("name", "Unknown Restaurant"),
                cuisine=", ".join(restaurant.get("cuisine_types", [])) or "N/A",
                address=restaurant.get("address") or "N/A",
                rating=restaurant.get("rating_overall") or 0.0,
                price_range=f"PKR {restaurant.get('price_range_min', 'N/A')}-{restaurant.get('price_range_max', 'N/A')}",
                ai_description=rec.get("ai_description", "Great restaurant choice!"),
                menu_items_recommended=[item.get("name", "") for item in restaurant.get("menu_items", [])]
            ))

        logger.info(f"Returning {len(formatted_recommendations)} recommendations")
        return formatted_recommendations

    except Exception as e:
        logger.error(f"Error processing recommendation query: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate recommendations"
        )

@router.get("/popular", response_model=List[RecommendationResponse])
async def get_popular_recommendations(city: str = "Islamabad") -> List[RecommendationResponse]:
    """
    Get popular restaurant recommendations based on ratings and reviews.

    Args:
        city: City to get popular restaurants from

    Returns:
        List of popular restaurant recommendations
    """
    try:
        logger.info(f"Getting popular recommendations for {city}")

        popular_restaurants = await recommendation_service.get_popular_recommendations(city=city)

        formatted_recommendations = []
        for restaurant in popular_restaurants:
            formatted_recommendations.append(RecommendationResponse(
                restaurant_id=str(restaurant.get('id', 'unknown')),
                name=restaurant.get('name', 'Popular Restaurant'),
                cuisine=", ".join(restaurant.get('cuisine_types', [])) or "N/A",
                address=restaurant.get('address') or "N/A",
                rating=restaurant.get('rating_overall') or 0.0,
                price_range=f"PKR {restaurant.get('price_range_min', 'N/A')}-{restaurant.get('price_range_max', 'N/A')}",
                ai_description="This is a popular choice among food lovers!",
                menu_items_recommended=[item.get('name', '') for item in restaurant.get('menu_items', [])]
            ))

        logger.info(f"Returning {len(formatted_recommendations)} popular recommendations")
        return formatted_recommendations

    except Exception as e:
        logger.error(f"Error getting popular recommendations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get popular recommendations"
        )

@router.get("/personalized", response_model=List[RecommendationResponse])
async def get_personalized_recommendations(city: str = "Islamabad") -> List[RecommendationResponse]:
    """
    Get personalized restaurant recommendations based on user preferences.

    Args:
        city: City to get recommendations from

    Returns:
        List of personalized restaurant recommendations
    """
    try:
        logger.info(f"Getting personalized recommendations for {city}")

        # Use dummy user preferences for development
        dummy_user_preferences = {
            'favorite_cuisines': ['Pakistani', 'BBQ', 'Fast Food'],
            'budget_range': (500, 3000),
            'spice_level': 'medium'
        }

        personalized_restaurants = await recommendation_service.get_personalized_recommendations(
            user_preferences=dummy_user_preferences,
            city=city
        )

        formatted_recommendations = []
        for restaurant in personalized_restaurants:
            formatted_recommendations.append(RecommendationResponse(
                restaurant_id=str(restaurant.get('id', 'unknown')),
                name=restaurant.get('name', 'Personalized Restaurant'),
                cuisine=", ".join(restaurant.get('cuisine_types', [])) or "N/A",
                address=restaurant.get('address') or "N/A",
                rating=restaurant.get('rating_overall') or 0.0,
                price_range=f"PKR {restaurant.get('price_range_min', 'N/A')}-{restaurant.get('price_range_max', 'N/A')}",
                ai_description="Here's a personalized recommendation just for you!",
                menu_items_recommended=[item.get('name', '') for item in restaurant.get('menu_items', [])]
            ))

        logger.info(f"Returning {len(formatted_recommendations)} personalized recommendations")
        return formatted_recommendations

    except Exception as e:
        logger.error(f"Error getting personalized recommendations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get personalized recommendations"
        )

@router.post("/feedback", status_code=status.HTTP_204_NO_CONTENT)
async def submit_feedback(feedback: Feedback):
    """
    Submit feedback for a restaurant recommendation.

    Args:
        feedback: User feedback data

    Returns:
        Success message
    """
    try:
        logger.info(f"Receiving feedback for recommendation {feedback.recommendation_id}")

        # For now, just log the feedback
        # In a real application, this would be stored in the database
        logger.info(f"Feedback: helpful={feedback.helpful}, comment={feedback.comment}")

        # TODO: Implement actual feedback storage when user service is available
        # await user_service.log_user_interaction({
        #     "user_id": "dummy_user_id",
        #     "interaction_type": "feedback",
        #     "restaurant_id": feedback.recommendation_id,
        #     "helpful": feedback.helpful,
        #     "comment": feedback.comment
        # })

        return {"message": "Feedback received successfully"}

    except Exception as e:
        logger.error(f"Error submitting feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit feedback"
        )