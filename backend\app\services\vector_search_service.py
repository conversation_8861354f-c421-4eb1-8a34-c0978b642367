"""
RotiShoti - AI-Powered Food Discovery Platform
Author: <PERSON><PERSON><PERSON>
Vector Search Service - Semantic search using pgvector embeddings
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from app.db.supabase import create_supabase_client
from app.services.ai_service import ai_service

logger = logging.getLogger(__name__)

class VectorSearchService:
    def __init__(self):
        self.supabase = create_supabase_client()
        self.ai_service = ai_service

    def _enhance_search_query(self, query: str) -> str:
        """
        Enhance search query with context for better semantic matching
        """
        query_lower = query.lower().strip()

        # Food category mappings for better semantic understanding
        food_mappings = {
            'desi': 'traditional Pakistani food biryani karahi handi BBQ',
            'bbq': 'grilled Pakistani barbecue malai boti seekh kabab tikka',
            'karahi': 'Pakistani chicken karahi mutton karahi traditional curry',
            'biryani': 'Pakistani rice dish chicken biryani mutton biryani',
            'fast food': 'burger pizza sandwich quick meal',
            'chinese': 'Chinese cuisine noodles fried rice chow mein',
            'continental': 'Western food pasta steak continental dishes',
            'breakfast': 'morning meal paratha halwa puri nihari chai',
            'lunch': 'midday meal rice curry main course',
            'dinner': 'evening meal family dining main course',
            'spicy': 'hot spicy food with chilies and spices',
            'mild': 'less spicy gentle flavors not hot',
            'vegetarian': 'vegetable dishes no meat plant based',
            'halal': 'Islamic halal certified meat dishes'
        }

        # Cuisine type enhancements
        cuisine_enhancements = {
            'pakistani': 'desi traditional local authentic',
            'italian': 'pasta pizza Italian cuisine',
            'american': 'burgers steaks American food',
            'mexican': 'tacos burritos Mexican spicy'
        }

        # Price-related enhancements
        budget_terms = {
            'cheap': 'affordable budget friendly low price',
            'expensive': 'premium high end costly',
            'budget': 'affordable economical reasonable price'
        }

        enhanced_query = query

        # Apply food category mappings
        for key, enhancement in food_mappings.items():
            if key in query_lower:
                enhanced_query += f" {enhancement}"

        # Apply cuisine enhancements
        for cuisine, enhancement in cuisine_enhancements.items():
            if cuisine in query_lower:
                enhanced_query += f" {enhancement}"

        # Apply budget enhancements
        for budget_term, enhancement in budget_terms.items():
            if budget_term in query_lower:
                enhanced_query += f" {enhancement}"

        # Add Pakistani context for better local results
        if not any(term in query_lower for term in ['chinese', 'italian', 'american', 'continental']):
            enhanced_query += " Pakistani Islamabad local"

        return enhanced_query.strip()

    def _calculate_adaptive_threshold(self, query: str, base_threshold: float) -> float:
        """
        Calculate adaptive similarity threshold based on query characteristics
        """
        query_lower = query.lower()

        # Lower threshold for specific food items (more precise matching needed)
        if any(term in query_lower for term in ['biryani', 'karahi', 'burger', 'pizza', 'tikka']):
            return max(0.25, base_threshold - 0.2)

        # Higher threshold for general categories (broader matching acceptable)
        if any(term in query_lower for term in ['food', 'restaurant', 'meal', 'cuisine']):
            return min(0.7, base_threshold + 0.1)

        # Medium threshold for cuisine types
        if any(term in query_lower for term in ['pakistani', 'chinese', 'italian', 'desi']):
            return max(0.3, base_threshold - 0.1)

        # Lower threshold for budget queries (cast wider net)
        if any(term in query_lower for term in ['cheap', 'budget', 'affordable', 'under']):
            return max(0.2, base_threshold - 0.25)

        return max(0.3, base_threshold - 0.2)  # Default: slightly lower for better recall

    def _post_process_results(self, results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Post-process search results for better relevance and ranking
        """
        if not results:
            return results

        query_lower = query.lower()
        processed_results = []

        for result in results:
            # Calculate relevance boost based on exact matches
            relevance_boost = 0.0
            item_name_lower = result['item_name'].lower()

            # Exact name match gets highest boost
            if query_lower in item_name_lower:
                relevance_boost += 0.3

            # Category match gets medium boost
            if result.get('category') and query_lower in result['category'].lower():
                relevance_boost += 0.2

            # Restaurant name match gets small boost
            if query_lower in result['restaurant_name'].lower():
                relevance_boost += 0.1

            # Cuisine type match gets medium boost
            if result.get('cuisine_types'):
                for cuisine in result['cuisine_types']:
                    if query_lower in cuisine.lower():
                        relevance_boost += 0.15
                        break

            # Apply relevance boost to similarity score
            boosted_score = min(1.0, result['similarity_score'] + relevance_boost)
            result['similarity_score'] = boosted_score
            result['relevance_boost'] = relevance_boost

            processed_results.append(result)

        # Re-sort by boosted similarity score
        processed_results.sort(key=lambda x: x['similarity_score'], reverse=True)

        return processed_results

    async def semantic_search_menu_items(
        self,
        query: str,
        limit: int = 30,
        similarity_threshold: float = 0.5,
        budget_range: Optional[Tuple[int, int]] = None,
        city: str = "Islamabad"
    ) -> List[Dict[str, Any]]:
        """
        Enhanced semantic search on menu items using vector embeddings with intelligent query processing
        """
        try:
            # Enhance query with context for better embeddings
            enhanced_query = self._enhance_search_query(query)
            logger.info(f"Enhanced query: '{query}' -> '{enhanced_query}'")

            # Generate embedding for the enhanced search query
            query_embedding = await self.ai_service.generate_embedding(enhanced_query)
            
            # Build the SQL query for vector similarity search
            sql_query = """
            SELECT 
                r.name as restaurant_name,
                r.area,
                r.cuisine_types,
                mi.name as item_name,
                mi.description,
                mi.price,
                mi.category,
                (1 - (mi.embedding <=> %s::vector)) as similarity_score
            FROM menu_items mi
            JOIN restaurants r ON mi.restaurant_id = r.id
            WHERE r.city = %s
            """
            
            params = [query_embedding, city]
            
            # Add budget filter if specified
            if budget_range:
                min_budget, max_budget = budget_range
                # Allow 5% flexibility for high-end items
                max_allowed = max_budget + (max_budget * 0.05 if max_budget >= 3000 else 0)
                sql_query += " AND mi.price BETWEEN %s AND %s"
                params.extend([min_budget, max_allowed])
            
            # Add similarity threshold and ordering
            sql_query += """
            AND (1 - (mi.embedding <=> %s::vector)) >= %s
            ORDER BY mi.embedding <=> %s::vector
            LIMIT %s
            """
            params.extend([query_embedding, similarity_threshold, query_embedding, limit])
            
            # Use intelligent vector similarity search with adaptive threshold
            logger.info(f"Starting enhanced vector search for query: '{query}' with budget: {budget_range}")

            # Adaptive threshold based on query type
            adaptive_threshold = self._calculate_adaptive_threshold(query, similarity_threshold)
            logger.info(f"Using adaptive threshold: {adaptive_threshold} (original: {similarity_threshold})")

            results = await self._direct_vector_search(query_embedding, city, budget_range, limit, adaptive_threshold)

            # Post-process results for better relevance
            filtered_results = self._post_process_results(results, query)
            logger.info(f"Vector search returned {len(filtered_results)} results after post-processing")

            return filtered_results

        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            # Fallback to text-based search
            logger.info(f"Falling back to text search for query: '{query}'")
            return await self._fallback_text_search(query, city, budget_range, limit)

    async def _direct_vector_search(
        self,
        query_embedding: List[float],
        city: str,
        budget_range: Optional[Tuple[int, int]],
        limit: int,
        similarity_threshold: float
    ) -> List[Dict[str, Any]]:
        """
        Direct vector search using Supabase client
        """
        try:
            logger.info(f"Direct vector search: city={city}, budget_range={budget_range}")

            # Build query with joins
            query_builder = self.supabase.from_('menu_items').select("""
                name,
                description,
                price,
                category,
                embedding,
                restaurants!inner(name, area, cuisine_types, city)
            """).eq('restaurants.city', city)

            # Add budget filter if specified
            if budget_range:
                min_budget, max_budget = budget_range
                max_allowed = max_budget + (max_budget * 0.05 if max_budget >= 3000 else 0)
                # Convert to integers to avoid SQL type errors
                min_budget = int(min_budget)
                max_allowed = int(max_allowed)
                query_builder = query_builder.gte('price', min_budget).lte('price', max_allowed)
                logger.info(f"Applied budget filter: {min_budget} <= price <= {max_allowed}")

            # Execute query to get all items
            response = query_builder.limit(200).execute()  # Get more items for similarity calculation
            logger.info(f"Database query returned {len(response.data or [])} items")

            # Calculate similarity scores manually
            results = []
            for item in response.data or []:
                try:
                    item_embedding = item.get('embedding')
                    if item_embedding and len(item_embedding) > 0:
                        # Parse embedding if it's a string (from Supabase)
                        if isinstance(item_embedding, str):
                            import json
                            item_embedding = json.loads(item_embedding)

                        # Debug: Log dimensions for first few items
                        if len(results) < 3:
                            logger.info(f"Item '{item['name']}': query_embedding={len(query_embedding)}D, item_embedding={len(item_embedding)}D")

                        # Calculate cosine similarity
                        similarity = self._calculate_cosine_similarity(query_embedding, item_embedding)

                        # Log similarity for debugging
                        if 'burger' in item['name'].lower():
                            logger.info(f"Burger item '{item['name']}' similarity: {similarity:.4f} (threshold: {similarity_threshold})")

                        if similarity >= similarity_threshold:
                            restaurant = item['restaurants']
                            results.append({
                                'restaurant_name': restaurant['name'],
                                'area': restaurant['area'],
                                'cuisine_types': restaurant['cuisine_types'],
                                'item_name': item['name'],
                                'description': item['description'],
                                'price': item['price'],
                                'category': item['category'],
                                'similarity_score': similarity
                            })
                except Exception as e:
                    logger.warning(f"Error calculating similarity for item {item.get('name')}: {str(e)}")
                    continue

            # Sort by similarity score and limit results
            results.sort(key=lambda x: x['similarity_score'], reverse=True)
            logger.info(f"Found {len(results)} items above similarity threshold {similarity_threshold}")

            return results[:limit]

        except Exception as e:
            logger.error(f"Direct vector search failed: {str(e)}")
            return []

    async def _fallback_vector_search(
        self,
        query_embedding: List[float],
        city: str,
        budget_range: Optional[Tuple[int, int]],
        limit: int
    ) -> List[Dict[str, Any]]:
        """
        Enhanced vector search using manual similarity calculation
        """
        try:
            logger.info(f"Fallback vector search: city={city}, budget_range={budget_range}")

            # Get all menu items with embeddings
            query_builder = self.supabase.from_('menu_items').select("""
                name,
                description,
                price,
                category,
                embedding,
                restaurants!inner(name, area, cuisine_types, city)
            """).eq('restaurants.city', city)

            logger.info(f"Built initial query for city: {city}")

            if budget_range:
                min_budget, max_budget = budget_range
                max_allowed = max_budget + (max_budget * 0.05 if max_budget >= 3000 else 0)
                query_builder = query_builder.gte('price', min_budget).lte('price', max_allowed)
                logger.info(f"Applied budget filter: {min_budget} <= price <= {max_allowed}")

            response = query_builder.limit(100).execute()  # Get more items for similarity calculation
            logger.info(f"Database query returned {len(response.data or [])} items")

            # Calculate similarity scores manually
            results = []
            for item in response.data or []:
                try:
                    item_embedding = item.get('embedding')
                    if item_embedding:
                        # Calculate cosine similarity
                        similarity = self._calculate_cosine_similarity(query_embedding, item_embedding)

                        if similarity >= 0.6:  # Similarity threshold
                            restaurant = item['restaurants']
                            results.append({
                                'restaurant_name': restaurant['name'],
                                'area': restaurant['area'],
                                'cuisine_types': restaurant['cuisine_types'],
                                'item_name': item['name'],
                                'description': item['description'],
                                'price': item['price'],
                                'category': item['category'],
                                'similarity_score': similarity
                            })
                except Exception as e:
                    logger.warning(f"Error calculating similarity for item {item.get('name')}: {str(e)}")
                    continue

            # Sort by similarity score and limit results
            results.sort(key=lambda x: x['similarity_score'], reverse=True)
            return results[:limit]

        except Exception as e:
            logger.error(f"Fallback vector search failed: {str(e)}")
            return []

    def _calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors
        """
        try:
            import math

            # Ensure vectors are the same length
            if len(vec1) != len(vec2):
                logger.warning(f"Vector length mismatch: {len(vec1)} vs {len(vec2)}")
                return 0.0

            # Convert to float arrays if needed
            vec1 = [float(x) for x in vec1]
            vec2 = [float(x) for x in vec2]

            # Calculate dot product
            dot_product = sum(a * b for a, b in zip(vec1, vec2))

            # Calculate magnitudes
            magnitude1 = math.sqrt(sum(a * a for a in vec1))
            magnitude2 = math.sqrt(sum(a * a for a in vec2))

            # Avoid division by zero
            if magnitude1 == 0 or magnitude2 == 0:
                logger.warning("Zero magnitude vector encountered")
                return 0.0

            # Calculate cosine similarity
            similarity = dot_product / (magnitude1 * magnitude2)

            # Cosine similarity ranges from -1 to 1, but we want 0 to 1
            # Convert to 0-1 range: (similarity + 1) / 2
            normalized_similarity = (similarity + 1) / 2

            logger.debug(f"Cosine similarity: {similarity:.4f}, normalized: {normalized_similarity:.4f}")
            return max(0.0, min(1.0, normalized_similarity))  # Ensure 0-1 range

        except Exception as e:
            logger.error(f"Error calculating cosine similarity: {str(e)}")
            return 0.0

    async def _fallback_text_search(
        self, 
        query: str, 
        city: str, 
        budget_range: Optional[Tuple[int, int]], 
        limit: int
    ) -> List[Dict[str, Any]]:
        """
        Fallback to text-based search when vector search fails
        """
        try:
            query_builder = self.supabase.from_('menu_items').select("""
                name,
                description,
                price,
                category,
                restaurants!inner(name, area, cuisine_types, city)
            """).eq('restaurants.city', city)
            
            # Text search in name and description
            search_term = f"%{query.lower()}%"
            query_builder = query_builder.or_(
                f'name.ilike.{search_term},description.ilike.{search_term}'
            )
            
            if budget_range:
                min_budget, max_budget = budget_range
                max_allowed = max_budget + (max_budget * 0.05 if max_budget >= 3000 else 0)
                query_builder = query_builder.gte('price', min_budget).lte('price', max_allowed)
            
            response = query_builder.limit(limit).execute()
            
            # Transform the response
            results = []
            for item in response.data or []:
                restaurant = item['restaurants']
                results.append({
                    'restaurant_name': restaurant['name'],
                    'area': restaurant['area'],
                    'cuisine_types': restaurant['cuisine_types'],
                    'item_name': item['name'],
                    'description': item['description'],
                    'price': item['price'],
                    'category': item['category'],
                    'similarity_score': 0.6  # Lower score for text search
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Text search fallback failed: {str(e)}")
            return []

    async def find_similar_items(
        self, 
        item_name: str, 
        restaurant_name: str, 
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Find items similar to a specific menu item
        """
        try:
            # Get the embedding of the target item
            response = self.supabase.from_('menu_items').select(
                'embedding'
            ).eq('name', item_name).execute()
            
            if not response.data:
                return []
            
            target_embedding = response.data[0]['embedding']
            
            # Find similar items using vector similarity
            return await self.semantic_search_menu_items(
                query=item_name,
                limit=limit,
                similarity_threshold=0.6
            )
            
        except Exception as e:
            logger.error(f"Similar items search failed: {str(e)}")
            return []

# Create global instance
vector_search_service = VectorSearchService()
