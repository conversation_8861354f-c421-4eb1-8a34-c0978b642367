'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, X } from 'lucide-react';
import ChatModal from './ChatModal';
import { RotiShotiAgent } from './RotiShotiAgent';

interface FloatingChatButtonProps {
  initialQuery?: string;
  className?: string;
}

const FloatingChatButton: React.FC<FloatingChatButtonProps> = ({
  initialQuery = '',
  className = ''
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [currentQuery, setCurrentQuery] = useState(initialQuery);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle initial query
  useEffect(() => {
    if (initialQuery) {
      setIsModalOpen(true);
    }
  }, [initialQuery]);

  // Listen for global chat open events
  useEffect(() => {
    const handleOpenChat = (event: CustomEvent) => {
      const query = event.detail?.query || '';
      setCurrentQuery(query);
      setIsModalOpen(true);
    };

    window.addEventListener('openChat', handleOpenChat as EventListener);
    return () => window.removeEventListener('openChat', handleOpenChat as EventListener);
  }, []);

  const handleToggleModal = () => {
    setIsModalOpen(!isModalOpen);
  };

  return (
    <>
      {/* Floating Chat Button */}
      <motion.div
        className={`fixed z-40 ${className}`}
        style={{
          bottom: isMobile ? '20px' : '30px',
          right: isMobile ? '20px' : '30px'
        }}
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 1, duration: 0.3 }}
      >
        <motion.button
          onClick={handleToggleModal}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
          className="relative group"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {/* Main Button */}
          <div
            className={`${
              isMobile ? 'w-16 h-16' : 'w-20 h-20'
            } bg-gradient-to-r from-orange-500 to-red-500 rounded-full shadow-2xl flex items-center justify-center text-white transition-all duration-300 hover:shadow-3xl`}
            style={{
              boxShadow: '0 8px 32px rgba(255, 107, 0, 0.4)'
            }}
          >
            <AnimatePresence mode="wait">
              {isModalOpen ? (
                <motion.div
                  key="close"
                  initial={{ rotate: -90, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  exit={{ rotate: 90, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <X size={isMobile ? 24 : 28} />
                </motion.div>
              ) : (
                <motion.div
                  key="chat"
                  initial={{ rotate: 90, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  exit={{ rotate: -90, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center justify-center"
                >
                  <RotiShotiAgent
                    state={isHovered ? 'excited' : 'idle'}
                    size="small"
                    showName={false}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Pulse Animation */}
          {!isModalOpen && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.7, 0, 0.7]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          )}

          {/* Tooltip */}
          <AnimatePresence>
            {isHovered && !isModalOpen && !isMobile && (
              <motion.div
                initial={{ opacity: 0, x: 10, scale: 0.8 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: 10, scale: 0.8 }}
                className="absolute right-full mr-4 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-4 py-2 rounded-xl text-sm font-medium whitespace-nowrap shadow-lg"
                style={{ zIndex: 1000 }}
              >
                Chat with RotiShoti AI 🍽️
                <div className="absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-l-8 border-l-gray-900 border-t-4 border-t-transparent border-b-4 border-b-transparent" />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.button>

        {/* Notification Badge (optional) */}
        {!isModalOpen && (
          <motion.div
            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 2, duration: 0.3 }}
          >
            AI
          </motion.div>
        )}
      </motion.div>

      {/* Chat Modal */}
      <ChatModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setCurrentQuery('');
        }}
        initialQuery={currentQuery}
      />
    </>
  );
};

export default FloatingChatButton;
