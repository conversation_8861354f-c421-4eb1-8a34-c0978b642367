#!/usr/bin/env python3
"""
Script to create test user for RotiShoti
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.auth_service import auth_service
from app.schemas.auth_schemas import UserRegister

async def create_test_user():
    """Create the test user"""
    try:
        user_data = UserRegister(
            name="Masab Farooque",
            email="<EMAIL>",
            password="pakistan123"
        )
        
        print("Creating test user...")
        user = await auth_service.register_user(user_data)
        print(f"✅ User created successfully!")
        print(f"   Name: {user.name}")
        print(f"   Email: {user.email}")
        print(f"   ID: {user.id}")
        
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        if "already exists" in str(e).lower():
            print("   User already exists - you can now login!")
        else:
            print(f"   Full error: {e}")

if __name__ == "__main__":
    asyncio.run(create_test_user())
