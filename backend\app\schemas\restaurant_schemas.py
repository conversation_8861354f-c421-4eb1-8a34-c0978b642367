from pydantic import BaseModel, Field
from typing import List, Optional
import uuid

class MenuItemBase(BaseModel):
    name: str
    description: Optional[str] = None
    price: int
    category: Optional[str] = None
    spice_level: Optional[str] = None
    is_vegetarian: Optional[bool] = False
    is_available: Optional[bool] = True

class MenuItemCreate(MenuItemBase):
    pass

class MenuItemResponse(MenuItemBase):
    id: uuid.UUID
    restaurant_id: uuid.UUID

    class Config:
        from_attributes = True

class Branch(BaseModel):
    branch_id: str
    name: str
    area: str
    address: str
    phone: Optional[str] = None
    coordinates: List[float]  # [latitude, longitude]
    is_main_branch: bool = False
    features: Optional[List[str]] = []

class PriceRange(BaseModel):
    min: int
    max: int
    average: int

class Ratings(BaseModel):
    overall: float
    food_quality: Optional[float] = None
    service: Optional[float] = None
    value: Optional[float] = None

class Ambiance(BaseModel):
    type: str
    noise_level: Optional[str] = None

class RestaurantBase(BaseModel):
    name: str
    city: str
    image_url: Optional[str] = None
    cuisine_types: List[str] = []
    price_range: PriceRange
    branches: List[Branch] = []
    ratings: Ratings
    ambiance: Ambiance

class RestaurantCreate(RestaurantBase):
    menu_items: List[MenuItemCreate] = []

class RestaurantResponse(RestaurantBase):
    id: uuid.UUID
    menu_items: List[MenuItemResponse] = []
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    class Config:
        from_attributes = True

class RestaurantSearch(BaseModel):
    query: str
    city: Optional[str] = None
    cuisine: Optional[str] = None
    price_max: Optional[int] = None
    limit: int = 10
    offset: int = 0

class RestaurantNearMe(BaseModel):
    latitude: float
    longitude: float
    radius_km: float = 5.0
    limit: int = 10
    offset: int = 0