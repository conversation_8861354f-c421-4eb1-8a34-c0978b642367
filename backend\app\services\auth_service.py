# app/services/auth_service.py
from typing import Any, Dict
from fastapi import HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>Re<PERSON>Form
from app.schemas.auth_schemas import UserReg<PERSON>, Token, UserProfile
from app.services.user_service import user_service
from app.core.jwt import verify_password, get_password_hash, create_access_token
from app.core.enums import UserRole

class AuthService:
    def _determine_food_personality(self, dietary_restrictions: list = None) -> str:
        """Determine user's food personality based on dietary restrictions and preferences"""
        if not dietary_restrictions:
            return "Food Explorer"

        restrictions = [r.lower() for r in dietary_restrictions]

        if 'vegan' in restrictions:
            return "Plant-Based Pioneer"
        elif 'vegetarian' in restrictions:
            return "Veggie Enthusiast"
        elif 'halal' in restrictions:
            return "Halal Food Connoisseur"
        elif 'keto' in restrictions:
            return "Keto Warrior"
        elif 'gluten-free' in restrictions:
            return "Gluten-Free Foodie"
        elif 'dairy-free' in restrictions:
            return "Dairy-Free Discoverer"
        else:
            return "Culinary Adventurer"
    async def register_user(self, user_in: UserRegister):
        existing_user = await user_service.get_user_by_email(user_in.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered",
            )

        hashed_password = get_password_hash(user_in.password)
        user_data = user_in.model_dump()
        user_data["password"] = hashed_password
        user_data["role"] = UserRole.USER
        return await user_service.create_user(user_data)

    async def login(self, form_data: OAuth2PasswordRequestForm):
        user = await user_service.get_user_by_email(form_data.username)
        if not user or not verify_password(form_data.password, user.password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        token = create_access_token(
            subject=str(user.id),
            role=user.role,
            name=user.name,
            email=user.email
        )
        return {"access_token": token, "token_type": "bearer"}

    async def handle_google_login(self, google_user: Dict[str, Any]) -> Dict[str, str]:
        """
        Called after Google login is successful on frontend.
        Checks if user exists in DB. If yes, returns token.
        If not, raises 404 so client can redirect to 'Complete Profile' page.
        """
        email = google_user.get("email")
        if not email:
            raise HTTPException(status_code=400, detail="Email is required")

        existing_user = await user_service.get_user_by_email(email)
        if not existing_user:
            raise HTTPException(status_code=404, detail="User not found")  # frontend should redirect to /complete-profile

        token = create_access_token(
            subject=str(existing_user.id),
            role=existing_user.role,
            name=existing_user.name,
            email=existing_user.email,
        )
        return {"access_token": token, "token_type": "bearer"}

    async def create_profile_after_google(self, profile_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Called from frontend after completing profile (e.g. name, phone, etc.)
        Assumes Google already verified the user.
        """
        email = profile_data.get("email")
        if not email:
            raise HTTPException(status_code=400, detail="Email is required")

        existing_user = await user_service.get_user_by_email(email)
        if existing_user:
            raise HTTPException(status_code=400, detail="User already exists")

        # Clean the profile data to only include fields that exist in database
        # Note: profile_picture is not in database schema, so we exclude it
        clean_profile_data = {
            "name": profile_data.get("name", ""),
            "email": profile_data.get("email", ""),
            "phone": profile_data.get("phone"),
            "city": profile_data.get("city"),
            "age_group": profile_data.get("age_group"),
            "dietary_restrictions": profile_data.get("dietary_restrictions", []),
            "role": UserRole.USER
        }

        # Remove None values to avoid database issues
        clean_profile_data = {k: v for k, v in clean_profile_data.items() if v is not None}

        print(f"Creating user with data: {clean_profile_data}")

        try:
            created_user = await user_service.create_user(clean_profile_data)
            print(f"User created successfully: {created_user.id}")
        except Exception as e:
            print(f"Error creating user: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Failed to create user: {str(e)}")

        token = create_access_token(
            subject=str(created_user.id),
            role=created_user.role,
            name=created_user.name,
            email=created_user.email,
        )
        return {"access_token": token, "token_type": "bearer"}
    
    async def get_profile(self, user_id: str):
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Convert to dict and add computed fields
        user_dict = {
            'id': str(user.id),
            'name': user.name,
            'email': user.email,
            'phone': user.phone,
            'city': user.city,
            'age_group': user.age_group,
            'dietary_restrictions': user.dietary_restrictions or [],
            'profile_picture': None,  # Not stored in database, will be handled by frontend
            'preferences': getattr(user, 'preferences', {}),  # Handle missing field
            'role': user.role,
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'updated_at': user.updated_at.isoformat() if user.updated_at else None
        }

        # Add computed food personality
        user_dict['food_personality'] = self._determine_food_personality(user.dietary_restrictions)

        print(f"Returning user profile for {user_id}: preferences = {user_dict.get('preferences')}")

        return user_dict

    async def handle_google_login(self, google_user: dict):
        """Handle Google OAuth login"""
        try:
            email = google_user.get("email")

            if not email:
                raise HTTPException(status_code=400, detail="Email is required")

            # Check if user exists
            existing_user = await user_service.get_user_by_email(email)

            if existing_user:
                # User exists, generate token
                token = create_access_token(
                    subject=str(existing_user.id),
                    role=existing_user.role,
                    name=existing_user.name,
                    email=existing_user.email,
                )
                return {
                    "access_token": token,
                    "token_type": "bearer",
                    "user_exists": True
                }
            else:
                # User doesn't exist, return consistent response
                return {
                    "access_token": None,
                    "token_type": None,
                    "user_exists": False,
                    "user_info": google_user
                }

        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))

    async def update_profile(self, user_id: str, profile_data: dict):
        """Update user profile with validation"""
        try:
            # Clean the profile data to only include updatable fields
            # Note: profile_picture excluded as it's not in database schema
            allowed_fields = {
                "name", "phone", "city", "age_group",
                "dietary_restrictions"
            }

            clean_profile_data = {
                k: v for k, v in profile_data.items()
                if k in allowed_fields and v is not None
            }

            if not clean_profile_data:
                raise HTTPException(status_code=400, detail="No valid fields to update")

            updated_user = await user_service.update_user_profile(user_id, clean_profile_data)

            # Return updated user with computed fields
            return await self.get_profile(user_id)

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Failed to update profile: {str(e)}")

    async def save_preferences(self, user_id: str, preferences: dict):
        """Save user preferences to database"""
        try:
            print(f"Saving preferences for user {user_id}: {preferences}")

            # Update user with preferences
            updated_user = await user_service.update_user_profile(user_id, {"preferences": preferences})

            print(f"Preferences saved successfully for user {user_id}")

            # Return updated user profile
            return await self.get_profile(user_id)

        except Exception as e:
            print(f"Error saving preferences for user {user_id}: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Failed to save preferences: {str(e)}")


auth_service = AuthService()
