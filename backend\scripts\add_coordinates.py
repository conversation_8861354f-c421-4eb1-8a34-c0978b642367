#!/usr/bin/env python3
"""
Script to add real coordinates to restaurants in Islamabad
"""

import sys
import os
import random

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.supabase import create_supabase_client

# Real coordinates for popular restaurant areas in Islamabad
RESTAURANT_COORDINATES = {
    # Blue Area restaurants
    "Salt'n Pepper": {"latitude": 33.7077, "longitude": 73.0563},
    "Savour Foods": {"latitude": 33.7089, "longitude": 73.0571},
    "Khoka <PERSON>hola": {"latitude": 33.7065, "longitude": 73.0548},
    "Haleem Ghar": {"latitude": 33.7081, "longitude": 73.0559},
    
    # F-6/F-7 area restaurants
    "Rewayat": {"latitude": 33.7294, "longitude": 73.0654},
    "The Burger Co": {"latitude": 33.7156, "longitude": 73.0498},
    "Howdy": {"latitude": 33.7201, "longitude": 73.0512},
    "Wild Wings": {"latitude": 33.7012, "longitude": 73.0634},  # F-8 Markaz location
    
    # F-8 area restaurants
    "Cheezious": {"latitude": 33.6998, "longitude": 73.0621},
    "Ranchers": {"latitude": 33.7012, "longitude": 73.0634},
    "De Flambe": {"latitude": 33.7023, "longitude": 73.0598},
    
    # I-8 Markaz area
    "Bundu Khan": {"latitude": 33.6654, "longitude": 73.0789},
    "Chaaye Khana": {"latitude": 33.6667, "longitude": 73.0801},
    
    # Other areas
    "El Momento": {"latitude": 33.7134, "longitude": 73.0456},
    "The Carnivore": {"latitude": 33.7245, "longitude": 73.0612},
    "Zamana Restaurant": {"latitude": 33.6987, "longitude": 73.0543},
}

def update_restaurant_coordinates():
    """Update restaurant coordinates in the database"""
    try:
        supabase = create_supabase_client()
        
        # Get all restaurants
        response = supabase.from_('restaurants').select('id, name').execute()
        restaurants = response.data
        
        print(f"Found {len(restaurants)} restaurants in database")
        
        updated_count = 0
        for restaurant in restaurants:
            restaurant_name = restaurant['name']
            restaurant_id = restaurant['id']
            
            # Find matching coordinates
            coords = None
            for name_pattern, coordinates in RESTAURANT_COORDINATES.items():
                if name_pattern.lower() in restaurant_name.lower() or restaurant_name.lower() in name_pattern.lower():
                    coords = coordinates
                    break
            
            if coords:
                # Update restaurant with coordinates
                update_response = supabase.from_('restaurants').update({
                    'latitude': coords['latitude'],
                    'longitude': coords['longitude']
                }).eq('id', restaurant_id).execute()
                
                if update_response.data:
                    print(f"✅ Updated {restaurant_name}: {coords['latitude']}, {coords['longitude']}")
                    updated_count += 1
                else:
                    print(f"❌ Failed to update {restaurant_name}")
            else:
                # Assign random coordinates around Islamabad for restaurants without specific locations
                base_lat = 33.6844  # Islamabad center
                base_lng = 73.0479
                
                # Random offset within ~10km radius
                lat_offset = (random.random() - 0.5) * 0.1  # ~11km per 0.1 degree
                lng_offset = (random.random() - 0.5) * 0.1
                
                random_coords = {
                    'latitude': base_lat + lat_offset,
                    'longitude': base_lng + lng_offset
                }
                
                update_response = supabase.from_('restaurants').update(random_coords).eq('id', restaurant_id).execute()
                
                if update_response.data:
                    print(f"🎲 Assigned random coordinates to {restaurant_name}: {random_coords['latitude']:.4f}, {random_coords['longitude']:.4f}")
                    updated_count += 1
        
        print(f"\n✅ Successfully updated coordinates for {updated_count} restaurants")
        
    except Exception as e:
        print(f"❌ Error updating coordinates: {str(e)}")

if __name__ == "__main__":
    update_restaurant_coordinates()
