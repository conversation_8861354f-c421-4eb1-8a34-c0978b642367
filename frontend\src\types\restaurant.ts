export interface Location {
  city: string;
  area?: string;
  address?: string;
  coordinates?: [number, number]; // [latitude, longitude]
}

export interface MenuItem {
  name: string;
  price: number;
  description?: string;
  category?: string;
  spice_level?: string;
  is_vegetarian?: boolean;
  is_available?: boolean;
  imageUrl?: string; // Added for frontend display
}

export interface Ambiance {
  type?: string;
  seating?: string;
  noise_level?: string;
}

export interface Ratings {
  overall: number;
  food_quality?: number;
  service?: number;
  value?: number;
}

export interface Review {
  user: string;
  rating: number;
  comment: string;
  timestamp?: string;
}

export interface Restaurant {
  id: string;
  name: string;
  location: Location;
  cuisine_types: string[];
  price_range: {
    min: number;
    max: number;
    average: number;
  };
  menu_items: MenuItem[];
  ambiance?: Ambiance;
  ratings: Ratings;
  features?: string[];
  phone?: string;
  website?: string;
  images?: string[]; // Added for frontend display
  created_at?: string;
  updated_at?: string;
}