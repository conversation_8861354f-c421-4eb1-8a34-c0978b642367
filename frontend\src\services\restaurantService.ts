import { IRestaurantCard } from '@/types/chat.types';

export class RestaurantService {
  static formatPrice(price: string | number): string {
    const numPrice = typeof price === 'string' ? parseInt(price, 10) : price;
    return `₨${numPrice.toLocaleString('en-PK')}`;
  }

  static generateDirectionsUrl(address: string): string {
    return `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(address)}`;
  }

  static convertEmbedToMapsUrl(embedUrl: string): string {
    return embedUrl.replace('/embed', '');
  }

  static validateRestaurantCard(restaurant: IRestaurantCard): boolean {
    return !!(
      restaurant.name &&
      restaurant.area &&
      restaurant.item_name &&
      restaurant.price
    );
  }

  static sortRestaurantsByPrice(restaurants: IRestaurantCard[], ascending: boolean = true): IRestaurantCard[] {
    return [...restaurants].sort((a, b) => {
      const priceA = parseInt(a.price.toString(), 10);
      const priceB = parseInt(b.price.toString(), 10);
      return ascending ? priceA - priceB : priceB - priceA;
    });
  }

  static filterRestaurantsByBudget(
    restaurants: IRestaurantCard[],
    minBudget: number,
    maxBudget: number
  ): IRestaurantCard[] {
    return restaurants.filter(restaurant => {
      const price = parseInt(restaurant.price.toString(), 10);
      return price >= minBudget && price <= maxBudget;
    });
  }

  static groupRestaurantsByArea(restaurants: IRestaurantCard[]): Record<string, IRestaurantCard[]> {
    return restaurants.reduce((groups, restaurant) => {
      const area = restaurant.area;
      if (!groups[area]) {
        groups[area] = [];
      }
      groups[area].push(restaurant);
      return groups;
    }, {} as Record<string, IRestaurantCard[]>);
  }

  static extractUniqueAreas(restaurants: IRestaurantCard[]): string[] {
    const areas = restaurants.map(r => r.area);
    return [...new Set(areas)].sort();
  }

  static calculateAveragePrice(restaurants: IRestaurantCard[]): number {
    if (restaurants.length === 0) return 0;
    
    const total = restaurants.reduce((sum, restaurant) => {
      return sum + parseInt(restaurant.price.toString(), 10);
    }, 0);
    
    return Math.round(total / restaurants.length);
  }

  static findCheapestOption(restaurants: IRestaurantCard[]): IRestaurantCard | null {
    if (restaurants.length === 0) return null;
    
    return restaurants.reduce((cheapest, current) => {
      const currentPrice = parseInt(current.price.toString(), 10);
      const cheapestPrice = parseInt(cheapest.price.toString(), 10);
      return currentPrice < cheapestPrice ? current : cheapest;
    });
  }

  static findMostExpensiveOption(restaurants: IRestaurantCard[]): IRestaurantCard | null {
    if (restaurants.length === 0) return null;
    
    return restaurants.reduce((expensive, current) => {
      const currentPrice = parseInt(current.price.toString(), 10);
      const expensivePrice = parseInt(expensive.price.toString(), 10);
      return currentPrice > expensivePrice ? current : expensive;
    });
  }
}
