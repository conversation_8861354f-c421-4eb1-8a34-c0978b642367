-- Create updated schema for latest restaurant data structure

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;

-- Drop existing tables if they exist (for fresh start)
DROP TABLE IF EXISTS menu_items CASCADE;
DROP TABLE IF EXISTS restaurant_branches CASCADE;
DROP TABLE IF EXISTS restaurants CASCADE;
DROP TABLE IF EXISTS user_interactions CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    city VARCHAR(100),
    age_group VARCHAR(50),
    dietary_restrictions TEXT[],
    profile_picture TEXT,
    preferences JSONB,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Restaurants table (main restaurant info)
CREATE TABLE restaurants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    restaurant_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    image_url TEXT,
    cuisine_types TEXT[] NOT NULL,
    
    -- Main branch info (for backward compatibility)
    area VARCHAR(100),
    address TEXT,
    phone VARCHAR(50),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    
    -- Price range
    price_range_min INTEGER,
    price_range_max INTEGER,
    price_range_average INTEGER,
    
    -- Ratings
    rating_overall DECIMAL(3, 2),
    rating_food DECIMAL(3, 2),
    rating_service DECIMAL(3, 2),
    rating_value DECIMAL(3, 2),
    
    -- Ambiance
    ambiance_type VARCHAR(100),
    noise_level VARCHAR(50),
    
    -- Features
    features TEXT[],
    
    -- Store full JSON data for complex queries
    full_data JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Restaurant branches table
CREATE TABLE restaurant_branches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    restaurant_id UUID REFERENCES restaurants(id) ON DELETE CASCADE,
    branch_id VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    area VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    phone VARCHAR(50),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_main_branch BOOLEAN DEFAULT false,
    features TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(restaurant_id, branch_id)
);

-- Menu items table with vector embeddings
CREATE TABLE menu_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    restaurant_id UUID REFERENCES restaurants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price INTEGER NOT NULL,
    category VARCHAR(100),
    spice_level VARCHAR(50),
    is_vegetarian BOOLEAN DEFAULT false,
    is_available BOOLEAN DEFAULT true,
    searchable_text TEXT,
    embedding vector(1536), -- For OpenAI embeddings
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User interactions table
CREATE TABLE user_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    restaurant_id UUID REFERENCES restaurants(id) ON DELETE CASCADE,
    interaction_type VARCHAR(50) NOT NULL, -- 'view', 'like', 'visit', 'rate', 'order'
    query TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_restaurants_city ON restaurants(city);
CREATE INDEX idx_restaurants_cuisine ON restaurants USING GIN(cuisine_types);
CREATE INDEX idx_restaurants_price_range ON restaurants(price_range_min, price_range_max);
CREATE INDEX idx_restaurants_rating ON restaurants(rating_overall);

CREATE INDEX idx_branches_restaurant_id ON restaurant_branches(restaurant_id);
CREATE INDEX idx_branches_area ON restaurant_branches(area);
CREATE INDEX idx_branches_main ON restaurant_branches(is_main_branch);
CREATE INDEX idx_branches_location ON restaurant_branches(latitude, longitude);

CREATE INDEX idx_menu_items_restaurant_id ON menu_items(restaurant_id);
CREATE INDEX idx_menu_items_category ON menu_items(category);
CREATE INDEX idx_menu_items_price ON menu_items(price);
CREATE INDEX idx_menu_items_searchable ON menu_items USING GIN(to_tsvector('english', searchable_text));

-- Vector similarity search index (for semantic search)
CREATE INDEX idx_menu_items_embedding ON menu_items USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX idx_user_interactions_user_id ON user_interactions(user_id);
CREATE INDEX idx_user_interactions_restaurant_id ON user_interactions(restaurant_id);
CREATE INDEX idx_user_interactions_type ON user_interactions(interaction_type);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_restaurants_updated_at BEFORE UPDATE ON restaurants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_branches_updated_at BEFORE UPDATE ON restaurant_branches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_menu_items_updated_at BEFORE UPDATE ON menu_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_interactions ENABLE ROW LEVEL SECURITY;

-- Users can only see/edit their own data
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- Users can only see/edit their own interactions
CREATE POLICY "Users can view own interactions" ON user_interactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own interactions" ON user_interactions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own interactions" ON user_interactions FOR UPDATE USING (auth.uid() = user_id);

-- Public read access for restaurants, branches, and menu items
CREATE POLICY "Public can view restaurants" ON restaurants FOR SELECT TO public USING (true);
CREATE POLICY "Public can view branches" ON restaurant_branches FOR SELECT TO public USING (true);
CREATE POLICY "Public can view menu items" ON menu_items FOR SELECT TO public USING (true);

-- Functions for common queries
CREATE OR REPLACE FUNCTION get_restaurants_with_branches_and_menu(city_param TEXT DEFAULT 'Islamabad')
RETURNS TABLE (
    restaurant_id UUID,
    restaurant_name TEXT,
    cuisine_types TEXT[],
    price_range_min INTEGER,
    price_range_max INTEGER,
    rating_overall DECIMAL,
    image_url TEXT,
    branches JSONB,
    menu_items JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        r.id,
        r.name,
        r.cuisine_types,
        r.price_range_min,
        r.price_range_max,
        r.rating_overall,
        r.image_url,
        COALESCE(
            (SELECT jsonb_agg(
                jsonb_build_object(
                    'branch_id', rb.branch_id,
                    'name', rb.name,
                    'area', rb.area,
                    'address', rb.address,
                    'phone', rb.phone,
                    'coordinates', ARRAY[rb.latitude, rb.longitude],
                    'is_main_branch', rb.is_main_branch,
                    'features', rb.features
                )
            ) FROM restaurant_branches rb WHERE rb.restaurant_id = r.id),
            '[]'::jsonb
        ) as branches,
        COALESCE(
            (SELECT jsonb_agg(
                jsonb_build_object(
                    'name', mi.name,
                    'price', mi.price,
                    'description', mi.description,
                    'category', mi.category
                )
            ) FROM menu_items mi WHERE mi.restaurant_id = r.id),
            '[]'::jsonb
        ) as menu_items
    FROM restaurants r
    WHERE r.city = city_param
    ORDER BY r.rating_overall DESC;
END;
$$ LANGUAGE plpgsql;

-- Function for semantic search (placeholder for vector search)
CREATE OR REPLACE FUNCTION search_menu_items_semantic(
    search_query TEXT,
    city_param TEXT DEFAULT 'Islamabad',
    limit_param INTEGER DEFAULT 10
)
RETURNS TABLE (
    restaurant_name TEXT,
    item_name TEXT,
    item_price INTEGER,
    item_description TEXT,
    item_category TEXT,
    similarity_score REAL
) AS $$
BEGIN
    -- For now, use text search. Later can be enhanced with vector similarity
    RETURN QUERY
    SELECT 
        r.name,
        mi.name,
        mi.price,
        mi.description,
        mi.category,
        ts_rank(to_tsvector('english', mi.searchable_text), plainto_tsquery('english', search_query)) as similarity_score
    FROM menu_items mi
    JOIN restaurants r ON mi.restaurant_id = r.id
    WHERE r.city = city_param
    AND to_tsvector('english', mi.searchable_text) @@ plainto_tsquery('english', search_query)
    ORDER BY similarity_score DESC
    LIMIT limit_param;
END;
$$ LANGUAGE plpgsql;
