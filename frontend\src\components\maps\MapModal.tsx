'use client';

import React, { useState, useEffect } from 'react';
import { LeafletMap } from './LeafletMap';

interface Restaurant {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  rating: number;
  cuisine_types: string[];
  price_range_min: number;
  price_range_max: number;
  phone?: string;
  image_url?: string;
  distance?: number;
}

interface MapModalProps {
  restaurant: Restaurant;
  allRestaurants?: Restaurant[];
  onClose: () => void;
  onRestaurantSelect?: (restaurant: Restaurant) => void;
}

export const MapModal: React.FC<MapModalProps> = ({
  restaurant,
  allRestaurants = [],
  onClose,
  onRestaurantSelect
}) => {
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null);
  const [selectedRestaurant, setSelectedRestaurant] = useState<Restaurant>(restaurant);
  const [isVisible, setIsVisible] = useState(false);
  const [locationPermission, setLocationPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');

  // Show modal after mount
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Get user location
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          setLocationPermission('granted');
        },
        (error) => {
          console.log('Location access denied:', error);
          setLocationPermission('denied');
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    }
  }, []);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  // Prevent body scroll
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => onClose(), 150);
  };

  const handleRestaurantSelect = (selectedRest: Restaurant) => {
    setSelectedRestaurant(selectedRest);
    if (onRestaurantSelect) {
      onRestaurantSelect(selectedRest);
    }
  };

  // Prepare restaurants for map (include main restaurant + nearby ones)
  const mapRestaurants = [
    restaurant,
    ...allRestaurants.filter(r => r.id !== restaurant.id).slice(0, 20)
  ].filter(r => r.latitude && r.longitude);

  // Calculate distances if user location is available
  const restaurantsWithDistance = userLocation ? mapRestaurants.map(r => {
    const distance = calculateDistance(
      userLocation.lat, userLocation.lng,
      r.latitude, r.longitude
    );
    return { ...r, distance };
  }).sort((a, b) => (a.distance || 0) - (b.distance || 0)) : mapRestaurants;

  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  return (
    <div 
      className={`fixed inset-0 bg-black transition-opacity duration-300 z-[9999] flex items-center justify-center p-4 ${
        isVisible ? 'bg-opacity-50' : 'bg-opacity-0'
      }`}
      onClick={handleClose}
    >
      <div 
        className={`bg-white rounded-3xl max-w-7xl w-full max-h-[95vh] overflow-hidden shadow-2xl transform transition-all duration-300 ${
          isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-red-500 via-orange-500 to-yellow-500 text-white p-6 relative">
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-3 transition-all duration-200 hover:scale-110"
          >
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          <div className="pr-16">
            <h2 className="text-3xl font-bold mb-3">{selectedRestaurant.name}</h2>
            <div className="flex items-center gap-6 text-lg opacity-90">
              <span>⭐ {selectedRestaurant.rating}/5</span>
              <span>📍 {selectedRestaurant.address}</span>
              {selectedRestaurant.distance && (
                <span>🚗 {selectedRestaurant.distance.toFixed(1)} km away</span>
              )}
              {selectedRestaurant.phone && (
                <span>📞 {selectedRestaurant.phone}</span>
              )}
            </div>
          </div>
        </div>

        {/* Location Permission Banner */}
        {locationPermission === 'denied' && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-yellow-400 text-xl">⚠️</span>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>Location access denied.</strong> Enable location services to see distances and get personalized recommendations.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Map Container */}
        <div className="flex-1 relative" style={{ height: 'calc(95vh - 200px)' }}>
          <LeafletMap
            restaurants={restaurantsWithDistance}
            userLocation={userLocation || undefined}
            selectedRestaurant={selectedRestaurant}
            onRestaurantSelect={handleRestaurantSelect}
            height="100%"
            showUserLocation={true}
            maxDistance={15}
            className="w-full h-full"
          />
        </div>

        {/* Bottom Info Panel */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 p-6 border-t">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div>
                <h3 className="text-xl font-bold text-gray-900">{selectedRestaurant.name}</h3>
                <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                  <span>{selectedRestaurant.cuisine_types.join(', ')}</span>
                  <span>•</span>
                  <span>Rs {selectedRestaurant.price_range_min} - Rs {selectedRestaurant.price_range_max}</span>
                  {selectedRestaurant.distance && (
                    <>
                      <span>•</span>
                      <span className="text-green-600 font-medium">
                        {selectedRestaurant.distance.toFixed(1)} km away
                      </span>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => {
                  const url = `https://www.google.com/maps/dir/?api=1&destination=${selectedRestaurant.latitude},${selectedRestaurant.longitude}`;
                  window.open(url, '_blank');
                }}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 flex items-center gap-2"
              >
                <span>🧭</span> Get Directions
              </button>
              
              {selectedRestaurant.phone && (
                <button
                  onClick={() => window.open(`tel:${selectedRestaurant.phone}`, '_self')}
                  className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 flex items-center gap-2"
                >
                  <span>📞</span> Call Now
                </button>
              )}

              <button
                onClick={() => {
                  if (onRestaurantSelect) {
                    onRestaurantSelect(selectedRestaurant);
                  }
                  handleClose();
                }}
                className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 flex items-center gap-2"
              >
                <span>📋</span> View Menu
              </button>
            </div>
          </div>

          {/* Quick Stats */}
          {userLocation && restaurantsWithDistance.length > 1 && (
            <div className="mt-4 pt-4 border-t border-orange-200">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {restaurantsWithDistance.filter(r => r.distance && r.distance <= 5).length}
                  </div>
                  <div className="text-sm text-gray-600">Within 5km</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {restaurantsWithDistance.filter(r => r.distance && r.distance <= 10).length}
                  </div>
                  <div className="text-sm text-gray-600">Within 10km</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {restaurantsWithDistance[0]?.distance?.toFixed(1) || 'N/A'} km
                  </div>
                  <div className="text-sm text-gray-600">Closest Restaurant</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
